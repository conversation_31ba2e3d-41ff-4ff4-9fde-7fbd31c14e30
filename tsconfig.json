{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/auth": ["libs/auth/src"], "@app/auth/*": ["libs/auth/src/*"], "@app/aws": ["libs/aws/src"], "@app/aws/*": ["libs/aws/src/*"], "@app/notification": ["libs/notification/src"], "@app/notification/*": ["libs/notification/src/*"], "@app/cache": ["libs/cache/src"], "@app/cache/*": ["libs/cache/src/*"], "@app/mail": ["libs/mail/src"], "@app/mail/*": ["libs/mail/src/*"], "@app/order": ["libs/order/src"], "@app/order/*": ["libs/order/src/*"], "@app/payment": ["libs/payment/src"], "@app/payment/*": ["libs/payment/src/*"], "@app/store": ["libs/store/src"], "@app/store/*": ["libs/store/src/*"], "@app/shared": ["libs/shared/src"], "@app/shared/*": ["libs/shared/src/*"], "@app/user": ["libs/user/src"], "@app/user/*": ["libs/user/src/*"], "@app/system": ["libs/system/src"], "@app/system/*": ["libs/system/src/*"]}}}