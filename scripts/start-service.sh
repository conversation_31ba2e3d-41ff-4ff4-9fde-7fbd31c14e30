#!/bin/bash

cleanup() {
  echo "Caught SIGTERM, cleaning up..."
  # You can add custom cleanup logic here if needed
  exit 0
}

trap cleanup SIGTERM

APPS_DIR="$(dirname "$0")/../apps"

get_services() {
  find "$APPS_DIR" -maxdepth 1 -type d -exec basename {} \; | tail -n +2
}

SERVICES=$(get_services)

start_service() {
  local service=$1
  local env=$2

  # Check if the service exists in the SERVICES array
  local service_exists=false
  for s in ${SERVICES}; do
    if [[ "$s" == "$service" ]]; then
      service_exists=true
      break
    fi
  done

  if [[ "$service_exists" == false ]]; then
    echo "Invalid service. Choose from: ${SERVICES}"
    exit 1
  fi

  local command
  if [[ "$env" == "dev" ]]; then
    echo "Launching app..."
    exec yarn nest start --watch ${service}
  else
    # Migration
    echo "Running migrations..."
    yarn typeorm migration:run -d dist/apps/${service}/apps/${service}/src/datasource.js

    echo "Launching app..."
    exec node dist/apps/${service}/apps/${service}/src/main.js
  fi
}

SERVICE=$1
ENV=$2

if [[ -z "$SERVICE" ]]; then
  echo "Usage: scripts/start-service.sh <service> <env>"
  echo "Example: scripts/start-service.sh api dev"
  exit 1
fi

start_service "$SERVICE" "$ENV"