#!/bin/bash

echo "🚀 Starting E2E tests for CI/CD environment..."

# Stop and remove existing containers if they exist
echo "🧹 Cleaning up existing containers..."
docker-compose down -v 2>/dev/null || true

# Start all required services using docker-compose with test env
echo "🐳 Starting services with test configuration..."
docker-compose --env-file env.test up -d postgres redis api

# Wait for PostgreSQL to be ready
echo "🔍 Checking PostgreSQL connection..."
until docker exec backend_laundry_postgres pg_isready -U postgres 2>/dev/null; do
    echo "⏳ PostgreSQL is unavailable - sleeping"
    sleep 3
done
echo "✅ PostgreSQL is ready!"

# Wait for Redis to be ready
echo "🔍 Checking Redis connection..."
until docker exec backend_laundry_redis redis-cli ping 2>/dev/null | grep -q "PONG"; do
    echo "⏳ Redis is unavailable - sleeping"
    sleep 3
done
echo "✅ Redis is ready!"

# Wait for API to be ready and database connection to be established
echo "🔍 Checking API database connection..."
until docker exec backend_laundry_api yarn typeorm query "SELECT 1" -d dist/apps/api/apps/api/src/datasource.js 2>/dev/null; do
    echo "⏳ API database connection is unavailable - sleeping"
    sleep 3
done
echo "✅ API database connection is ready!"

# Clear test database before running tests
echo "🧹 Clearing test database..."
docker exec backend_laundry_api yarn typeorm schema:drop -d dist/apps/api/apps/api/src/datasource.js --force

# Run migrations for test database
echo "🔄 Running test migrations..."
docker exec backend_laundry_api yarn typeorm migration:run -d dist/apps/api/apps/api/src/datasource.js

# Run E2E tests
echo "🧪 Running E2E tests..."
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e

# Clean up after tests
echo "🧹 Cleaning up after tests..."
docker-compose down -v

echo "✅ E2E tests completed!" 