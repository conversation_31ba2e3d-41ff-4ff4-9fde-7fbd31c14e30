import { IsEmpty, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserDto } from '@app/user/dto';
import { Exclude, Expose, Type } from 'class-transformer';

@Exclude()
export class RetrieveAuthDto {
  @Expose()
  @Type(() => UserDto)
  user: UserDto;

  @Expose()
  @IsNotEmpty()
  @ApiProperty()
  accessToken: string;

  @Expose()
  @ApiProperty()
  @IsEmpty()
  refreshToken?: string;

  @Expose()
  @ApiProperty()
  accessTokenExpiresIn: number;

  @Expose()
  @ApiProperty()
  refreshTokenExpiresIn?: number;

  @Expose()
  @ApiProperty()
  @IsEmpty()
  redirectTo?: string;
}
