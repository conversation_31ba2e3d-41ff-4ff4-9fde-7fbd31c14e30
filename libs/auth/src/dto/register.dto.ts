import {
  USER_LANGUAGE,
  UserGender,
} from '@app/shared/database/entities/user.entity';
import {
  IsDateString,
  IsEmail,
  IsEmpty,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Matches,
  MaxLength,
  <PERSON>Length,
  ValidateIf,
} from 'class-validator';
import { isEmpty } from 'lodash';
import { IsPhoneNumber } from '@app/shared/decorators/is-phone-number.decorator';
import { IsEmailValidation } from '@app/shared/decorators/email-validation.decorator';

export class RegisterDto {
  @IsNotEmpty()
  @IsEmail({
    ignore_max_length: true,
  })
  @IsEmailValidation() // Uses environment config BLOCK_EMAIL_PLUS_SYMBOL
  @MaxLength(255)
  email: string;

  @IsNotEmpty()
  @IsPhoneNumber()
  phone: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  name: string;

  @IsNotEmpty()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!\"#\$%&'()\^@\[\];:,\.\/=~\|`{\+\*}<>?_\-\"ー一−‐―-])\S+$/,
  )
  @MinLength(8)
  @MaxLength(128)
  password: string;

  @IsNotEmpty()
  @IsDateString()
  birthday: Date;

  @IsOptional()
  @IsUrl()
  @ValidateIf((values) => !isEmpty(values.avatar))
  avatar?: string;

  @IsString()
  @IsOptional()
  @IsEnum(USER_LANGUAGE)
  language?: string;

  @IsOptional()
  @IsEnum(UserGender)
  gender?: UserGender;
}
