import { PassportModule } from '@nestjs/passport';
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtModule } from '@nestjs/jwt';
import { AwsModule } from '@app/aws/aws.module';
import { HttpModule } from '@nestjs/axios';
import { CacheModule } from '@app/cache';

@Module({
  imports: [
    AwsModule,
    CacheModule,
    PassportModule.register({
      session: true,
    }),
    JwtModule.register({}),
    HttpModule,
  ],
  providers: [AuthService],
  exports: [AuthService],
})
export class AuthModule {}
