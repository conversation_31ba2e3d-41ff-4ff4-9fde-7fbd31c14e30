import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TokenPayload } from './interfaces/token.interface';
import { JwtService } from '@nestjs/jwt';
import { isEmpty } from 'lodash';
import { LineVerificationDto } from './dto/line-verification.dto';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { ExtractJwt } from 'passport-jwt';
import { Request } from 'express';
import { AppLogger } from '@app/shared/logger/app.logger';
import { GoogleVerificationDto } from './dto/google-verification.dto';
import { FacebookVerificationDto } from './dto/facebook-verification.dto';
import { ErrorResponse } from '@app/shared/types';
import * as jwt from 'jsonwebtoken';
import { JwksClient } from 'jwks-rsa';
import { RedisClientService } from '@app/cache/services/redis-client.service';

@Injectable()
export class AuthService {
  private readonly jwksClient: JwksClient;
  private readonly BLACKLIST_PREFIX = 'blacklist:access_token:';
  private readonly REFRESH_BLACKLIST_PREFIX = 'blacklist:refresh_token:';

  constructor(
    private readonly logger: AppLogger,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    private readonly httpService: HttpService,
    private readonly redisClientService: RedisClientService,
  ) {
    this.jwksClient = new JwksClient({
      jwksUri: 'https://www.facebook.com/.well-known/oauth/openid/jwks',
    });
  }

  signIn(userId: string): {
    accessToken: string;
    refreshToken: string;
    accessTokenCookie: string;
    refreshTokenCookie: string;
    accessTokenExpiresIn: number;
    refreshTokenExpiresIn: number;
  } {
    try {
      const accessToken: string = this.generateAccessToken({
        userId,
      });
      const refreshToken: string = this.generateRefreshToken({
        userId,
      });
      const { accessTokenCookie, refreshTokenCookie } = this.generateAuthCookie(
        accessToken,
        refreshToken,
      );

      return {
        accessToken,
        refreshToken,
        accessTokenCookie,
        refreshTokenCookie,
        accessTokenExpiresIn: this.configService.get(
          'JWT.ACCESS_TOKEN_EXPIRATION_TIME',
        ),
        refreshTokenExpiresIn: this.configService.get(
          'JWT.REFRESH_TOKEN_EXPIRATION_TIME',
        ),
      };
    } catch (error) {
      throw error;
    }
  }

  async logout(
    accessToken?: string,
    refreshToken?: string,
  ): Promise<{
    accessTokenCookie: string;
    refreshTokenCookie: string;
  }> {
    const { accessTokenCookie, refreshTokenCookie } = this.generateAuthCookie();

    // Add access token to blacklist if provided
    if (accessToken) {
      await this.addToBlacklist(accessToken);
    }

    // Add refresh token to blacklist if provided
    if (refreshToken) {
      await this.addRefreshTokenToBlacklist(refreshToken);
    }

    return {
      accessTokenCookie,
      refreshTokenCookie,
    };
  }

  /**
   * Add access token to blacklist
   * @param token - The access token to blacklist
   */
  async addToBlacklist(token: string): Promise<void> {
    try {
      const payload = this.getPayloadFromAccessToken(token);
      const expiresIn = payload.exp
        ? payload.exp - Math.floor(Date.now() / 1000)
        : this.configService.get('JWT.ACCESS_TOKEN_EXPIRATION_TIME');

      const blacklistKey = `${this.BLACKLIST_PREFIX}${token}`;
      await this.redisClientService.set(blacklistKey, 'blacklisted', {
        EX: expiresIn,
      });

      this.logger.debug(`Access token added to blacklist: ${blacklistKey}`);
    } catch (error) {
      this.logger.error('Error adding access token to blacklist', error);
      throw error;
    }
  }

  /**
   * Add refresh token to blacklist
   * @param token - The refresh token to blacklist
   */
  async addRefreshTokenToBlacklist(token: string): Promise<void> {
    try {
      const payload = this.getPayloadFromRefreshToken(token);
      const expiresIn = payload.exp
        ? payload.exp - Math.floor(Date.now() / 1000)
        : this.configService.get('JWT.REFRESH_TOKEN_EXPIRATION_TIME');

      const blacklistKey = `${this.REFRESH_BLACKLIST_PREFIX}${token}`;
      await this.redisClientService.set(blacklistKey, 'blacklisted', {
        EX: expiresIn,
      });

      this.logger.debug(`Refresh token added to blacklist: ${blacklistKey}`);
    } catch (error) {
      this.logger.error('Error adding refresh token to blacklist', error);
      throw error;
    }
  }

  /**
   * Check if access token is blacklisted
   * @param token - The access token to check
   * @returns true if token is blacklisted, false otherwise
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistKey = `${this.BLACKLIST_PREFIX}${token}`;
      const blacklisted = await this.redisClientService.get(blacklistKey);
      return blacklisted === 'blacklisted';
    } catch (error) {
      this.logger.error('Error checking access token blacklist', error);
      return false; // If Redis is down, allow the token to pass
    }
  }

  /**
   * Check if refresh token is blacklisted
   * @param token - The refresh token to check
   * @returns true if token is blacklisted, false otherwise
   */
  async isRefreshTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistKey = `${this.REFRESH_BLACKLIST_PREFIX}${token}`;
      const blacklisted = await this.redisClientService.get(blacklistKey);
      return blacklisted === 'blacklisted';
    } catch (error) {
      this.logger.error('Error checking refresh token blacklist', error);
      return false; // If Redis is down, allow the token to pass
    }
  }

  /**
   * Remove access token from blacklist (useful for testing or admin operations)
   * @param token - The access token to remove from blacklist
   */
  async removeFromBlacklist(token: string): Promise<void> {
    try {
      const blacklistKey = `${this.BLACKLIST_PREFIX}${token}`;
      await this.redisClientService.del(blacklistKey);
      this.logger.debug(`Access token removed from blacklist: ${blacklistKey}`);
    } catch (error) {
      this.logger.error('Error removing access token from blacklist', error);
      throw error;
    }
  }

  /**
   * Remove refresh token from blacklist (useful for testing or admin operations)
   * @param token - The refresh token to remove from blacklist
   */
  async removeRefreshTokenFromBlacklist(token: string): Promise<void> {
    try {
      const blacklistKey = `${this.REFRESH_BLACKLIST_PREFIX}${token}`;
      await this.redisClientService.del(blacklistKey);
      this.logger.debug(
        `Refresh token removed from blacklist: ${blacklistKey}`,
      );
    } catch (error) {
      this.logger.error('Error removing refresh token from blacklist', error);
      throw error;
    }
  }

  generateAccessToken(
    payload: TokenPayload,
    expiresIn: string = `${this.configService.get<string>(
      'JWT.ACCESS_TOKEN_EXPIRATION_TIME',
    )}s`,
  ) {
    return this.jwtService.sign(payload, {
      secret: this.configService.get('JWT.ACCESS_TOKEN_SECRET'),
      expiresIn: expiresIn,
    });
  }

  generateRefreshToken(
    payload: TokenPayload,
    expiresIn: string = `${this.configService.get<string>(
      'JWT.REFRESH_TOKEN_EXPIRATION_TIME',
    )}s`,
  ) {
    return this.jwtService.sign(payload, {
      secret: this.configService.get('JWT.REFRESH_TOKEN_SECRET'),
      expiresIn: expiresIn,
    });
  }

  generateAuthCookie(
    accessToken: string = '',
    refreshToken: string = '',
  ): {
    accessTokenCookie: string;
    refreshTokenCookie: string;
  } {
    const hasCorsLocal = (this.configService.get('CORS') ?? []).some((v) => {
      return v.indexOf('localhost') >= 0;
    });
    const accessTokenCookie = `${this.configService.get(
      'JWT.ACCESS_COOKIES_NAME',
    )}=${accessToken}; HttpOnly; Path=/; ${
      hasCorsLocal ? 'SameSite=None;' : 'SameSite=Lax;'
    } Secure; Domain=${this.configService.get(
      'COOKIE_DOMAIN',
    )}; Max-Age=${this.configService.get('JWT.ACCESS_TOKEN_EXPIRATION_TIME')}`;
    const refreshTokenCookie = `${this.configService.get(
      'JWT.REFRESH_COOKIES_NAME',
    )}=${refreshToken}; HttpOnly; Path=/; ${
      hasCorsLocal ? 'SameSite=None;' : 'SameSite=Lax;'
    } Secure; Domain=${this.configService.get(
      'COOKIE_DOMAIN',
    )}; Max-Age=${this.configService.get('JWT.REFRESH_TOKEN_EXPIRATION_TIME')}`;

    return {
      accessTokenCookie,
      refreshTokenCookie,
    };
  }

  async verifyLineMember(input: LineVerificationDto): Promise<{
    sub: string;
    userId: string;
    name: string;
    displayName: string;
    picture: string;
    pictureUrl: string;
  }> {
    let verifyResponse;
    try {
      const response = this.httpService.get(
        'https://api.line.me/oauth2/v2.1/verify',
        {
          params: {
            access_token: input.accessToken,
          },
        },
      );

      verifyResponse = await lastValueFrom(response);
      if (verifyResponse.status !== HttpStatus.OK) {
        throw new BadRequestException({
          code: 'INVALID_SOCIAL_PROVIDER',
        } as ErrorResponse);
      }
    } catch (exception) {
      this.logger.error(exception);
      throw new BadRequestException({
        code: 'INVALID_SOCIAL_PROVIDER',
      } as ErrorResponse);
    }

    const memberData = verifyResponse?.data;
    if (!memberData) {
      throw new BadRequestException({
        code: 'INVALID_SOCIAL_PROVIDER',
      } as ErrorResponse);
    }

    return memberData;
  }

  async verifyGoogleMember(input: GoogleVerificationDto): Promise<{
    sub: string;
    email: string;
    name: string;
    picture: string;
  }> {
    let verifyResponse;
    try {
      const response = this.httpService.get(
        'https://openidconnect.googleapis.com/v1/userinfo',
        {
          headers: {
            Authorization: `Bearer ${input.accessToken}`,
          },
        },
      );

      verifyResponse = await lastValueFrom(response);
      if (verifyResponse.status !== HttpStatus.OK) {
        throw new BadRequestException({
          code: 'INVALID_SOCIAL_PROVIDER',
        } as ErrorResponse);
      }
    } catch (exception) {
      this.logger.error(exception);
      throw new BadRequestException({
        code: 'INVALID_SOCIAL_PROVIDER',
      } as ErrorResponse);
    }

    const memberData = verifyResponse?.data;
    if (!memberData) {
      throw new BadRequestException({
        code: 'INVALID_SOCIAL_PROVIDER',
      } as ErrorResponse);
    }

    return memberData;
  }

  async verifyFacebookMember(input: FacebookVerificationDto): Promise<{
    id: string;
    name: string;
    picture: string;
  }> {
    let verifyResponse;

    // Try standard Facebook Graph API login first
    try {
      verifyResponse = await this.getStandardFacebookUser(input.accessToken);

      if (verifyResponse.status !== HttpStatus.OK) {
        throw new BadRequestException({
          code: 'INVALID_SOCIAL_PROVIDER',
        } as ErrorResponse);
      }

      const memberData = verifyResponse?.data;
      if (!memberData) {
        throw new BadRequestException({
          code: 'INVALID_SOCIAL_PROVIDER',
        } as ErrorResponse);
      }

      // Return standard Graph API response
      return memberData;
    } catch (standardLoginError) {
      this.logger.warn(
        'Standard Facebook login failed. Attempting limited login.',
        {
          error: standardLoginError?.message || standardLoginError,
        },
      );

      // Fallback to limited login (JWT-based) for iOS
      try {
        const facebookDetails = await this.verifyFacebookJwt(input.accessToken);

        if (!facebookDetails.id) {
          throw new BadRequestException({
            code: 'INVALID_SOCIAL_PROVIDER',
          } as ErrorResponse);
        }

        // Return limited login response
        return facebookDetails;
      } catch (limitedLoginError) {
        this.logger.error('Limited Facebook login also failed.', {
          error: limitedLoginError?.message || limitedLoginError,
        });
        throw new BadRequestException({
          code: 'INVALID_SOCIAL_PROVIDER',
        } as ErrorResponse);
      }
    }
  }

  getJwtAccessTokenFromExtractors() {
    return [
      (request: Request) => {
        return request?.cookies?.[
          this.configService.get('JWT.ACCESS_COOKIES_NAME')
        ];
      },
      ExtractJwt.fromAuthHeaderAsBearerToken(),
    ];
  }

  getJwtRefreshTokenFromExtractors() {
    return [
      (request: Request) => {
        return request?.cookies?.[
          this.configService.get('JWT.REFRESH_COOKIES_NAME')
        ];
      },
      ExtractJwt.fromAuthHeaderAsBearerToken(),
    ];
  }

  getJwtAccessTokenFromRequest(req: Request): string | null {
    return this.getJwtTokenFromRequest(
      req,
      this.getJwtAccessTokenFromExtractors(),
    );
  }

  getJwtRefreshTokenFromRequest(req: Request): string | null {
    return this.getJwtTokenFromRequest(
      req,
      this.getJwtRefreshTokenFromExtractors(),
    );
  }

  protected getJwtTokenFromRequest(
    req: Request,
    extractors: Array<(req: Request) => string | null>,
  ): string | null {
    let token = null;
    for (const extractor of extractors) {
      token = extractor(req);
      if (token) {
        break;
      }
    }
    return token;
  }

  getPayloadFromAccessToken(token: string): TokenPayload {
    return this.jwtService.verify(token, {
      secret: this.configService.get('JWT.ACCESS_TOKEN_SECRET'),
    });
  }

  getPayloadFromRefreshToken(token: string): TokenPayload {
    return this.jwtService.verify(token, {
      secret: this.configService.get('JWT.REFRESH_TOKEN_SECRET'),
    });
  }

  getUserIdFromAccessToken(token: string): string {
    const payload = this.getPayloadFromAccessToken(token);
    return payload.userId ?? '';
  }

  async getStandardFacebookUser(token: string) {
    const response = this.httpService.get(
      `https://graph.facebook.com/me?fields=id,name,picture&access_token=${token}`,
    );
    return await lastValueFrom(response);
  }

  async verifyFacebookJwt(token: string): Promise<{
    id: string;
    name: string;
    picture: string;
  }> {
    return new Promise((resolve, reject) => {
      const verifyOptions: any = {
        issuer: 'https://www.facebook.com',
        algorithms: ['RS256'],
        audience: this.configService.get('SOCIAL.FACEBOOK.clientID'),
      };

      jwt.verify(
        token,
        async (header, callback) => {
          try {
            const key = await this.jwksClient.getSigningKey(header.kid);
            callback(null, key.getPublicKey());
          } catch (error) {
            callback(error as Error, undefined);
          }
        },
        verifyOptions,
        (err, decoded: any) => {
          this.logger.debug('JWT Verification Result:', {
            err: err?.message,
            decoded: decoded ? 'success' : 'failed',
          });
          if (err) {
            this.logger.error('JWT Verification Error:', err);
            reject(
              new BadRequestException({
                code: 'INVALID_SOCIAL_PROVIDER',
              } as ErrorResponse),
            );
            return;
          }

          // Extract user information from the JWT payload
          const userData = {
            id: decoded.sub || decoded.user_id,
            name: decoded.name || '',
            picture: decoded.picture || '',
          };

          if (!userData.id) {
            reject(
              new BadRequestException({
                code: 'INVALID_SOCIAL_PROVIDER',
              } as ErrorResponse),
            );
            return;
          }

          resolve(userData);
        },
      );
    });
  }
}
