// Order status enum
export enum OrderStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

// Order types enum
export enum OrderType {
  LAUNDRY = 'LAUNDRY',
  DRY_CLEAN = 'DRY_CLEAN',
  IRON = 'IRON',
  OTHER = 'OTHER',
}

// Order priority enum
export enum OrderPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export const ORDER_EVENTS = {
  CREATED: 'order.created',
  STATUS_CHANGED: 'order.status.changed',
  PAID: 'order.paid',
  COMPLETED: 'order.completed',
  CANCELLED: 'order.cancelled',
} as const;

export type OrderEventType = (typeof ORDER_EVENTS)[keyof typeof ORDER_EVENTS];

// Order sort fields enum
export enum OrderSortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  TOTAL_AMOUNT = 'totalAmount',
  STATUS = 'status',
  PAYMENT_METHOD = 'paymentMethod',
  USER_NAME = 'user.name',
  CODE = 'code',
  TOTAL_POINTS = 'totalPoints',
  USED_POINTS = 'usedPoints',
  BONUS_POINTS = 'bonusPoints',
}

// Order sort field mapping for database queries
export const ORDER_SORT_FIELD_MAP: Record<string, string> = {
  [OrderSortField.USER_NAME]: 'user.name',
  [OrderSortField.CREATED_AT]: 'order.createdAt',
  [OrderSortField.UPDATED_AT]: 'order.updatedAt',
  [OrderSortField.TOTAL_AMOUNT]: 'order.totalAmount',
  [OrderSortField.STATUS]: 'order.status',
  [OrderSortField.PAYMENT_METHOD]: 'order.paymentMethod',
  [OrderSortField.CODE]: 'order.code',
  [OrderSortField.TOTAL_POINTS]: 'order.totalPoints',
  [OrderSortField.USED_POINTS]: 'order.usedPoints',
  [OrderSortField.BONUS_POINTS]: 'order.bonusPoints',
};
