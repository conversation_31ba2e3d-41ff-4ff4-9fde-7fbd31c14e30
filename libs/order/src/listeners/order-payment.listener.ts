import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PAYMENT_EVENTS } from '@app/payment/constants';
import { PaymentBalanceSuccessEvent } from '@app/payment/events/payment.events';
import { OrderService } from '../order.service';
import { CatchEventError } from '@app/shared/decorators/catch-event-error.decorator';
import { PaymentTransactionService } from '@app/payment/services/payment-transaction.service';
import { PaymentBalanceService } from '@app/payment/services/payment-balance.service';
import { CreatePaymentTransactionDto } from '@app/payment/dto/create-payment-transaction.dto';
import {
  PaymentTransactionType,
  PaymentTransactionStatus,
} from '@app/shared/database/entities';
import { CURRENCY_TYPE } from '@app/payment/constants';

@Injectable()
export class OrderPaymentListener {
  private readonly logger = new Logger(OrderPaymentListener.name);

  constructor(
    private readonly orderService: OrderService,
    private readonly paymentTransactionService: PaymentTransactionService,
    private readonly paymentBalanceService: PaymentBalanceService,
  ) {}

  @OnEvent(PAYMENT_EVENTS.BALANCE_SUCCESS)
  @CatchEventError()
  async handlePaymentBalanceSuccess(event: PaymentBalanceSuccessEvent) {
    // Validate event data
    if (!event.orderId || !event.transactionId || !event.userId) {
      this.logger.error(
        `Invalid payment balance success event data: ${JSON.stringify(event)}`,
      );
      return;
    }

    this.logger.log(
      `Payment balance success for order: ${event.orderId} with transaction: ${event.transactionId}`,
    );

    // Update order payment status (only after balance operations are successful)
    await this.orderService.handlePaymentSuccess(
      event.orderId,
      event.transactionId,
      event.amount,
    );

    this.logger.log(`Order status updated to PAID for order: ${event.orderId}`);

    // Process cashback when order is paid
    // TODO: Comment out for now
    // await this.processCashback(event);
  }

  /**
   * Process cashback for paid order
   */
  private async processCashback(
    event: PaymentBalanceSuccessEvent,
  ): Promise<void> {
    // Calculate cashback amount
    const cashbackAmount = this.calculateCashbackAmount(event.amount);

    if (cashbackAmount > 0) {
      // Calculate cashback points
      const cashbackPoints =
        this.paymentBalanceService.calculatePointsFromAmount(
          cashbackAmount,
          1000.0, // Default exchange rate
          event.currency as CURRENCY_TYPE,
        );

      // Create cashback payment transaction
      const createTransactionDto: CreatePaymentTransactionDto = {
        userId: event.userId,
        orderId: event.orderId,
        type: PaymentTransactionType.CASHBACK,
        amount: cashbackAmount,
        point: cashbackPoints,
        exchangeRate: 1000.0,
        currency: event.currency,
        paymentMethod: '',
        status: PaymentTransactionStatus.SUCCESS,
        description: 'Cashback for paid order',
      };

      // Save cashback transaction to database
      const cashbackTransaction =
        await this.paymentTransactionService.createPaymentTransaction(
          createTransactionDto,
        );

      // Process cashback balance update
      await this.paymentBalanceService.addUserBalanceWithTransaction(
        event.userId,
        cashbackPoints,
        cashbackTransaction.id,
        'Cashback points added for order payment',
      );

      this.logger.log(
        `Cashback processed for order: ${event.orderId}, Amount: ${cashbackAmount} ${event.currency}, Points: ${cashbackPoints}`,
      );
    } else {
      this.logger.log(
        `No cashback for order: ${event.orderId}, Amount: ${event.amount} ${event.currency} (below threshold)`,
      );
    }
  }

  /**
   * Calculate cashback amount based on order amount
   * @param orderAmount Order amount in VND
   * @returns Cashback amount in VND
   */
  private calculateCashbackAmount(orderAmount: number): number {
    // Cashback 1,000 VND for orders >= 50,000 VND
    if (orderAmount >= 50000) {
      return 1000;
    }
    return 0;
  }
}
