import {
  Inject,
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import {
  CreateOrderDto,
  UpdateOrderDto,
  SearchOrdersDto,
  OrderResponseDto,
} from './dto';
import {
  Order,
  OrderStatus,
  Product,
  OrderItem,
  PaymentTransaction,
  MachineProgram,
  WashingMachine,
  Store,
  User,
  PaymentTransactionType,
  PaymentTransactionStatus,
} from '@app/shared/database/entities';
import { CURRENCY_TYPE, EXCHANGE_RATES } from '@app/payment/constants';
import { OrderRepositoryInterface } from '@app/shared/database';
import { ProductRepositoryInterface } from '@app/shared/database/repositories/product.interface.repository';
import {
  OrderItemRepositoryInterface,
  PaymentTransactionRepositoryInterface,
  SystemConfigurationRepositoryInterface,
  UserRepositoryInterface,
} from '@app/shared/database/repositories';
import { In, Between, LessThan } from 'typeorm';
import { ErrorResponse } from '@app/shared/types/error.type';
import { PaginationResponse } from '@app/shared/types/common.type';
import { OrderItemService } from './services/order-item.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  ORDER_EVENTS,
  ORDER_SORT_FIELD_MAP,
  OrderSortField,
} from './constants';
import {
  OrderCreatedEvent,
  OrderStatusChangedEvent,
  OrderPaidEvent,
  OrderCompletedEvent,
  OrderCancelledEvent,
} from './events/order.events';
import { map, uniq, first, sumBy } from 'lodash';
import { plainToInstance } from 'class-transformer';
import { PaymentBalanceService, PaymentTransactionService } from '@app/payment';
import { PaymentTransactionDto } from '@app/payment/dto/payment-transaction.dto';
import { UserDto } from '@app/user/dto';
import { BONUS_POINTS_RATIO } from '@app/shared/constants';
import { SYSTEM_SETTING_KEY } from '@app/system/const/system.constant';
import { applySorting } from '@app/shared/helpers';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    @Inject('OrderRepositoryInterface')
    private readonly orderRepository: OrderRepositoryInterface,
    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,
    @Inject('OrderItemRepositoryInterface')
    private readonly orderItemRepository: OrderItemRepositoryInterface,
    @Inject('PaymentTransactionRepositoryInterface')
    private readonly paymentTransactionRepository: PaymentTransactionRepositoryInterface,
    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,
    @Inject('SystemConfigurationRepositoryInterface')
    private readonly systemConfigurationRepository: SystemConfigurationRepositoryInterface,
    private readonly orderItemService: OrderItemService,
    private readonly paymentBalanceService: PaymentBalanceService,
    private readonly paymentTransactionService: PaymentTransactionService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async createOrder(createOrderDto: CreateOrderDto): Promise<Order> {
    // Validate products exist
    const productIds = createOrderDto.items.map((item) => item.productId);
    const products = await this.productRepository.findBy({
      id: In(productIds),
    });

    if (products.length !== productIds.length) {
      const foundProductIds = products.map((p) => p.id);
      const missingProductIds = productIds.filter(
        (id) => !foundProductIds.includes(id),
      );
      throw new NotFoundException({
        code: 'PRODUCT_NOT_FOUND',
        data: { productIds: missingProductIds },
      } as ErrorResponse);
    }

    const paymentUser = await this.paymentBalanceService.getOrCreatePaymentUser(
      createOrderDto.userId,
    );

    if (paymentUser.totalPoints < createOrderDto.usedPoints) {
      throw new BadRequestException({
        code: 'ORDER_INSUFFICIENT_POINTS',
        data: { userId: createOrderDto.userId },
      } as ErrorResponse);
    }

    // Calculate total points and amount
    let totalPoints = 0;
    for (const item of createOrderDto.items) {
      const product = products.find((p) => p.id === item.productId);
      if (!product) {
        throw new NotFoundException({
          code: 'PRODUCT_NOT_FOUND',
          data: { productId: item.productId },
        } as ErrorResponse);
      }
      totalPoints += (product.pricePoints || 0) * item.quantity;
    }

    const currency = createOrderDto.currency || CURRENCY_TYPE.VND;
    const exchangeRate = this.getExchangeRate(currency);
    const totalAmount = totalPoints * exchangeRate;

    // Get bonus point ratio from system configuration
    const bonusRatio = await this.systemConfigurationRepository.findOne({
      where: {
        key: SYSTEM_SETTING_KEY.BONUS_POINT_RATIO,
      },
    });
    const bonusPointRatio = Number(bonusRatio?.value || BONUS_POINTS_RATIO);

    const orderData = {
      userId: createOrderDto.userId,
      code: createOrderDto.code || (await this.generateOrderCode()),
      totalPoints,
      totalAmount,
      currency,
      paymentMethod: createOrderDto.paymentMethod,
      description: createOrderDto.description,
      exchangeRate,
      bonusPointRatio,
      status: OrderStatus.PENDING,
      usedPoints: createOrderDto.usedPoints ?? 0,
    };

    const order = this.orderRepository.create(orderData);
    const savedOrder = await this.orderRepository.save(order);

    // Build order items array and bulk save
    const orderItemsToCreate = createOrderDto.items.map((item) => {
      const product = products.find((p) => p.id === item.productId)!;
      const itemPoints = (product.pricePoints || 0) * item.quantity;
      return {
        orderId: savedOrder.id,
        productId: item.productId,
        quantity: item.quantity,
        totalPoints: itemPoints,
        metadata: item.metadata || {},
      } as Partial<OrderItem>;
    });

    const orderItemEntities =
      this.orderItemRepository.create(orderItemsToCreate);
    await this.orderItemRepository.save(orderItemEntities);

    // Load order items and return complete order
    const orderItems = await this.orderItemService.findByOrderId(savedOrder.id);
    savedOrder.items = orderItems;

    // Emit order created event
    this.eventEmitter.emit(
      ORDER_EVENTS.CREATED,
      new OrderCreatedEvent(
        savedOrder.id,
        savedOrder.userId,
        savedOrder.status,
        savedOrder.totalAmount,
        savedOrder.totalPoints,
        savedOrder.paymentMethod,
        {
          usedPoints: savedOrder.usedPoints,
          currency: savedOrder.currency,
          exchangeRate: savedOrder.exchangeRate,
        },
      ),
    );

    return savedOrder;
  }

  async getOrderById(id: string, userId?: string): Promise<Order | null> {
    try {
      const order = await this.orderRepository.findOne({ where: { id } });

      if (!order) {
        return null;
      }

      if (userId && order.userId !== userId) {
        return null; // User can only access their own orders
      }

      // Load order items
      const orderItems = await this.orderItemService.findByOrderId(order.id);
      order.items = orderItems;

      return order;
    } catch (error) {
      // Handle invalid UUID format
      if (error.message?.includes('invalid input syntax for type uuid')) {
        return null;
      }
      throw error;
    }
  }

  async getOrdersByUserId(userId: string): Promise<Order[]> {
    const orders = await this.orderRepository.findByUserId(userId);

    // Load order items for each order
    for (const order of orders) {
      const orderItems = await this.orderItemService.findByOrderId(order.id);
      order.items = orderItems;
    }

    return orders;
  }

  async updateOrder(
    id: string,
    updateOrderDto: UpdateOrderDto,
  ): Promise<Order | null> {
    const order = await this.getOrderById(id);
    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId: id },
      } as ErrorResponse);
    }

    await this.orderRepository.update(id, updateOrderDto);
    return this.getOrderById(id);
  }

  async updateOrderStatus(
    orderId: string,
    status: OrderStatus,
  ): Promise<Order | null> {
    const order = await this.getOrderById(orderId);
    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId },
      } as ErrorResponse);
    }

    const previousStatus = order.status;
    const updatedOrder = await this.orderRepository.updateStatus(
      orderId,
      status,
    );

    if (updatedOrder) {
      this.eventEmitter.emit(
        ORDER_EVENTS.STATUS_CHANGED,
        new OrderStatusChangedEvent(
          updatedOrder.id,
          updatedOrder.userId,
          previousStatus,
          updatedOrder.status,
        ),
      );
    }

    return updatedOrder;
  }

  async cancelOrder(id: string): Promise<Order | null> {
    const order = await this.getOrderById(id);
    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId: id },
      } as ErrorResponse);
    }

    if (order.status === OrderStatus.CANCELLED) {
      throw new BadRequestException({
        code: 'ORDER_ALREADY_CANCELLED',
        data: { orderId: id },
      } as ErrorResponse);
    }

    if (
      order.status === OrderStatus.PAID ||
      order.status === OrderStatus.COMPLETED
    ) {
      throw new BadRequestException({
        code: 'ORDER_CANNOT_BE_CANCELLED',
        data: { orderId: id, status: order.status },
      } as ErrorResponse);
    }

    const updatedOrder = await this.updateOrderStatus(
      id,
      OrderStatus.CANCELLED,
    );
    if (updatedOrder) {
      this.eventEmitter.emit(
        ORDER_EVENTS.CANCELLED,
        new OrderCancelledEvent(
          updatedOrder.id,
          updatedOrder.userId,
          new Date(),
        ),
      );
    }
    return updatedOrder;
  }

  async completeOrder(id: string): Promise<Order | null> {
    const order = await this.getOrderById(id);
    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId: id },
      } as ErrorResponse);
    }

    if (order.status === OrderStatus.COMPLETED) {
      throw new BadRequestException({
        code: 'ORDER_ALREADY_COMPLETED',
        data: { orderId: id },
      } as ErrorResponse);
    }

    if (order.status !== OrderStatus.PAID) {
      throw new BadRequestException({
        code: 'ORDER_CANNOT_BE_COMPLETED',
        data: { orderId: id, status: order.status },
      } as ErrorResponse);
    }

    const updatedOrder = await this.updateOrderStatus(
      id,
      OrderStatus.COMPLETED,
    );
    if (updatedOrder) {
      this.eventEmitter.emit(
        ORDER_EVENTS.COMPLETED,
        new OrderCompletedEvent([updatedOrder], new Date()),
      );
    }
    return updatedOrder;
  }

  async completeOrders(orderIds: string[]): Promise<Order[]> {
    const orders = await this.orderRepository.findBy({
      id: In(orderIds),
      status: OrderStatus.PAID,
    });

    if (orders.length === 0) {
      return [];
    }

    await this.orderRepository.updateBy(
      { id: In(map(orders, 'id')) },
      { status: OrderStatus.COMPLETED },
    );

    if (orders.length > 0) {
      this.eventEmitter.emit(
        ORDER_EVENTS.COMPLETED,
        new OrderCompletedEvent(
          orders.map((order) => ({
            ...order,
            status: OrderStatus.COMPLETED,
          })),
          new Date(),
        ),
      );
    }

    return orders;
  }

  async handlePaymentSuccess(
    orderId: string,
    paymentTransactionId: string,
    amount: number,
  ): Promise<Order | null> {
    const order = await this.getOrderById(orderId);
    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId },
      } as ErrorResponse);
    }

    const totalPointUsed = first(
      await this.paymentTransactionService.sumPointByOrders([orderId]),
    );

    if (order.totalPoints !== totalPointUsed.point) {
      return order;
    }

    // Update order status to PAID
    const updatedOrder = await this.updateOrderStatus(
      orderId,
      OrderStatus.PAID,
    );

    if (updatedOrder) {
      // Emit order paid event
      this.eventEmitter.emit(
        ORDER_EVENTS.PAID,
        new OrderPaidEvent(updatedOrder, paymentTransactionId, amount),
      );
    }

    return updatedOrder;
  }

  async deleteOrder(id: string): Promise<boolean> {
    const order = await this.getOrderById(id);
    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId: id },
      } as ErrorResponse);
    }

    // Delete order items first
    await this.orderItemService.deleteByOrderId(id);

    // Then delete the order
    const result = await this.orderRepository.delete(id);
    return result.affected > 0;
  }

  async getCompletedOrders(): Promise<Order[]> {
    const orders = await this.orderRepository.findCompletedOrders();

    // Load order items for each order
    for (const order of orders) {
      const orderItems = await this.orderItemService.findByOrderId(order.id);
      order.items = orderItems;
    }

    return orders;
  }

  async calculateAmountSpentByUser(userId: string[]): Promise<
    {
      userId: string;
      amountSpent: number;
      usedPoints: number;
    }[]
  > {
    const amountSpentByUser =
      await this.paymentTransactionService.calculateAmountSpentByUser(userId);

    const pointsSpentByUser = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.userId', 'userId')
      .addSelect('SUM(order.usedPoints)', 'usedPoints')
      .where('order.userId IN (:...userIds)', { userIds: userId })
      .andWhere('order.status IN (:...orderStatus)', {
        orderStatus: [
          OrderStatus.COMPLETED,
          OrderStatus.PAID,
          OrderStatus.PENDING,
        ],
      })
      .groupBy('order.userId')
      .getRawMany();

    return userId.map((userId) => {
      const amountSpent =
        amountSpentByUser.find((item) => item.userId === userId)?.amount ?? 0;
      const usedPoints =
        pointsSpentByUser.find((item) => item.userId === userId)?.usedPoints ??
        0;

      return {
        userId,
        amountSpent,
        usedPoints,
      };
    });
  }

  // Helper function to get exchange rate for a currency
  private getExchangeRate(currency: CURRENCY_TYPE): number {
    return EXCHANGE_RATES[currency] || EXCHANGE_RATES[CURRENCY_TYPE.VND];
  }

  private async generateOrderCode(): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');

    // Get count of orders created today
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() + 1,
    );

    const todayOrders = await this.orderRepository.find({
      where: {
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    const orderNumber = (todayOrders.length + 1).toString().padStart(3, '0');
    return `ORD_${dateStr}_${orderNumber}`;
  }

  async list(query: SearchOrdersDto): Promise<PaginationResponse<Order>> {
    const {
      userId,
      keyword,
      washingMachineName,
      storeName,
      userName,
      status,
      paymentMethod,
      startDate,
      endDate,
      sort,
      skip = 0,
      limit = 20,
    } = query;

    const qb = this.orderRepository
      .createQueryBuilder('order')
      .withDeleted()
      .leftJoin(User, 'user', 'user.id = order.userId');

    // Filter by user ID
    if (userId) {
      qb.andWhere('order.userId = :userId', { userId });
    }

    // Filter by washing machine name (search in order items metadata)
    if (washingMachineName || storeName || keyword) {
      qb.leftJoin(OrderItem, 'orderItems', 'orderItems.orderId = order.id')
        .leftJoin(Product, 'product', 'product.id = orderItems.productId')
        .leftJoin(
          MachineProgram,
          'machineProgram',
          'machineProgram.productId = product.id',
        )
        .leftJoin(
          WashingMachine,
          'washingMachine',
          'washingMachine.id = machineProgram.machineId',
        )
        .leftJoin(Store, 'store', 'store.id = product.storeId');
      if (washingMachineName) {
        qb.andWhere('washingMachine.name ILIKE :washingMachineName', {
          washingMachineName: `%${washingMachineName}%`,
        });
      }
      if (storeName) {
        qb.andWhere('store.name ILIKE :storeName', {
          storeName: `%${storeName}%`,
        });
      }
      if (keyword) {
        qb.andWhere(
          '(washingMachine.name ILIKE :keyword OR store.name ILIKE :keyword OR user.name ILIKE :keyword)',
          {
            keyword: `%${keyword}%`,
          },
        );
      }
    }

    // Filter by customer name
    if (userName) {
      qb.andWhere('user.name ILIKE :userName', {
        userName: `%${userName}%`,
      });
    }

    // Filter by order status
    if (status) {
      qb.andWhere('order.status IN (:...status)', { status });
    }

    // Filter by payment method
    if (paymentMethod) {
      qb.andWhere('order.paymentMethod IN (:...paymentMethod)', {
        paymentMethod,
      });
    }

    // Filter by date range
    if (startDate) {
      qb.andWhere('order.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      qb.andWhere('order.createdAt <= :endDate', { endDate });
    }

    // Apply sorting
    applySorting(qb, sort, ORDER_SORT_FIELD_MAP, OrderSortField.CREATED_AT);

    qb.skip(skip).take(limit);

    const [items, count] = await qb.distinct(true).getManyAndCount();

    return {
      items,
      count,
    };
  }

  async processOrder(
    order: Order,
    options?: {
      includePaymentTransactions?: boolean;
    },
  ): Promise<OrderResponseDto> {
    return first(await this.processOrders([order], options));
  }

  async processOrders(
    orders: Order[],
    options?: {
      includePaymentTransactions?: boolean;
    },
  ): Promise<OrderResponseDto[]> {
    if (orders.length === 0) {
      return [];
    }

    const { includePaymentTransactions = true } = options ?? {};
    const orderIds = uniq(map(orders, 'id'));

    let paymentTransactions: PaymentTransaction[] = [];

    if (includePaymentTransactions) {
      paymentTransactions = await this.paymentTransactionRepository.find({
        where: {
          orderId: In(orderIds),
        },
        order: {
          createdAt: 'DESC',
        },
      });
    }

    const orderItems = await this.orderItemRepository.find({
      where: {
        orderId: In(orderIds),
      },
      withDeleted: true,
    });

    const orderItemsDtos =
      await this.orderItemService.processOrderItems(orderItems);

    const paymentTransactionDtos = plainToInstance(
      PaymentTransactionDto,
      paymentTransactions,
    );

    const users = await this.userRepository.findBy({
      id: In(uniq(map(orders, 'userId'))),
    });

    return orders.map((order) => {
      const orderDto = plainToInstance(OrderResponseDto, order);
      const user = users.find((user) => user.id === order.userId);

      orderDto.items = orderItemsDtos.filter(
        (item) => item.orderId === order.id,
      );
      const paymentTransactionsByOrder = paymentTransactionDtos.filter(
        (transaction) => transaction.orderId === order.id,
      );

      orderDto.paymentTransactions = paymentTransactionsByOrder;

      orderDto.totalAmountPaid = sumBy(
        paymentTransactionsByOrder.filter(
          (transaction) =>
            transaction.type === PaymentTransactionType.DIRECT_PAYMENT,
        ),
        'amount',
      );

      orderDto.user = plainToInstance(UserDto, user);
      return orderDto;
    });
  }

  async calculateBonusPoint(
    order: Order,
    paymentTransactions?: PaymentTransaction[],
  ): Promise<number> {
    // Use bonus_point_ratio from order instead of system config
    const bonusRate = order.bonusPointRatio || BONUS_POINTS_RATIO;

    if (paymentTransactions) {
      paymentTransactions = await this.paymentTransactionRepository.findBy({
        orderId: order.id,
        type: PaymentTransactionType.DIRECT_PAYMENT,
        status: PaymentTransactionStatus.SUCCESS,
      });
    }

    const totalAmountPaid = sumBy(paymentTransactions, 'amount');

    if (totalAmountPaid <= 0) {
      return 0;
    }

    this.logger.log(
      `Calculating bonus points for order: ${order.id}, Amount: ${totalAmountPaid} ${order.currency}, Bonus Rate: ${bonusRate}`,
    );

    // bonusPoints = totalAmount * bonusRate
    // bonusPoints = totalAmount / exchangeRate (use calculatePointsFromAmount
    // => exchangeRate = 1 / bonusRate
    return this.paymentBalanceService.calculatePointsFromAmount(
      totalAmountPaid,
      1 / bonusRate,
    );
  }

  async getPendingOrders(options: {
    createdBefore?: Date;
    limit?: number;
    skip?: number;
  }): Promise<Order[]> {
    const { createdBefore, limit, skip } = options;
    return this.orderRepository.find({
      where: {
        status: OrderStatus.PENDING,
        ...(createdBefore ? { createdAt: LessThan(createdBefore) } : {}),
      },
      order: {
        createdAt: 'DESC',
      },
      ...(limit ? { take: limit } : {}),
      ...(skip ? { skip: skip } : {}),
    });
  }

  async checkPendingOrder({
    userId,
    machineId,
  }: {
    userId?: string;
    machineId?: string;
  }): Promise<boolean> {
    const orderItemQb = this.orderItemRepository
      .createQueryBuilder('oi')
      .innerJoin(Order, 'o', 'o.id = oi.orderId AND o.status = :status', {
        status: OrderStatus.PENDING,
      });

    if (userId) {
      orderItemQb.andWhere('o.userId = :userId', { userId });
    }

    if (machineId) {
      orderItemQb.innerJoin(
        MachineProgram,
        'mp',
        'mp.productId = oi.productId AND mp.machineId = :machineId',
        { machineId },
      );
    }

    return orderItemQb.getExists();
  }
}
