import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { OrderService } from './order.service';
import { OrderItemService } from './services/order-item.service';
import { OrderPaymentListener } from './listeners/order-payment.listener';
import { OrderStatusListener } from './listeners/order-status.listener';

import { DatabaseModule } from '@app/shared/database/database.module';
import { PaymentModule } from '@app/payment/payment.module';
import { StoreModule } from '@app/store/store.module';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    DatabaseModule,
    PaymentModule,
    StoreModule,
  ],
  providers: [
    OrderService,
    OrderItemService,
    OrderPaymentListener,
    OrderStatusListener,
  ],
  exports: [OrderService, OrderItemService],
})
export class OrderModule {}
