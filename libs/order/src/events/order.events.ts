import { Order, OrderStatus } from '@app/shared/database/entities';

export class OrderStatusChangedEvent {
  constructor(
    public readonly orderId: string,
    public readonly userId: string,
    public readonly oldStatus: OrderStatus,
    public readonly newStatus: OrderStatus,
    public readonly metadata?: Record<string, any>,
  ) {}
}

export class OrderCreatedEvent {
  constructor(
    public readonly orderId: string,
    public readonly userId: string,
    public readonly status: OrderStatus,
    public readonly totalAmount: number,
    public readonly totalPoints: number,
    public readonly paymentMethod: string,
    public readonly metadata?: Record<string, any>,
  ) {}
}

export class OrderPaidEvent {
  constructor(
    public readonly order: Order,
    public readonly paymentTransactionId: string,
    public readonly amount: number,
    public readonly metadata?: Record<string, any>,
  ) {}
}

export class OrderCompletedEvent {
  constructor(
    public readonly orders: Order[],
    public readonly completedAt: Date,
    public readonly metadata?: Record<string, any>,
  ) {}
}

export class OrderCancelledEvent {
  constructor(
    public readonly orderId: string,
    public readonly userId: string,
    public readonly cancelledAt: Date,
    public readonly reason?: string,
    public readonly metadata?: Record<string, any>,
  ) {}
}
