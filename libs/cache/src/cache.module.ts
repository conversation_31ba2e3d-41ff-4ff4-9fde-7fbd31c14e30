import {
  DynamicModule,
  Global,
  Module,
  OnModuleDestroy,
  Inject,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { REDIS_CLIENT, REDIS_PUBSUB_CLIENT } from './constants';
import { RedisClientService } from './services/redis-client.service';
import { RedisPublisherService } from './services/redis-publisher.service';
import { RedisSubscriberService } from './services/redis-subscriber.service';

@Global()
@Module({})
export class CacheModule implements OnModuleDestroy {
  private redisClient: Redis;
  private redisPubSubClient: Redis;

  static forRoot(): DynamicModule {
    return {
      module: CacheModule,
      imports: [ConfigModule.forRoot()],
      providers: [
        {
          provide: REDIS_CLIENT,
          useFactory: (configService: ConfigService) => {
            const client = new Redis({
              host: configService.get<string>('REDIS_HOST'),
              port: configService.get<number>('REDIS_PORT'),
            });
            return client;
          },
          inject: [ConfigService],
        },
        {
          provide: REDIS_PUBSUB_CLIENT,
          useFactory: (configService: ConfigService) => {
            const client = new Redis({
              host: configService.get<string>('REDIS_HOST'),
              port: configService.get<number>('REDIS_PORT'),
            });
            return client;
          },
          inject: [ConfigService],
        },
        RedisClientService,
        RedisPublisherService,
        RedisSubscriberService,
      ],
      exports: [
        REDIS_CLIENT,
        REDIS_PUBSUB_CLIENT,
        RedisClientService,
        RedisPublisherService,
        RedisSubscriberService,
      ],
    };
  }

  constructor(
    @Inject(REDIS_CLIENT) redisClient: Redis,
    @Inject(REDIS_PUBSUB_CLIENT) redisPubSubClient: Redis,
  ) {
    this.redisClient = redisClient;
    this.redisPubSubClient = redisPubSubClient;
  }

  async onModuleDestroy() {
    if (this.redisClient) {
      await this.redisClient.disconnect();
    }
    if (this.redisPubSubClient) {
      await this.redisPubSubClient.disconnect();
    }
  }
}
