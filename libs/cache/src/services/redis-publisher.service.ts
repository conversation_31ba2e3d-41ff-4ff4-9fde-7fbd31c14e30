import { Injectable, Inject, Logger } from '@nestjs/common';
import { Redis } from 'ioredis';
import { REDIS_PUBSUB_CLIENT } from '../constants';
import moment from 'moment';

@Injectable()
export class RedisPublisherService {
  private readonly logger = new Logger(RedisPublisherService.name);
  private readonly publisher: Redis;

  constructor(@Inject(REDIS_PUBSUB_CLIENT) private readonly client: Redis) {
    this.publisher = client;
  }

  async publish(channel: string, message: any): Promise<number> {
    message.data = {
      ...message.data,
      time: moment().toDate(),
    };
    return this.publisher.publish(channel, JSON.stringify(message));
  }

  getClient(): Redis {
    return this.publisher;
  }
}
