import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cluster, Redis } from 'ioredis';
import { REDIS_CLIENT } from '../constants';

@Injectable()
export class RedisClientService {
  protected readonly logger = new Logger(this.constructor.name);
  constructor(
    @Inject(REDIS_CLIENT) protected readonly client: Redis | Cluster,
  ) {}

  async keys(key: string): Promise<string[]> {
    return this.client.keys(key);
  }

  async get(key: string): Promise<string> {
    return this.client.get(key);
  }

  async set(
    key: string,
    value: string,
    options?: { EX?: number },
  ): Promise<void> {
    await this.client.set(key, value, 'EX', options?.EX ?? 0);
  }

  async del(key: string, value?: string): Promise<number> {
    return this.client.del(key, value);
  }

  async sadd(key: string, value: string): Promise<void> {
    await this.client.sadd(key, value);
  }

  async srem(key: string, value: string): Promise<void> {
    await this.client.srem(key, value);
  }

  async smembers(key: string): Promise<string[]> {
    return this.client.smembers(key);
  }

  pipeline() {
    return this.client.pipeline();
  }
}
