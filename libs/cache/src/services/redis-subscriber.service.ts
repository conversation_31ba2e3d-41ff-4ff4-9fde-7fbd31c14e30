import { Injectable, Inject, Logger, OnModuleD<PERSON>roy } from '@nestjs/common';
import { Redis } from 'ioredis';
import { REDIS_PUBSUB_CLIENT } from '../constants';
import { isUndefined } from 'lodash';

@Injectable()
export class RedisSubscriberService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisSubscriberService.name);
  private readonly subscriber: <PERSON>is;

  constructor(@Inject(REDIS_PUBSUB_CLIENT) private readonly client: Redis) {
    this.subscriber = client.duplicate();
  }

  async subscribe(
    channel: string,
    callback?: (message: string) => void,
  ): Promise<void> {
    this.subscriber.subscribe(channel, (err, count) => {
      if (err) {
        this.logger.error('Failed to subscribe: %s', err.message);
      } else {
        this.logger.debug(
          `Channel id ${channel} subscribed successfully! This client is currently subscribed to ${count} channels.`,
        );
      }
    });

    if (!isUndefined(callback)) {
      this.subscriber.on('message', (chan, message) => {
        if (chan === channel) {
          callback(message);
        }
      });
    }
  }

  async unsubscribe(channel: string): Promise<void> {
    this.subscriber.unsubscribe(channel);
  }

  getClient(): Redis {
    return this.subscriber;
  }

  async onModuleDestroy() {
    if (this.subscriber) {
      await this.subscriber.disconnect();
    }
  }
}
