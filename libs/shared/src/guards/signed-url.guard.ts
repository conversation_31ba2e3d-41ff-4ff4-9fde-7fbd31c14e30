import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { verifySignature } from '../helpers/url.helper';
import { ErrorResponse } from '../types';

@Injectable()
export class SignedUrlGuard implements CanActivate {
  logger = new Logger(SignedUrlGuard.name);

  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    this.logger.debug('===TRIGGER CONTROLLER GUARD===');
    const req = context.switchToHttp().getRequest();

    // Extract relevant parameters from the request
    const path = req.path;
    const expires = req.query.expires;
    const signature = req.query.signature;

    if (!path || !expires || !signature) {
      throw new ForbiddenException({
        code: 'URL_MISSING_PARAMETERS',
      } as ErrorResponse);
    }

    return verifySignature(path, req.query, signature, this.configService);
  }
}
