import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { UserStatus } from '@app/shared/database/entities/user.entity';
import { ErrorResponse } from '@app/shared/types';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/auth.decorator';

@Injectable()
export class UserStatusAccessible implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    if (user.status === UserStatus.INACTIVE) {
      throw new UnauthorizedException({
        code: 'USER_NOT_ACTIVATED',
      } as ErrorResponse);
    }

    if (user.status === UserStatus.BLOCKED) {
      throw new UnauthorizedException({
        code: 'USER_BLOCKED',
      } as ErrorResponse);
    }

    return true;
  }
}
