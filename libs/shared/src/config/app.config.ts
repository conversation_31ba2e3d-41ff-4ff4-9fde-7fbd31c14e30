import * as dotenv from 'dotenv';
import DatabaseConfig from './typeorm.config';
import FacebookConfig from './facebook.config';
import GoogleConfig from './google.config';
import LineConfig from './line.config';
import MoMoPayConfig from './momopay.config';
import ZaloPayConfig from './zalopay.config';
import VNPayConfig from './vnpay.config';

// Load environment variables based on NODE_ENV
const nodeEnv = process.env.NODE_ENV;
if (nodeEnv === 'test') {
  dotenv.config({ path: 'env.test', override: true, quiet: true });
} else {
  dotenv.config({ quiet: true });
}

export default () => ({
  APP_ENV: process.env.APP_ENV || 'localhost',
  APP_PORT: process.env.APP_PORT,
  APP_KEY: process.env.APP_KEY,
  APP_URL: `${process.env.APP_URL}${process.env.APP_ROUTER_PREFIX ? '/' : ''}${process.env.APP_ROUTER_PREFIX}`,
  APP_ROUTER_PREFIX: process.env.APP_ROUTER_PREFIX || '',
  APP_NAME: process.env.APP_NAME || 'nestjs',
  APP_LOG_LEVEL: process.env.APP_LOG_LEVEL || 'log',
  FRONTEND_APP: {
    URL: process.env.FRONTEND_URL,
    AUTH_LOGIN_URL: `${process.env.FRONTEND_URL}/${process.env.FRONTEND_AUTH_LOGIN_URI}`,
    AUTH_SUCCESS_URL: `${process.env.FRONTEND_URL}/${process.env.FRONTEND_AUTH_SUCCESS_URI}`,
  },
  MOBILE_APP: (() => {
    const scheme = process.env.MOBILE_APP_DEEPLINK_SCHEME || 'niinuma-laundry';
    const host = process.env.MOBILE_APP_DEEPLINK_HOST || 'payment';
    const successPath =
      process.env.MOBILE_APP_DEEPLINK_SUCCESS_PATH || 'success';
    const cancelPath = process.env.MOBILE_APP_DEEPLINK_CANCEL_PATH || 'cancel';

    return {
      DEEPLINK_SCHEME: scheme,
      DEEPLINK_HOST: host,
      DEEPLINK_SUCCESS_PATH: successPath,
      DEEPLINK_CANCEL_PATH: cancelPath,
      SUCCESS_DEEPLINK_TEMPLATE: `${scheme}://${host}/${successPath}?orderId=`,
      CANCEL_DEEPLINK_TEMPLATE: `${scheme}://${host}/${cancelPath}?orderId=`,
    };
  })(),
  CORS: process.env.CORS_ALLOWED_ORIGINS?.split(','),
  COOKIE_DOMAIN: process.env.COOKIE_DOMAIN?.split(','),
  MAIL_SENDER: process.env.MAIL_SENDER || '<EMAIL>',
  EXPO: {
    ACCESS_TOKEN: process.env.EXPO_ACCESS_TOKEN,
  },
  DATABASE: {
    ...DatabaseConfig(),
  },
  JWT: {
    ACCESS_TOKEN_SECRET: process.env.JWT_ACCESS_TOKEN_SECRET,
    REFRESH_TOKEN_SECRET: process.env.JWT_REFRESH_TOKEN_SECRET,
    ACCESS_TOKEN_EXPIRATION_TIME:
      process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME || 300,
    REFRESH_TOKEN_EXPIRATION_TIME:
      process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME || 25200,
    ACCESS_COOKIES_NAME:
      process.env.JWT_ACCESS_COOKIES_NAME || 'Authentication',
    REFRESH_COOKIES_NAME: process.env.JWT_REFRESH_COOKIES_NAME || 'Refresh',
  },
  CODE_FORGOT_PASSWORD_EXPIRE_MINUTES:
    process.env.CODE_FORGOT_PASSWORD_EXPIRE_MINUTES || 60,
  CODE_EMAIL_VERIFY_EXPIRE_MINUTES:
    process.env.CODE_EMAIL_VERIFY_EXPIRE_MINUTES || 60,
  CODE_CHANGE_EMAIL_EXPIRE_MINUTES:
    process.env.CODE_CHANGE_EMAIL_EXPIRE_MINUTES || 30,
  CODE_DELETE_USER_MINUTES: process.env.CODE_DELETE_USER_MINUTES || 5,
  BLOCK_EMAIL_PLUS_SYMBOL: process.env.BLOCK_EMAIL_PLUS_SYMBOL === 'true',
  REDIS: {
    HOST: process.env.REDIS_HOST,
    PORT: process.env.REDIS_PORT,
  },
  CACHE: {
    HOST: process.env.REDIS_HOST,
    PORT: process.env.REDIS_PORT,
    TTL: parseInt(process.env.CACHE_TTL || '86400000'),
    MAX: parseInt(process.env.CACHE_MAX || '1000'),
  },
  THROTTLE: {
    TTL: process.env.THROTTLE_TTL || 60,
    LIMIT: process.env.THROTTLE_LIMIT || 10,
  },
  SOCIAL: {
    GOOGLE: {
      ...GoogleConfig(),
    },
    LINE: {
      ...LineConfig(),
    },
    FACEBOOK: {
      ...FacebookConfig(),
    },
  },
  AWS: {
    ACCESS_KEY: process.env.AWS_ACCESS_KEY_ID,
    SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    DEFAULT_REGION: process.env.AWS_DEFAULT_REGION,
    BUCKET_GALLERY: process.env.AWS_BUCKET_GALLERY,
    PRESIGNED_URL_EXPIRE_MINUTES: process.env.AWS_PRESIGNED_URL_EXPIRE_MINUTES,
    BUCKET_GALLERY_ENDPOINT: process.env.AWS_BUCKET_GALLERY_ENDPOINT,
    USE_ACCELERATE_ENDPOINT:
      !process.env.AWS_USE_ACCELERATE_ENDPOINT ||
      process.env.AWS_USE_ACCELERATE_ENDPOINT === 'true',
  },
  CRONJOB: {
    REMIND_ENDING_SESSION: process.env.REMIND_ENDING_SESSION || '*/1 * * * *',
    ENDING_SESSION_MINUTES: process.env.ENDING_SESSION_MINUTES || 5,
    ENDING_SESSION_BUFFER_SECONDS:
      process.env.ENDING_SESSION_BUFFER_SECONDS || 2,
    REMIND_ENDING_SESSION_INTERVAL_SECONDS:
      process.env.REMIND_ENDING_SESSION_INTERVAL_SECONDS || 1,
    REMIND_COMPLETED_SESSION:
      process.env.REMIND_COMPLETED_SESSION || '*/1 * * * *',
    REMIND_COMPLETED_SESSION_BUFFER_SECONDS:
      process.env.REMIND_COMPLETED_SESSION_BUFFER_SECONDS || 3,
    REMIND_COMPLETED_SESSION_INTERVAL_SECONDS:
      process.env.REMIND_COMPLETED_SESSION_INTERVAL_SECONDS || 1,
    MARK_DONE_SESSION: process.env.MARK_DONE_SESSION || '*/1 * * * *',
    MARK_DONE_SESSION_OUTDATED_MINUTES:
      process.env.MARK_DONE_SESSION_OUTDATED_MINUTES || 20,
    REMIND_AFTER_COMPLETED_SESSION:
      process.env.REMIND_AFTER_COMPLETED_SESSION || '*/1 * * * *',
    REMIND_AFTER_COMPLETED_SESSION_MINUTES:
      process.env.REMIND_AFTER_COMPLETED_SESSION_MINUTES || 10,
    REMIND_AFTER_COMPLETED_SESSION_BUFFER_SECONDS:
      process.env.REMIND_AFTER_COMPLETED_SESSION_BUFFER_SECONDS || 30,
    CANCEL_ORDER: process.env.CANCEL_ORDER || '*/1 * * * *',
    CANCEL_ORDER_MINUTES: process.env.CANCEL_ORDER_MINUTES || 15,
  },
  PAYMENT: {
    MOMOPAY: {
      ...MoMoPayConfig(),
    },
    ZALOPAY: {
      ...ZaloPayConfig(),
    },
    VNPAY: {
      ...VNPayConfig(),
    },
    CASH: {
      CASH_ID: process.env.CASH_ID || 'CASH_001',
      CASH_LOCATION: process.env.CASH_LOCATION || 'Main Cash Counter',
    },
  },
});
