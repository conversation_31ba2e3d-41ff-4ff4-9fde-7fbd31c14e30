import { DocumentBuilder } from '@nestjs/swagger';

const openAPIConfig = new DocumentBuilder()
  .setTitle('Laundry OpenAPI')
  .setVersion('1.0')
  .addBearerAuth(
    {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      name: 'JWT',
      description: 'Enter JWT token',
      in: 'header',
    },
    'Access token', // This name here is important for matching up with @ApiBearerAuth() in your controller!
  )
  .build();

export default openAPIConfig;
