import { DataSourceOptions } from 'typeorm';

export default () => {
  const dbWriteHost = process.env.DB_WRITE_HOST;
  const dbReadHosts = (process.env.DB_READ_HOST ?? '').split(',');
  const dbPort = parseInt(process.env.DB_PORT) || 5432;
  const dbName = process.env.DB_NAME;
  const dbUser = process.env.DB_USER;
  const dbPassword = process.env.DB_PASSWORD;

  return {
    type: 'postgres',
    entities: ['dist/**/database/entities/**/*.entity{.ts,.js}'],
    synchronize: process.env.DB_SYNCHRONIZE || false,
    migrationsTableName: 'migrations',
    logging: Boolean(process.env.DB_LOGGING) || false,
    relationLoadStrategy: 'query',
    maxQueryExecutionTime:
      parseInt(process.env.DB_MAX_QUERY_EXECUTION_TIME) || -1,
    replication: {
      // set the default destination for read queries as the master instance
      defaultMode: 'master',
      master: {
        host: dbWriteHost,
        port: dbPort,
        database: dbName,
        username: dbUser,
        password: dbPassword,
      },
      slaves: dbReadHosts.map((host) => {
        return {
          host: host,
          port: dbPort,
          database: dbName,
          username: dbUser,
          password: dbPassword,
        };
      }),
    },
    migrationsRun: false,
  } as DataSourceOptions;
};
