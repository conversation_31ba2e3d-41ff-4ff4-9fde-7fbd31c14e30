export default () => ({
  // ZaloPay Sandbox credentials from official docs
  // Source: https://docs.zalopay.vn/v2/general/overview.html
  APP_ID: process.env.ZALOPAY_APP_ID || '554',
  KEY1: process.env.ZALOPAY_KEY1 || '8NdU5pG5R2spGHGhyO99HN1OhD8IQJBn',
  KEY2: process.env.ZALOPAY_KEY2 || 'uUfsWgfLkRLzq6W2uNXTCxrfxs51auny',

  // ZaloPay v2 Sandbox endpoints
  PAYMENT_URL:
    process.env.ZALOPAY_PAYMENT_URL ||
    'https://sb-openapi.zalopay.vn/v2/create',
  STATUS_URL:
    process.env.ZALOPAY_STATUS_URL || 'https://sb-openapi.zalopay.vn/v2/query',
  CALLBACK_URL:
    process.env.ZALOPAY_CALLBACK_URL ||
    'https://your-domain.com/api/payment/callback/zalopay',

  // Test cards for development
  TEST_CARDS: {
    // International cards (Visa, Master, JCB)
    INTERNATIONAL: {
      CARD_NUMBER: '****************',
      CARD_HOLDER: 'NGUYEN VAN A',
      EXPIRY_DATE: '01/25',
      CVV: '123',
    },

    // ATM cards (SBI Bank) - Valid cards
    ATM_VALID: [
      '****************',
      '****************',
      '****************',
      '****************',
      '****************',
      '****************',
    ],

    // ATM cards - Lost/stolen cards
    ATM_LOST: [
      '****************',
      '****************',
      '****************',
      '****************',
      '****************',
      '****************',
    ],

    // ATM cards - Timeout cards
    ATM_TIMEOUT: ['****************', '****************', '****************'],

    // ATM cards - Insufficient balance
    ATM_NO_BALANCE: [
      '****************',
      '****************',
      '****************',
    ],
  },
});
