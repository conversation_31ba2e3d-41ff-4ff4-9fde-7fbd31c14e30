import { ConsoleLogger, Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import appConfig from '../config/app.config';

@Injectable()
export class AppLogger extends ConsoleLogger {
  constructor(private readonly cls: ClsService) {
    super();

    // Set log level from environment variable
    const logLevel = appConfig().APP_LOG_LEVEL;
    this.setLogLevels(this.getLogLevels(logLevel));

    return new Proxy(this, {
      get(target, prop) {
        const original = target[prop];
        if (
          typeof original === 'function' &&
          ['log', 'error', 'warn', 'debug', 'verbose'].includes(prop as string)
        ) {
          return (...args: any[]) => {
            if (args.length > 0) {
              args[0] = target.formatMessageLog(args[0]);
            }
            return original.apply(target, args);
          };
        }
        return original;
      },
    });
  }

  private getLogId() {
    return this.cls.get('logId') || this.cls.getId();
  }

  private formatMessageLog(message: any) {
    const logId = this.getLogId();
    return (logId ? `[${logId}] ` : '') + `${message}`;
  }

  private getLogLevels(
    logLevel: string,
  ): ('error' | 'warn' | 'log' | 'debug' | 'verbose' | 'fatal')[] {
    // Handle special cases for disabling logs
    if (
      logLevel.toLowerCase() === 'none' ||
      logLevel.toLowerCase() === 'silent'
    ) {
      return [];
    }

    const levels: ('error' | 'warn' | 'log' | 'debug' | 'verbose' | 'fatal')[] =
      ['error', 'warn', 'log', 'debug', 'verbose'];
    const levelIndex = levels.indexOf(logLevel.toLowerCase() as any);

    if (levelIndex === -1) {
      // Default to 'log' if invalid level
      return ['error', 'warn', 'log'];
    }

    return levels.slice(0, levelIndex + 1);
  }
}
