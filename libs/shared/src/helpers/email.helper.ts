import { USER_LANGUAGE } from '@app/shared/database/entities/user.entity';
import { join } from 'path';
import * as process from 'process';
import * as Handlebars from 'handlebars';
import fs from 'fs';
import { MAIL_TEMPLATE, MAIL_TEMPLATE_TYPE } from '@app/mail/constants';

/**
 * Check email domain.
 *
 * @param email
 */
export function isValidEmailDomain(email: string): boolean {
  let isValid: boolean = true;
  const emailDomainPatternStr: string = process.env.EMAIL_DOMAIN_PATTERN;
  const emailDomainPatterns: string[] = emailDomainPatternStr.split(',');
  for (const emailDomainPattern of emailDomainPatterns) {
    isValid = email.endsWith(emailDomainPattern);
    if (isValid) {
      break;
    }
  }

  return isValid;
}

export function getMailTemplate(
  type: MAIL_TEMPLATE_TYPE,
  language: USER_LANGUAGE,
): {
  subject: string;
  path: string;
  language: USER_LANGUAGE;
} {
  const template = MAIL_TEMPLATE[type][language];
  return {
    subject: template.subject,
    path: template.path,
    language,
  };
}

/**
 * Map data to template.
 *
 * @param template
 * @param data
 */
export async function mapDataToTemplate(
  template: { subject: string; path: string; language: USER_LANGUAGE },
  data: any,
): Promise<string> {
  const language = template.language || USER_LANGUAGE.EN;
  const templatePath = join(process.cwd(), 'dist', template.path);
  const layoutPath = join(
    process.cwd(),
    'dist',
    'templates/layouts',
    `footer_${language}.hbs`,
  );

  const html: string = fs.readFileSync(templatePath, 'utf-8');
  const footer = fs.readFileSync(layoutPath, 'utf-8');
  Handlebars.registerPartial('footer', footer);

  return Handlebars.compile(html)({
    ...data,
  });
}
