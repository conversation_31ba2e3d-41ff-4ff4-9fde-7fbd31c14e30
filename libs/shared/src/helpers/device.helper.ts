/**
 * Detect if the request is from a mobile device based on User-Agent
 * @param userAgent - User-Agent string from request headers
 * @returns boolean - true if mobile device, false otherwise
 */
export function isMobileDevice(userAgent?: string): boolean {
  if (!userAgent) return false;

  const mobileRegex =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  return mobileRegex.test(userAgent);
}
