import { ForbiddenException } from '@nestjs/common';
import * as crypto from 'crypto';
import { keys, map, sortBy, join } from 'lodash';
import { ErrorResponse } from '../types';
import { ConfigService } from '@nestjs/config';

/**
 * Generate Signed Url
 *
 * @param path
 * @param expiresIn
 * @param payload
 * @param configService
 * @returns
 */
export function generateSignedUrl(
  path: string,
  expiresIn: number,
  payload: object,
  configService?: ConfigService,
): { signedUrl: string; signature: string; expires: number } {
  const config = configService || new ConfigService();
  const secretKey = config.get('APP_KEY');
  const apiUrl = config.get('APP_URL');
  const prefixApi = config.get('APP_ROUTER_PREFIX');
  const expires = Math.floor(Date.now() / 1000) + expiresIn;
  if (path[0] !== '/') {
    path = '/' + path;
  }
  payload['expires'] = expires;
  const payloadString = join(
    map(sortBy(keys(payload)), (k) => `${k}=${payload[k]}`),
    '&',
  );

  let canonicalPath = `${path}&${payloadString}`;
  if (prefixApi) {
    canonicalPath = `/${prefixApi}${canonicalPath}`;
  }
  const canonicalString = encodeURI(`${canonicalPath}`);

  const signature = crypto
    .createHmac('sha256', secretKey)
    .update(canonicalString)
    .digest('hex');

  const signedUrl = `${apiUrl}/${path}?${payloadString}&signature=${signature}`;

  return {
    signedUrl,
    signature,
    expires,
  };
}

/**
 * Verify Signature
 *
 *
 * @param path
 * @param payload
 * @param signature
 * @param configService
 * @returns boolean
 */
export function verifySignature(
  path: string,
  payload: object,
  signature: string,
  configService?: ConfigService,
): boolean {
  const config = configService || new ConfigService();
  const expectedTimestamp = Math.floor(Date.now() / 1000);
  const expires = payload['expires'] ?? 0;

  // Check if timestamp is expired
  if (Number(expires) < expectedTimestamp) {
    throw new ForbiddenException({ code: 'URL_EXPIRED' } as ErrorResponse);
  }
  delete payload['signature'];
  const payloadString = join(
    map(sortBy(keys(payload)), (k) => `${k}=${payload[k]}`),
    '&',
  );

  // Generate the expected signature using HMAC
  const secretKey = config.get('APP_KEY');
  const prefixApi = config.get('APP_ROUTER_PREFIX');

  let canonicalPath = `${path}&${payloadString}`;

  // Chỉ thêm prefix nếu path chưa bao gồm prefix
  if (prefixApi && !path.includes(`/${prefixApi}`)) {
    canonicalPath = `/${prefixApi}${canonicalPath}`;
  }

  const canonicalString = encodeURI(`${canonicalPath}`);
  const expectedSignature = crypto
    .createHmac('sha256', secretKey)
    .update(canonicalString)
    .digest('hex');

  // Compare the extracted and expected signatures
  if (signature !== expectedSignature) {
    throw new ForbiddenException({
      code: 'URL_INVALID_SIGNATURE',
    } as ErrorResponse);
  }
  return true;
}

export function extractUrls(string: string) {
  const cleanedText = string.replace(/<img.*?\/>/g, '');
  const urlRegex = /(https?:\/\/[^\s<]+)/g;
  const urls = cleanedText.match(urlRegex);

  return urls ? urls : [];
}

export function getPathFromUrl(url: string): string {
  return new URL(url).pathname;
}

export function isUrl(value: string) {
  try {
    new URL(value);
    return true;
  } catch (exception) {
    return false;
  }
}

/**
 * Parse state query param
 * @param stateParam
 * @returns
 */
export function parseStateQueryParam(stateParam: string): Record<string, any> {
  if (!stateParam) return {};

  try {
    const decoded = decodeURIComponent(stateParam);
    return JSON.parse(decoded);
  } catch (error) {
    return {};
  }
}

/**
 * Generate url attachment
 *
 * @param uri
 */
export function generateUrlAttachment(uri: string): string {
  const bucketGalleryEndpoint = process.env.AWS_BUCKET_GALLERY_ENDPOINT || '';
  const attachmentUrl = process.env.ATTACHMENT_URL || '';
  const isFullUrl = uri.startsWith(bucketGalleryEndpoint);
  const fileBucketUrl: string = isFullUrl
    ? uri
    : `${bucketGalleryEndpoint}/${uri}`;
  if (!attachmentUrl) {
    return fileBucketUrl;
  }
  uri = isFullUrl ? uri.replace(`${bucketGalleryEndpoint}`, '') : uri;
  return `${attachmentUrl}${uri.startsWith('/') ? uri : '/' + uri}`;
}
