/**
 * Extracts values from data using specified key paths.
 * @param sourceData - The source data (object or array) to extract values from
 * @param keyPaths - Array of key paths (strings or arrays) to navigate the data
 * @returns Flattened array of extracted values
 */
export function extractValuesFromDataByKeyPaths<T>(
  sourceData: T | T[],
  keyPaths: (string | string[])[],
): any[] {
  // Normalize input data into an array
  const dataArray: T[] = Array.isArray(sourceData) ? sourceData : [sourceData];

  // Parse and cache key paths (convert strings to arrays if needed)
  const parsedKeyPaths: string[][] = keyPaths.map((path) =>
    Array.isArray(path) ? path : path.split('.'),
  );

  // Map through each key path and extract values from all data items
  return parsedKeyPaths.flatMap((keySequence: string[]) =>
    dataArray.flatMap((dataItem: T) =>
      extractValueFromObjectByKeySequence(dataItem, keySequence),
    ),
  );
}

/**
 * Extracts a value from an object using a sequence of keys.
 * @param dataObject - The object to extract the value from
 * @param keySequence - Sequence of keys to navigate the object
 * @returns Extracted values as a flattened array
 */
export function extractValueFromObjectByKeySequence<T>(
  dataObject: T,
  keySequence: string[],
): any[] {
  let currentValue: any = dataObject;

  // Iterate through each key in the sequence
  for (const key of keySequence) {
    // Return empty array if current value is null/undefined
    if (currentValue == null) return [];

    // Handle array values by mapping through them
    if (Array.isArray(currentValue)) {
      currentValue = currentValue.flatMap((item: any) => item?.[key] ?? []);
    } else {
      // Access property directly for non-array values
      currentValue = currentValue[key] ?? [];
    }
  }

  return currentValue;
}
