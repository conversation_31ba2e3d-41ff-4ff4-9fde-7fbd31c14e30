import { generate } from 'randomstring';
import { load } from 'cheerio';

export function randomString(length: number = 10): string {
  return generate(length);
}

export function randomNumberString(length: number = 6): string {
  return generate({
    length: length,
    charset: 'numeric',
  });
}

export function isRichText(text: string): boolean {
  const richTextTags = [
    '<p',
    '</p>',
    '</strong>',
    '</em>',
    '</u>',
    '</span>',
    '</s>',
    '</h1>',
    '</h2>',
    '</h3>',
    '</strike>',
    '<ol',
    '<li',
    '<img',
    '<s',
  ];
  return richTextTags.some((tag) => text.includes(tag));
}

export function getPlainText(
  htmlString: string,
  isBrokenLine: boolean = false,
  separator: string = '{br}',
): string {
  if (!isRichText(htmlString)) return htmlString;
  const richText = load(htmlString);

  richText('img.emoji').each((index, element) => {
    const emoji: string = richText(element).attr('alt');
    if (emoji) {
      richText(element).replaceWith(emoji);
    }
  });

  richText('img.custom-emoji').each((index, element) => {
    const serverEmojiSrc: string = richText(element).attr('src');
    const serverEmojiName: string = richText(element).attr('alt');
    if (serverEmojiSrc) {
      richText(element).replaceWith(
        `{img src="${serverEmojiSrc}" alt="${serverEmojiName}" />`,
      );
    }
  });

  let result: string;
  if (isBrokenLine) {
    result = richText('p')
      .map((index, element) => richText(element).text().trim())
      .get()
      .join(separator);
    result = result.replaceAll('\n\n', separator).trim();
  } else {
    result = richText.text();
  }

  return result.replace(/&nbsp;/g, ' ').trim();
}

export function extractHrefLinks(input: string): string[] {
  const hrefRegex = /href="([^"]+)"/g;
  const links: string[] = [];
  let match;

  while ((match = hrefRegex.exec(input)) !== null) {
    links.push(match[1]);
  }

  return links;
}

/**
 * Check if word is Japanese.
 *
 * @param word
 * @private
 */
export function isJapaneseWord(word: string): boolean {
  const regex: RegExp =
    /[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}]/gu;
  return regex.test(word);
}

/**
 * Check if message contains NGWord.
 *
 * @param content
 * @param ngWords
 * @private
 */
export function isContainsNGWord(content: string, ngWords: string[]): boolean {
  /*
   * Regex Breakdown:
   *
   * (?:^|[^\p{L}\p{N}])  - Matches the start of the string (^) or any character that is
   *                         NOT a Unicode letter (\p{L}) or number (\p{N}).
   *                         This ensures the forbidden word is not part of a larger word.
   *
   * ${ngWord}            - The exact forbidden word to match (e.g., "shit").
   *
   * (?=[^\p{L}\p{N}]|$)  - Lookahead to ensure the forbidden word is followed by a character
   *                         that is NOT a letter or number, or it's the end of the string.
   *                         This ensures a clean boundary at the end.
   *
   * Flags:
   * 'i'                  - Case-insensitive matching (e.g., "ShIt" will match "shit").
   * 'u'                  - Enables Unicode support, allowing proper handling of non-ASCII characters
   *                         like emoji, Japanese, and other symbols.
   *
   * Purpose:
   * This regex matches the forbidden word (${ngWord}) only when it is **not part of a larger word**,
   * and is surrounded by non-letter/number characters, such as spaces, punctuation, emoji, or symbols.
   */
  for (const ngWord of ngWords) {
    const isJapaneseNGWord: boolean = isJapaneseWord(ngWord);
    const regex: RegExp = new RegExp(
      isJapaneseNGWord
        ? `${ngWord}`
        : `(?:^|[^\\p{L}\\p{N}])${ngWord}(?=[^\\p{L}\\p{N}]|$)`,
      'iu',
    );
    if (regex.test(content)) {
      return true;
    }
  }

  return false;
}
