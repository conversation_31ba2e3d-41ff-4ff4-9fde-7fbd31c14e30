import { Injectable, PipeTransform } from '@nestjs/common';
import appConfig from '@app/shared/config/app.config';
import { getPathFromUrl } from '@app/shared/helpers/url.helper';
import { S3Service } from '@app/aws';
import { ConfigService } from '@nestjs/config';
import { FileValidationException } from '@app/shared/exceptions/file-validation.exception';
import { extractValuesFromDataByKeyPaths } from '@app/shared/helpers/object.helper';
import { first } from 'lodash';
import { ErrorResponse } from '../types';

export class FileValidationField {
  private FILE_TYPE_SEPARATOR = '|';
  private NESTED_PATH_SEPARATOR = '.';

  private readonly _fieldName: string;
  private _fileType: string;
  private _maxSize: number;
  private _isRequired: boolean = false;
  private _callbackCondition: (value: any) => boolean;

  constructor(fieldName: string) {
    this._fieldName = fieldName;
  }

  get fieldName(): string {
    return this._fieldName;
  }

  get fileType(): string[] {
    return this._fileType.split(this.FILE_TYPE_SEPARATOR);
  }

  get maxSize(): number {
    return this._maxSize;
  }

  get firstFieldNestedPath(): string {
    return first(this._fieldName.split(this.NESTED_PATH_SEPARATOR));
  }

  get isRequired(): boolean {
    return this._isRequired;
  }

  getFilteredValues(value: any): string[] {
    if (!this._callbackCondition) {
      return extractValuesFromDataByKeyPaths(value, [this._fieldName]);
    }

    const fieldName = this._fieldName.split(this.NESTED_PATH_SEPARATOR);

    const lastNestedPath = fieldName.pop();

    const values = extractValuesFromDataByKeyPaths(value, [
      fieldName.join(this.NESTED_PATH_SEPARATOR),
    ]);

    if (this._callbackCondition) {
      return values
        .filter(this._callbackCondition)
        .map((value) => value[lastNestedPath]);
    }

    return values.map((value) => value[lastNestedPath]);
  }

  setFileType(fileType: string): FileValidationField {
    this._fileType = fileType;
    return this;
  }

  setMaxSize(maxSize: number): FileValidationField {
    this._maxSize = maxSize;
    return this;
  }

  setIsRequired(isRequired: boolean): FileValidationField {
    this._isRequired = isRequired;
    return this;
  }

  setCallbackCondition(
    callbackCondition: (value: any) => boolean,
  ): FileValidationField {
    this._callbackCondition = callbackCondition;
    return this;
  }
}

@Injectable()
export class FileBuilderPipe implements PipeTransform {
  private fields: FileValidationField[] = [];

  async transform(value: any): Promise<any> {
    const s3Service = this.createS3Service();
    const fileUrls = this.fields
      .map((field) => field.getFilteredValues(value))
      .flat();
    for (const field of this.fields) {
      if (
        !value ||
        !value[field.firstFieldNestedPath] ||
        value[field.firstFieldNestedPath].length === 0
      ) {
        if (field.isRequired) {
          throw new FileValidationException(
            { code: 'FILE_REQUIRED' } as ErrorResponse,
            s3Service,
            fileUrls,
          );
        }
        continue;
      }
      const urls = field.getFilteredValues(value);

      for (const url of urls) {
        const filePath = getPathFromUrl(url);
        const metadata = await s3Service.getMetadata(filePath);
        if (!metadata) {
          continue;
        }
        if (field.fileType) {
          const allowedFileExtensions: string[] = field.fileType;
          const fileExtension = metadata.format;

          const isValidFileExtension: boolean =
            allowedFileExtensions.includes(fileExtension);
          if (!isValidFileExtension) {
            throw new FileValidationException(
              { code: 'INVALID_FILE_TYPE' } as ErrorResponse,
              s3Service,
              fileUrls,
            );
          }
        }

        if (field.maxSize && metadata.size > field.maxSize) {
          throw new FileValidationException(
            { code: 'FILE_SIZE_EXCEEDED' } as ErrorResponse,
            s3Service,
            fileUrls,
          );
        }
      }
    }

    return value;
  }

  addValidateField(validationField: FileValidationField): FileBuilderPipe {
    return this.addValidateFields([validationField]);
  }

  addValidateFields(validationField: FileValidationField[]): FileBuilderPipe {
    this.fields = [...this.fields, ...validationField];
    return this;
  }

  private createS3Service() {
    return new S3Service(new ConfigService(appConfig()));
  }
}
