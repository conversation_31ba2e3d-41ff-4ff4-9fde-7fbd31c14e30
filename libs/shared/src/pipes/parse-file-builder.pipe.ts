import {
  PipeTransform,
  Injectable,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { ErrorResponse } from '../types';

@Injectable()
export class ParseFileBuilderPipe implements PipeTransform {
  private fileTypeValidator: string | null = null;
  private maxSizeValidator: number | null = null;
  private fileIsRequired: boolean = true;
  private errorHttpStatusCode: HttpStatus = HttpStatus.BAD_REQUEST;

  constructor(private readonly fieldName: string) {}

  addFileTypeValidator(options: { fileType: string }): ParseFileBuilderPipe {
    this.fileTypeValidator = options.fileType;
    return this;
  }

  addMaxSizeValidator(options: { maxSize: number }): ParseFileBuilderPipe {
    this.maxSizeValidator = options.maxSize;
    return this;
  }

  setFileIsRequired(fileIsRequired: boolean): ParseFileBuilderPipe {
    this.fileIsRequired = fileIsRequired;
    return this;
  }

  setErrorHttpStatusCode(
    errorHttpStatusCode: HttpStatus,
  ): ParseFileBuilderPipe {
    this.errorHttpStatusCode = errorHttpStatusCode;
    return this;
  }

  transform(value: any): any {
    if (
      !value ||
      !value[this.fieldName] ||
      value[this.fieldName].length === 0
    ) {
      if (this.fileIsRequired) {
        throw new BadRequestException({
          code: 'FILE_REQUIRED',
        } as ErrorResponse);
      }
      return value;
    }

    const files = Array.isArray(value[this.fieldName])
      ? value[this.fieldName]
      : [value[this.fieldName]];

    for (const file of files) {
      if (this.fileTypeValidator) {
        const allowedFileExtensions: string[] =
          this.fileTypeValidator.split('|');
        const fileExtension = file.originalname.split('.').pop().toLowerCase();

        const isValidFileExtension: boolean =
          allowedFileExtensions.includes(fileExtension);
        if (!isValidFileExtension) {
          throw new BadRequestException({
            code: 'INVALID_FILE_TYPE',
          } as ErrorResponse);
        }
      }

      if (this.maxSizeValidator && file.size > this.maxSizeValidator) {
        throw new BadRequestException({
          code: 'FILE_SIZE_EXCEEDED',
        } as ErrorResponse);
      }
    }

    return value;
  }
}
