import { Logger as TypeOrmLogger } from 'typeorm';
import { isEmpty } from 'lodash';
import { ConfigService } from '@nestjs/config';
import { AppLogger } from '@app/shared/logger/app.logger';

export class DatabaseLogger implements TypeOrmLogger {
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: AppLogger,
  ) {}

  logQuery(query: string, parameters?: unknown[]) {
    const enableAllSlowQuery =
      this.configService.get('DATABASE.maxQueryExecutionTime') == -1;
    if (!enableAllSlowQuery) {
      this.logger.log(
        `${this.stringifyQueryWithParameters(query, parameters)}`,
      );
    }
  }

  logQueryError(error: string, query: string, parameters?: unknown[]) {
    this.logger.error(
      `${this.stringifyQueryWithParameters(query, parameters)} -- ${error}`,
    );
  }

  logQuerySlow(time: number, query: string, parameters?: unknown[]) {
    this.logger.warn(
      `Time: ${time}ms -- ${this.stringifyQueryWithParameters(query, parameters)}`,
    );
  }

  logMigration(message: string) {
    this.logger.log(message);
  }

  logSchemaBuild(message: string) {
    this.logger.log(message);
  }

  log(level: 'log' | 'info' | 'warn', message: string) {
    if (level === 'log') {
      return this.logger.log(message);
    }
    if (level === 'info') {
      return this.logger.debug(message);
    }
    if (level === 'warn') {
      return this.logger.warn(message);
    }
  }

  private stringifyParameters(parameters?: unknown[]) {
    try {
      return JSON.stringify(parameters);
    } catch {
      return '';
    }
  }

  private stringifyQueryWithParameters(query: string, parameters: unknown[]) {
    if (!Array.isArray(parameters)) {
      return query.replace(/\s+/g, ' ');
    }

    const replacements = parameters.map((value) => {
      if (typeof value === 'string') {
        return `'${value}'`;
      }
      if (typeof value === 'number') {
        return value;
      }
      if (typeof value === 'boolean') {
        return value;
      }
      if (Array.isArray(value)) {
        return value
          .map((element) =>
            typeof element === 'string' ? `'${element}'` : element,
          )
          .join(',');
      }
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value);
      }
      return !isEmpty(value) ? value.toString() : '';
    });

    query = query.replace(/\$\d+/g, (match) => {
      const index = parseInt(match.slice(1), 10) - 1;
      return replacements[index] !== undefined
        ? String(replacements[index])
        : match;
    });

    return query.replace(/\s+/g, ' ');
  }
}
