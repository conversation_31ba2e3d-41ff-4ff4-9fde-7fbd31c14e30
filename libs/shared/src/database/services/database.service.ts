import { Injectable, OnApplicationShutdown } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { AppLogger } from '@app/shared/logger/app.logger';
import {
  QueryRunner,
  EntityManager,
  DataSource,
  ReplicationMode,
} from 'typeorm';
import { releaseQueryRunner } from '@app/shared/helpers/database.helper';

@Injectable()
export class DatabaseService implements OnApplicationShutdown {
  constructor(
    protected readonly logger: AppLogger,
    protected readonly dataSource: DataSource,
    protected readonly cls: ClsService,
  ) {}

  async onApplicationShutdown(signal: string) {
    if (this.dataSource.isInitialized) {
      await this.dataSource.destroy();
      this.logger.log(`DB DataSource shutdown on ${signal}`);
    }
  }

  /**
   * Create QueryRunner with replication mode.
   * @param mode - The replication mode (read or write).
   * @param withTransaction - Run with transaction.
   */
  async createQueryRunner(
    mode: ReplicationMode,
    withTransaction = false,
  ): Promise<boolean> {
    // Store the QueryRunner in the CLS context
    const queryRunner = this.dataSource.createQueryRunner(mode);

    await queryRunner.connect();
    if (withTransaction) {
      await queryRunner.startTransaction();
    }
    this.logger.debug('Used database connection!');
    this.cls.set('queryRunner', queryRunner);

    return true;
  }

  /**
   * Start a transaction by creating a QueryRunner.
   * @returns {Promise<void>}
   */
  async startTransaction(): Promise<void> {
    // Initialize QueryRunner
    await this.createQueryRunner('master', true);
  }

  /**
   * Commit the active transaction.
   * @returns {Promise<void>}
   */
  async commitTransaction(): Promise<void> {
    const queryRunner = this.getQueryRunner();
    if (queryRunner) {
      await queryRunner.commitTransaction();
    }
  }

  /**
   * Rollback the active transaction.
   * @returns {Promise<void>}
   */
  async rollbackTransaction(): Promise<void> {
    const queryRunner = this.getQueryRunner();
    if (queryRunner) {
      await queryRunner.rollbackTransaction();
    }
  }

  /**
   * Release after completing the transaction.
   * @returns {Promise<void>}
   */
  async release(): Promise<void> {
    const queryRunner = this.getQueryRunner();
    this.cls?.set('queryRunner', null);
    await releaseQueryRunner(queryRunner);
    this.logger.debug('Releases used database connection success!');
  }

  /**
   * Get the current EntityManager if transaction is in progress
   * @returns EntityManager
   */
  getEntityManager(): EntityManager {
    const queryRunner = this.getQueryRunner();
    if (!queryRunner) {
      return null;
    }
    return queryRunner.manager;
  }

  /**
   * Get the current QueryRunner from CLS.
   */
  getQueryRunner(): QueryRunner | undefined {
    return this.cls?.get('queryRunner');
  }
}
