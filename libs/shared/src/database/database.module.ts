import { Global, Module } from '@nestjs/common';
import { getDataSourceToken, TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppLogger } from '@app/shared/logger/app.logger';
import { DatabaseLogger } from './logger/database.logger';
import { DataSource } from 'typeorm';
import { DB_CONNECTION_NAME, DB_SERVICE_NAME } from './constants';
import { ClsService } from 'nestjs-cls';
import { DatabaseService } from './services/database.service';
import {
  UserDeviceTokenRepository,
  UserNotificationRepository,
  UserRepository,
  UserSocialRepository,
  StoreRepository,
  ProductRepository,
  WashingMachineRepository,
  MachineProgramRepository,
  ServiceSessionRepository,
  PaymentTransactionRepository,
  PaymentUserRepository,
  PaymentTransactionBalanceRepository,
  OrderRepository,
  OrderItemRepository,
  SystemConfigurationRepository,
  FaqQuestionRepository,
} from './repositories';
import {
  User,
  UserSocial,
  UserNotification,
  UserDeviceToken,
  Store,
  Product,
  WashingMachine,
  MachineProgram,
  ServiceSession,
  PaymentTransaction,
  PaymentUser,
  PaymentTransactionBalance,
  Order,
  OrderItem,
  SystemConfiguration,
  FaqQuestion,
} from './entities';

@Global()
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forRootAsync({
      name: DB_CONNECTION_NAME.BACKEND,
      imports: [ConfigModule],
      useFactory: async (
        configService: ConfigService,
        appLogger: AppLogger,
      ) => ({
        ...configService.get('DATABASE'),
        name: DB_CONNECTION_NAME.BACKEND,
        entities: [
          User,
          UserSocial,
          UserNotification,
          UserDeviceToken,
          Store,
          Product,
          WashingMachine,
          MachineProgram,
          ServiceSession,
          Order,
          OrderItem,
          PaymentUser,
          PaymentTransaction,
          PaymentTransactionBalance,
          SystemConfiguration,
          FaqQuestion,
        ],
        logger: new DatabaseLogger(configService, appLogger),
      }),
      inject: [ConfigService, AppLogger],
    }),
  ],
  providers: [
    AppLogger,
    {
      provide: DB_SERVICE_NAME.BACKEND,
      useFactory: (
        dataSource: DataSource,
        appLogger: AppLogger,
        cls: ClsService,
      ) => {
        return new DatabaseService(appLogger, dataSource, cls);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        AppLogger,
        ClsService,
      ],
    },
    {
      provide: 'UserRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new UserRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'UserSocialRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new UserSocialRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'UserDeviceTokenRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new UserDeviceTokenRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'UserNotificationRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new UserNotificationRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'StoreRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new StoreRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'ProductRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new ProductRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'WashingMachineRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new WashingMachineRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'MachineProgramRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new MachineProgramRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'ServiceSessionRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new ServiceSessionRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'PaymentTransactionRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new PaymentTransactionRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'PaymentUserRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new PaymentUserRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'PaymentTransactionBalanceRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new PaymentTransactionBalanceRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'OrderRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new OrderRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'OrderItemRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new OrderItemRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'SystemConfigurationRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new SystemConfigurationRepository(
          dataSource.manager,
          databaseService,
        );
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
    {
      provide: 'FaqQuestionRepositoryInterface',
      useFactory: (
        dataSource: DataSource,
        databaseService: DatabaseService,
      ) => {
        return new FaqQuestionRepository(dataSource.manager, databaseService);
      },
      inject: [
        getDataSourceToken(DB_CONNECTION_NAME.BACKEND),
        DB_SERVICE_NAME.BACKEND,
      ],
    },
  ],
  exports: [
    DB_SERVICE_NAME.BACKEND,
    'UserRepositoryInterface',
    'UserSocialRepositoryInterface',
    'UserDeviceTokenRepositoryInterface',
    'UserNotificationRepositoryInterface',
    'StoreRepositoryInterface',
    'ProductRepositoryInterface',
    'WashingMachineRepositoryInterface',
    'MachineProgramRepositoryInterface',
    'ServiceSessionRepositoryInterface',
    'PaymentTransactionRepositoryInterface',
    'PaymentUserRepositoryInterface',
    'PaymentTransactionBalanceRepositoryInterface',
    'OrderRepositoryInterface',
    'OrderItemRepositoryInterface',
    'SystemConfigurationRepositoryInterface',
    'FaqQuestionRepositoryInterface',
  ],
})
export class DatabaseModule {}
