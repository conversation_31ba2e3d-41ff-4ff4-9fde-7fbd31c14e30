import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { PaymentTransactionBalance } from '@app/shared/database/entities';
import { PaymentTransactionBalanceRepositoryInterface } from '@app/shared/database/repositories';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class PaymentTransactionBalanceRepository
  extends BaseRepository<PaymentTransactionBalance>
  implements PaymentTransactionBalanceRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(PaymentTransactionBalance, manager, databaseService);
  }
}
