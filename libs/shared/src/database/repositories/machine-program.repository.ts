import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { BaseRepository } from './base/base.repository';
import { MachineProgram } from '../entities/machine-program.entity';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class MachineProgramRepository extends BaseRepository<MachineProgram> {
  constructor(manager: EntityManager, databaseService: DatabaseService) {
    super(MachineProgram, manager, databaseService);
  }
}
