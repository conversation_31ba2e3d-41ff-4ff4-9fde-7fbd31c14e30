import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { PaymentTransaction } from '@app/shared/database/entities';
import { PaymentTransactionRepositoryInterface } from '@app/shared/database/repositories';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class PaymentTransactionRepository
  extends BaseRepository<PaymentTransaction>
  implements PaymentTransactionRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(PaymentTransaction, manager, databaseService);
  }
}
