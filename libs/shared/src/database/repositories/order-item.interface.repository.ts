import { OrderItem } from '../entities/order-item.entity';
import { BaseRepositoryInterface } from './base/base.repository.interface';

export interface OrderItemRepositoryInterface
  extends BaseRepositoryInterface<OrderItem> {
  findById(id: string): Promise<OrderItem | null>;
  findByOrderId(orderId: string): Promise<OrderItem[]>;
  findByProductId(productId: string): Promise<OrderItem[]>;
  deleteByOrderId(orderId: string): Promise<boolean>;
}
