import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { UserSocial } from '@app/shared/database/entities';
import { UserSocialRepositoryInterface } from '@app/shared/database/repositories';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class UserSocialRepository
  extends BaseRepository<UserSocial>
  implements UserSocialRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected readonly databaseService: DatabaseService,
  ) {
    super(UserSocial, manager, databaseService);
  }
}
