import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { BaseRepository } from './base/base.repository';
import { Product } from '../entities/product.entity';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class ProductRepository extends BaseRepository<Product> {
  constructor(manager: EntityManager, databaseService: DatabaseService) {
    super(Product, manager, databaseService);
  }
}
