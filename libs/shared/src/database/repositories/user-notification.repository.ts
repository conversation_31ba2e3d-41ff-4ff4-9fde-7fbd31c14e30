import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { UserNotification } from '@app/shared/database/entities';
import { UserNotificationRepositoryInterface } from '@app/shared/database/repositories';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class UserNotificationRepository
  extends BaseRepository<UserNotification>
  implements UserNotificationRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(UserNotification, manager, databaseService);
  }
}
