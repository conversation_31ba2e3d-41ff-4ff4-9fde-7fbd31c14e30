import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { BaseRepository } from './base/base.repository';
import { ServiceSession } from '../entities/service-session.entity';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class ServiceSessionRepository extends BaseRepository<ServiceSession> {
  constructor(manager: EntityManager, databaseService: DatabaseService) {
    super(ServiceSession, manager, databaseService);
  }
}
