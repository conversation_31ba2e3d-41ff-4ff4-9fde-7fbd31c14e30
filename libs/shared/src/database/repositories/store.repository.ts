import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { BaseRepository } from './base/base.repository';
import { Store } from '../entities/store.entity';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class StoreRepository extends BaseRepository<Store> {
  constructor(manager: EntityManager, databaseService: DatabaseService) {
    super(Store, manager, databaseService);
  }
}
