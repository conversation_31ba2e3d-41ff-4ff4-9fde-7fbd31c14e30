import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { UserDeviceToken } from '@app/shared/database/entities';
import { UserDeviceTokenRepositoryInterface } from '@app/shared/database/repositories';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class UserDeviceTokenRepository
  extends BaseRepository<UserDeviceToken>
  implements UserDeviceTokenRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(UserDeviceToken, manager, databaseService);
  }
}
