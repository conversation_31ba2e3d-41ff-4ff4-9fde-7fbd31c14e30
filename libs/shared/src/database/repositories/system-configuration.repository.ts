import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { SystemConfiguration } from '../entities';
import { DatabaseService } from '../services/database.service';
import { BaseRepository } from './base/base.repository';

@Injectable()
export class SystemConfigurationRepository extends BaseRepository<SystemConfiguration> {
  constructor(manager: EntityManager, databaseService: DatabaseService) {
    super(SystemConfiguration, manager, databaseService);
  }
}
