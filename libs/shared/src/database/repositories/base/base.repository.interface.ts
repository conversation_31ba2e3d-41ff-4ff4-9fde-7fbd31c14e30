import {
  DeepPartial,
  <PERSON>ete<PERSON><PERSON>ult,
  FindManyO<PERSON>s,
  FindOneOptions,
  FindOptionsWhere,
  QueryRunner,
  Repository,
  SaveOptions,
  SelectQueryBuilder,
  UpdateResult,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

export interface BaseRepositoryInterface<T> extends Repository<T> {
  /**
   * Creates a new query builder that can be used to build a SQL query.
   * @returns {SelectQueryBuilder<T>}
   */
  createQueryBuilder(
    alias?: string,
    queryRunner?: QueryRunner,
  ): SelectQueryBuilder<T>;

  /**
   * Creates a new entity instance and copies all entity properties from this object into a new entity.
   * Note that it copies only properties that are present in entity schema.
   * @returns {T | T[]}
   */
  create(): T;
  create(entityLikeArray: DeepPartial<T>[]): T[];
  create(entityLike: DeepPartial<T>): T;
  create(entityLike?: DeepPartial<T> | DeepPartial<T>[]): T | T[];

  /**
   * Save or update an entity, automatically using the correct EntityManager if transaction is active.
   * @param entityOrEntities - The entity or entities to save.
   * @returns {Promise<T>}
   */
  save(entity: T, options?: SaveOptions): Promise<T>;
  save(entities: T[], options?: SaveOptions): Promise<T[]>;
  save(entityOrEntities: T | T[], options?: SaveOptions): Promise<T | T[]>;

  /**
   * Update an entity by ID, automatically using the correct EntityManager if transaction is active.
   * @param id - The ID of the entity.
   * @param entity - The partial entity to update.
   * @returns {Promise<UpdateResult>}
   */
  update(
    id: string | number,
    entity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult>;

  /**
   * Update entities matching the specified conditions.
   * @param where - Conditions to match entities.
   * @param entity - Partial entity containing the fields to update.
   * @returns {Promise<UpdateResult>}
   */
  updateBy(
    where: FindOptionsWhere<T>,
    entity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult>;

  /**
   * Delete an entity by its ID.
   * @param id - The ID of the entity to delete.
   * @returns {Promise<DeleteResult>}
   */
  delete(id: string | number): Promise<DeleteResult>;

  /**
   * Deletes entities by a given condition(s)
   * @param where - The ID of the entity to delete.
   * @returns {Promise<DeleteResult>}
   */
  deleteBy(where: FindOptionsWhere<T>): Promise<DeleteResult>;

  /**
   * Soft delete an entity
   * @param criteria
   * @returns {Promise<UpdateResult>}
   */
  softDelete(
    criteria: string | string[] | number | number[] | FindOptionsWhere<T>,
  ): Promise<UpdateResult>;

  /**
   * Check if an entity exists based on the specified conditions.
   * @param where - Conditions to match entities.
   * @returns {Promise<boolean>}
   */
  exists(where: FindOptionsWhere<T>): Promise<boolean>;

  /**
   * Check if an entity exists based on the specified conditions.
   * @param where - Conditions to match entities.
   * @returns {Promise<boolean>}
   */
  existsBy(where: FindOptionsWhere<T>): Promise<boolean>;

  /**
   * Find a single entity based on the provided options.
   * @param options - Options for finding a single entity.
   * @returns {Promise<T | null>}
   */
  findOne(options: FindOneOptions<T>): Promise<T | null>;

  /**
   * Finds first entity that matches given where condition.
   * @param where
   * @returns {Promise<T | null>}
   */
  findOneBy(
    where: FindOptionsWhere<T> | FindOptionsWhere<T>[],
  ): Promise<T | null>;

  /**
   * Find a single entity by its ID.
   * @param id - The ID of the entity to find.
   * @returns {Promise<T | null>}
   */
  findOneById(id: string | number): Promise<T | null>;

  /**
   * Find multiple entities based on the provided options.
   * @param options - Options for finding entities.
   * @returns {Promise<T[]>}
   */
  find(options?: FindManyOptions<T>): Promise<T[]>;

  /**
   * Finds entities that match given find options.
   * @param where
   * @returns {Promise<T[]>}
   */
  findBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<T[]>;

  /**
   * Find multiple entities and return the count of matching entities.
   * @param options - Options for finding entities.
   * @returns {Promise<[T[], number]>}
   */
  findAndCount(options?: FindManyOptions<T>): Promise<[T[], number]>;

  /**
   * Finds entities that match given WHERE conditions.
   * Also counts all entities that match given conditions,
   * but ignores pagination settings (from and take options).
   * @param where
   * @returns {Promise<T[]>}
   */
  findAndCountBy(
    where: FindOptionsWhere<T> | FindOptionsWhere<T>[],
  ): Promise<[T[], number]>;

  /**
   * Get the count of entities that match the specified conditions.
   * @param where - Conditions to match entities.
   * @returns {Promise<number>}
   */
  count(where: FindOptionsWhere<T>): Promise<number>;

  /**
   * Increments some column by provided value of the entities matched given conditions.
   * @param conditions - Conditions to match the entity.
   * @param propertyPath - The field to increment.
   * @param value - The value to increment by (default is 1).
   * @returns {Promise<UpdateResult>}
   */
  increment(
    conditions: FindOptionsWhere<T>,
    propertyPath: string,
    value: number | string,
  ): Promise<UpdateResult>;
  /**
   * Decrements some column by provided value of the entities matched given conditions.
   * @param conditions - Conditions to match the entity.
   * @param propertyPath - The field to increment.
   * @param value - The value to increment by (default is 1).
   * @returns {Promise<UpdateResult>}
   */
  decrement(
    conditions: FindOptionsWhere<T>,
    propertyPath: string,
    value: number | string,
  ): Promise<UpdateResult>;
}
