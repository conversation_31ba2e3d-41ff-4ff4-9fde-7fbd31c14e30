import {
  Repository,
  DeepPartial,
  UpdateResult,
  DeleteResult,
  FindOptionsWhere,
  FindOneOptions,
  FindManyOptions,
  EntityManager,
  EntityTarget,
  SaveOptions,
  SelectQueryBuilder,
  QueryRunner,
  InsertResult,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { DatabaseService } from '@app/shared/database/services/database.service';
import { BaseRepositoryInterface } from './base.repository.interface';

export abstract class BaseRepository<T>
  extends Repository<T>
  implements BaseRepositoryInterface<T>
{
  constructor(
    target: EntityTarget<T>,
    manager: EntityManager,
    protected readonly databaseService: DatabaseService,
  ) {
    super(target, manager);
  }

  /**
   * Creates a new query builder that can be used to build a SQL query.
   * @returns {SelectQueryBuilder<T>}
   */
  createQueryBuilder(
    alias?: string,
    queryRunner?: QueryRunner,
  ): SelectQueryBuilder<T> {
    if (!queryRunner) {
      queryRunner = this.databaseService.getQueryRunner();
    }

    return super.createQueryBuilder(alias, queryRunner);
  }

  /**
   * Creates a new entity instance and copies all entity properties from this object into a new entity.
   * Note that it copies only properties that are present in entity schema.
   * @returns {T | T[]}
   */
  create(): T;
  create(entityLikeArray: DeepPartial<T>[]): T[];
  create(entityLike: DeepPartial<T>): T;
  create(entityLike?: DeepPartial<T> | DeepPartial<T>[]): T | T[] {
    if (Array.isArray(entityLike)) {
      return entityLike.map((item) => {
        const entity = super.create(item);
        return entity;
      });
    }

    const entity = super.create(entityLike as DeepPartial<T>);
    return entity;
  }

  /**
   * Save or update an entity, automatically using the correct EntityManager if transaction is active.
   * @param entityOrEntities - The entity or entities to save.
   * @returns {Promise<T>}
   */
  save(entity: T, options?: SaveOptions): Promise<T>;
  save(entities: T[], options?: SaveOptions): Promise<T[]>;
  async save(
    entityOrEntities: T | T[],
    options?: SaveOptions,
  ): Promise<T | T[]> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();
    if (Array.isArray(entityOrEntities)) {
      return entityManager
        ? await entityManager.save(
            entityManager.create(this.target, entityOrEntities),
            options,
          )
        : await super.save(this.create(entityOrEntities), options);
    }
    return entityManager
      ? await entityManager.save(
          entityManager.create(this.target, entityOrEntities),
          options,
        )
      : await super.save(this.create(entityOrEntities), options);
  }

  /**
   * Insert a new entity or entities, automatically using the correct EntityManager if transaction is active.
   * @param entity - The entity or entities to insert.
   * @returns {Promise<InsertResult>}
   */
  async insert(
    entity: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
  ): Promise<InsertResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();
    return entityManager
      ? await entityManager.insert(this.target, entity)
      : await super.insert(entity);
  }

  /**
   * Update an entity by ID, automatically using the correct EntityManager if transaction is active.
   * @param id - The ID of the entity.
   * @param entity - The partial entity to update.
   * @returns {Promise<UpdateResult>}
   */
  async update(
    id: string | number,
    entity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();
    return entityManager
      ? await entityManager.update(this.target, id, entity)
      : await super.update(id, entity);
  }

  /**
   * Update entities matching the specified conditions.
   * @param where - Conditions to match entities.
   * @param entity - Partial entity containing the fields to update.
   * @returns {Promise<UpdateResult>}
   */
  async updateBy(
    where: FindOptionsWhere<T>,
    entity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();
    return entityManager
      ? await entityManager.update(this.target, where, entity)
      : await super.update(where, entity);
  }

  /**
   * Delete an entity by its ID.
   * @param id - The ID of the entity to delete.
   * @returns {Promise<DeleteResult>}
   */
  async delete(id: string | number): Promise<DeleteResult> {
    return await this.deleteBy({ id } as any);
  }

  /**
   * Deletes entities by a given condition(s)
   * @param where - The ID of the entity to delete.
   * @returns {Promise<DeleteResult>}
   */
  async deleteBy(where: FindOptionsWhere<T>): Promise<DeleteResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();
    return entityManager
      ? await entityManager.delete(this.target, where)
      : await super.delete(where);
  }

  /**
   * Soft delete an entity
   * @param criteria
   * @returns {Promise<UpdateResult>}
   */
  async softDelete(
    criteria: string | string[] | number | number[] | FindOptionsWhere<T>,
  ): Promise<UpdateResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();
    return entityManager
      ? await entityManager.softDelete(this.target, criteria)
      : await super.softDelete(criteria);
  }

  /**
   * Check if an entity exists based on the specified conditions.
   * @param where - Conditions to match entities.
   * @returns {Promise<boolean>}
   */
  async exists(where: FindOptionsWhere<T>): Promise<boolean> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.exists(this.target, { where })
      : await super.exists({ where });
  }

  /**
   * Check if an entity exists based on the specified conditions.
   * @param where - Conditions to match entities.
   * @returns {Promise<boolean>}
   */
  async existsBy(where: FindOptionsWhere<T>): Promise<boolean> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.existsBy(this.target, where)
      : await super.existsBy(where);
  }

  /**
   * Find a single entity based on the provided options.
   * @param options - Options for finding a single entity.
   * @returns {Promise<T | null>}
   */
  async findOne(options: FindOneOptions<T>): Promise<T | null> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.findOne(this.target, options)
      : await super.findOne(options);
  }

  /**
   * Finds first entity that matches given where condition.
   * @param where
   * @returns {Promise<T | null>}
   */
  async findOneBy(
    where: FindOptionsWhere<T> | FindOptionsWhere<T>[],
  ): Promise<T | null> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.findOneBy(this.target, where)
      : await super.findOneBy(where);
  }

  /**
   * Find a single entity by its ID.
   * @param id - The ID of the entity to find.
   * @returns {Promise<T | null>}
   */
  async findOneById(id: string | number): Promise<T | null> {
    return this.findOneBy({ id } as any);
  }

  /**
   * Find multiple entities based on the provided options.
   * @param options - Options for finding entities.
   * @returns {Promise<T[]>}
   */
  async find(options?: FindManyOptions<T>): Promise<T[]> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.find(this.target, options)
      : await super.find(options);
  }

  /**
   * Finds entities that match given find options.
   * @param where
   * @returns {Promise<T[]>}
   */
  async findBy(
    where: FindOptionsWhere<T> | FindOptionsWhere<T>[],
  ): Promise<T[]> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.findBy(this.target, where)
      : await super.findBy(where);
  }

  /**
   * Find multiple entities and return the count of matching entities.
   * @param options - Options for finding entities.
   * @returns {Promise<[T[], number]>}
   */
  async findAndCount(options?: FindManyOptions<T>): Promise<[T[], number]> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.findAndCount(this.target, options)
      : await super.findAndCount(options);
  }

  /**
   * Finds entities that match given WHERE conditions.
   * Also counts all entities that match given conditions,
   * but ignores pagination settings (from and take options).
   * @param where
   * @returns {Promise<T[]>}
   */
  async findAndCountBy(
    where: FindOptionsWhere<T> | FindOptionsWhere<T>[],
  ): Promise<[T[], number]> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.findAndCountBy(this.target, where)
      : await super.findAndCountBy(where);
  }

  /**
   * Get the count of entities that match the specified conditions.
   * @param where - Conditions to match entities.
   * @returns {Promise<number>}
   */
  async count(where: FindOptionsWhere<T>): Promise<number> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.count(this.target, where)
      : await super.count(where);
  }

  /**
   * Increments some column by provided value of the entities matched given conditions.
   * @param conditions - Conditions to match the entity.
   * @param propertyPath - The field to increment.
   * @param value - The value to increment by (default is 1).
   * @returns {Promise<UpdateResult>}
   */
  async increment(
    conditions: FindOptionsWhere<T>,
    propertyPath: string,
    value: number | string,
  ): Promise<UpdateResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.increment(
          this.target,
          conditions,
          propertyPath,
          value,
        )
      : await super.increment(conditions, propertyPath, value);
  }
  /**
   * Decrements some column by provided value of the entities matched given conditions.
   * @param conditions - Conditions to match the entity.
   * @param propertyPath - The field to increment.
   * @param value - The value to increment by (default is 1).
   * @returns {Promise<UpdateResult>}
   */
  async decrement(
    conditions: FindOptionsWhere<T>,
    propertyPath: string,
    value: number | string,
  ): Promise<UpdateResult> {
    const entityManager: EntityManager =
      this.databaseService.getEntityManager();

    return entityManager
      ? await entityManager.decrement(
          this.target,
          conditions,
          propertyPath,
          value,
        )
      : await super.decrement(conditions, propertyPath, value);
  }
}
