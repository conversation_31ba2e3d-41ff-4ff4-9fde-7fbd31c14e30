import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { PaymentUser } from '@app/shared/database/entities';
import { PaymentUserRepositoryInterface } from '@app/shared/database/repositories';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class PaymentUserRepository
  extends BaseRepository<PaymentUser>
  implements PaymentUserRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(PaymentUser, manager, databaseService);
  }
}
