import { Order, OrderStatus } from '../entities/order.entity';
import { BaseRepositoryInterface } from './base/base.repository.interface';

export interface OrderRepositoryInterface
  extends BaseRepositoryInterface<Order> {
  findById(id: string): Promise<Order | null>;
  findByUserId(userId: string): Promise<Order[]>;
  findByStatus(status: OrderStatus): Promise<Order[]>;
  updateStatus(id: string, status: OrderStatus): Promise<Order | null>;
  findPendingOrders(): Promise<Order[]>;
  findCompletedOrders(): Promise<Order[]>;
}
