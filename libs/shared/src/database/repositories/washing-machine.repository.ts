import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { BaseRepository } from './base/base.repository';
import { WashingMachine } from '../entities/washing-machine.entity';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class WashingMachineRepository extends BaseRepository<WashingMachine> {
  constructor(manager: EntityManager, databaseService: DatabaseService) {
    super(WashingMachine, manager, databaseService);
  }
}
