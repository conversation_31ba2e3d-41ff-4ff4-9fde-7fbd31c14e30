import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { Order, OrderStatus } from '../entities/order.entity';
import { OrderRepositoryInterface } from './order.interface.repository';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class OrderRepository
  extends BaseRepository<Order>
  implements OrderRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(Order, manager, databaseService);
  }

  async findById(id: string): Promise<Order | null> {
    return this.findOne({
      where: { id },
      relations: ['items'],
    });
  }

  async findByUserId(userId: string): Promise<Order[]> {
    return this.find({
      where: { userId },
      relations: ['items'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByStatus(status: OrderStatus): Promise<Order[]> {
    return this.find({
      where: { status },
      relations: ['items'],
      order: { createdAt: 'DESC' },
    });
  }

  async updateStatus(id: string, status: OrderStatus): Promise<Order | null> {
    await this.update(id, { status });
    return this.findById(id);
  }

  async findPendingOrders(): Promise<Order[]> {
    return this.find({
      where: { status: OrderStatus.PENDING },
      relations: ['items'],
      order: { createdAt: 'ASC' },
    });
  }

  async findCompletedOrders(): Promise<Order[]> {
    return this.find({
      where: { status: OrderStatus.COMPLETED },
      relations: ['items'],
      order: { createdAt: 'DESC' },
    });
  }
}
