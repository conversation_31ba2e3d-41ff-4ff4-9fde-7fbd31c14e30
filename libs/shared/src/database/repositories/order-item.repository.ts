import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { OrderItem } from '../entities/order-item.entity';
import { OrderItemRepositoryInterface } from './order-item.interface.repository';
import { BaseRepository } from './base/base.repository';
import { DatabaseService } from '../services/database.service';

@Injectable()
export class OrderItemRepository
  extends BaseRepository<OrderItem>
  implements OrderItemRepositoryInterface
{
  constructor(
    manager: EntityManager,
    protected databaseService: DatabaseService,
  ) {
    super(OrderItem, manager, databaseService);
  }

  async findById(id: string): Promise<OrderItem | null> {
    return this.findOne({
      where: { id },
      relations: ['order'],
    });
  }

  async findByOrderId(orderId: string): Promise<OrderItem[]> {
    return this.find({
      where: { orderId },
      order: { id: 'ASC' },
    });
  }

  async findByProductId(productId: string): Promise<OrderItem[]> {
    return this.find({
      where: { productId },
      relations: ['order'],
      order: { id: 'DESC' },
    });
  }

  async deleteByOrderId(orderId: string): Promise<boolean> {
    const result = await this.deleteBy({ orderId });
    return result.affected > 0;
  }
}
