import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  OneToOne,
  BeforeInsert,
  BeforeUpdate,
  BeforeRemove,
} from 'typeorm';
import {
  PaymentUser,
  PaymentTransaction,
  UserDeviceToken,
  UserNotification,
  UserSocial,
  ServiceSession,
  Order,
} from '@app/shared/database/entities';

export enum UserRole {
  ADMIN = 'admin',
  STAFF = 'staff',
  CUSTOMER = 'customer',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BLOCKED = 'blocked',
}

export enum USER_LANGUAGE {
  JA = 'ja',
  EN = 'en',
  VI = 'vi',
}

export enum UserGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

@Entity({ name: 'users' })
export class User {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({
    unique: true,
    nullable: false,
  })
  public username: string;

  @Column({
    unique: true,
    nullable: true,
  })
  public email: string;

  @Column({
    unique: true,
    nullable: true,
  })
  public phone: string;

  @Column({ nullable: false })
  public password: string;

  @Column({
    name: 'name',
    nullable: true,
  })
  public name: string;

  @Column({
    name: 'address',
    nullable: true,
    length: 255,
  })
  public address: string;

  @Column({
    name: 'birthday',
    type: 'timestamp',
    nullable: true,
  })
  public birthday: Date;

  @Column({ type: 'varchar', enum: USER_LANGUAGE, default: USER_LANGUAGE.EN })
  public language: USER_LANGUAGE;

  @Column({
    name: 'gender',
    type: 'varchar',
    enum: UserGender,
    nullable: true,
  })
  public gender: UserGender;

  @Column({
    name: 'avatar',
    nullable: true,
  })
  public avatar: string;

  @Column({ type: 'varchar', enum: UserRole, default: UserRole.CUSTOMER })
  public role: UserRole;

  @Column({ type: 'varchar', enum: UserStatus, default: UserStatus.ACTIVE })
  public status: UserStatus;

  @Column({
    name: 'is_allowed_notify',
    type: 'boolean',
    nullable: false,
    default: true,
  })
  public isAllowedNotify: boolean;

  @Column({ name: 'verified_at', type: 'timestamp', nullable: true })
  public verifiedAt: Date;

  @CreateDateColumn({
    name: 'created_at',
    nullable: false,
  })
  public createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    nullable: true,
  })
  public updatedAt: Date;

  @DeleteDateColumn({
    name: 'deleted_at',
    nullable: true,
  })
  public deletedAt: Date;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }

  @BeforeUpdate()
  setUpdatedAt() {
    this.updatedAt = new Date();
  }

  @BeforeRemove()
  setDeletedAt() {
    this.deletedAt = new Date();
  }

  @OneToMany(() => UserSocial, (userSocial: UserSocial) => userSocial.user)
  socials: UserSocial[];

  @OneToMany(
    () => UserDeviceToken,
    (userDeviceToken: UserDeviceToken) => userDeviceToken.user,
  )
  deviceTokens: UserDeviceToken[];

  @OneToMany(
    () => UserNotification,
    (userNotification: UserNotification) => userNotification.user,
  )
  notifications: UserNotification[];

  @OneToMany(() => ServiceSession, (serviceSession) => serviceSession.user)
  serviceSessions: ServiceSession[];

  @OneToMany(() => Order, (order) => order.user)
  orders: Order[];

  @OneToOne(() => PaymentUser, (paymentUser: PaymentUser) => paymentUser.user)
  payment: PaymentUser;

  @OneToMany(
    () => PaymentTransaction,
    (transaction: PaymentTransaction) => transaction.user,
  )
  transactions: PaymentTransaction[];
}
