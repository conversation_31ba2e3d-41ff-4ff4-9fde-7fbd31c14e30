import {
  Column,
  <PERSON><PERSON>ty,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
  ManyToOne,
  BeforeInsert,
  BeforeUpdate,
  BeforeRemove,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum USER_NOTIFICATION_TYPE {
  EMAIL = 'email',
  NOTIFICATION = 'notification',
  SMS = 'sms',
}
export enum USER_NOTIFICATION_STATUS {
  UNSENT = 'unsent',
  SENT = 'sent',
  FAILURE = 'failure',
}

@Entity({ name: 'user_notifications' })
export class UserNotification {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({
    name: 'user_id',
    nullable: false,
  })
  userId: string;

  @Column({
    name: 'device_token',
    nullable: false,
  })
  deviceToken: string;

  @Column({
    nullable: false,
  })
  title: string;

  @Column({
    nullable: false,
  })
  content: string;

  @Column({
    type: 'varchar',
    enum: USER_NOTIFICATION_TYPE,
    default: USER_NOTIFICATION_TYPE.NOTIFICATION,
  })
  type: USER_NOTIFICATION_TYPE;

  @Column({
    type: 'varchar',
    enum: USER_NOTIFICATION_STATUS,
    default: USER_NOTIFICATION_STATUS.UNSENT,
  })
  status: USER_NOTIFICATION_STATUS;

  @CreateDateColumn({
    name: 'created_at',
    nullable: false,
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    nullable: true,
  })
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.notifications)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }

  @BeforeUpdate()
  setUpdatedAt() {
    this.updatedAt = new Date();
  }
}
