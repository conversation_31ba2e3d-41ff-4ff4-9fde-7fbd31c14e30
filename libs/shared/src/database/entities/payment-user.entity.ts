import {
  Column,
  <PERSON>tity,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { PaymentTransaction } from './payment-transaction.entity';
import { PaymentTransactionBalance } from './payment-transaction-balance.entity';
import { User } from './user.entity';

@Entity({ name: 'payment_users' })
export class PaymentUser {
  @PrimaryColumn({ name: 'user_id', type: 'uuid' })
  public userId: string;

  @Column({
    name: 'total_points',
    type: 'integer',
    default: 0,
    nullable: false,
  })
  public totalPoints: number;

  @CreateDateColumn({
    name: 'created_at',
    nullable: false,
  })
  public createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    nullable: true,
  })
  public updatedAt: Date;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }

  @BeforeUpdate()
  setUpdatedAt() {
    this.updatedAt = new Date();
  }

  @OneToOne(() => User, (user) => user.payment)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
