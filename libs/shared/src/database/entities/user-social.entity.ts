import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
  BeforeRemove,
} from 'typeorm';
import { User } from '@app/shared/database/entities';

export enum SOCIAL_PROVIDER {
  FACEBOOK = 'facebook',
  GOOGLE = 'google',
  LINE = 'line',
  APPLE = 'apple',
}

@Entity({ name: 'user_socials' })
export class UserSocial {
  @PrimaryGeneratedColumn()
  public id: string;

  @Column({
    name: 'user_id',
    nullable: false,
  })
  public userId: string;

  @Column({
    name: 'social_id',
    nullable: false,
  })
  public socialId: string;

  @Column({
    type: 'varchar',
    enum: SOCIAL_PROVIDER,
    default: SOCIAL_PROVIDER.FACEBOOK,
  })
  public provider: SOCIAL_PROVIDER;

  @CreateDateColumn({
    name: 'created_at',
    nullable: true,
  })
  public createdAt: Date;

  @ManyToOne(() => User, (user: User) => user.socials)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }
}
