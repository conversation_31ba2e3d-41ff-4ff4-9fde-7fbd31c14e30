import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { User } from './user.entity';
import { PaymentUser } from './payment-user.entity';
import { PaymentTransaction } from './payment-transaction.entity';

@Entity({ name: 'payment_transaction_balances' })
export class PaymentTransactionBalance {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ name: 'user_id', type: 'uuid', nullable: false })
  public userId: string;

  @Column({ name: 'transaction_id', type: 'uuid', nullable: false })
  public transactionId: string;

  @Column({
    name: 'balance_before',
    type: 'integer',
    nullable: false,
    comment: 'Point balance before transaction',
  })
  public balanceBefore: number;

  @Column({
    name: 'balance_after',
    type: 'integer',
    nullable: false,
    comment: 'Point balance after transaction',
  })
  public balanceAfter: number;

  @Column({
    name: 'amount_change',
    type: 'integer',
    nullable: false,
    comment: 'Points changed (+/-)',
  })
  public amountChange: number;

  @CreateDateColumn({
    name: 'created_at',
    nullable: false,
  })
  public createdAt: Date;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }

  @ManyToOne(
    () => PaymentTransaction,
    (transaction) => transaction.balanceRecords,
    { onDelete: 'NO ACTION', onUpdate: 'NO ACTION' },
  )
  @JoinColumn({ name: 'transaction_id' })
  transaction: any;
}
