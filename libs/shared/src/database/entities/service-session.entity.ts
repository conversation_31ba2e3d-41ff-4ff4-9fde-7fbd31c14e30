import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { WashingMachine } from './washing-machine.entity';
import { MachineProgram } from './machine-program.entity';

export enum ServiceSessionStatus {
  WAITING = 'waiting',
  IN_USE = 'in_use',
  DONE = 'done',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('service_sessions')
export class ServiceSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', name: 'user_id' })
  userId: string;

  @Column({ type: 'uuid', name: 'order_id', nullable: true })
  orderId: string | null;

  @Column({ type: 'uuid', name: 'order_item_id', nullable: true })
  orderItemId: string | null;

  @Column({ type: 'uuid', name: 'machine_id', nullable: true })
  machineId: string | null;

  @Column({ type: 'uuid', name: 'machine_program_id', nullable: true })
  machineProgramId: string | null;

  @Column({
    type: 'varchar',
    length: 20,
    default: ServiceSessionStatus.WAITING,
    name: 'status',
  })
  status: ServiceSessionStatus;

  @Column({ type: 'timestamp', nullable: true, name: 'started_at' })
  startedAt: Date | null;

  @Column({ type: 'timestamp', nullable: true, name: 'estimated_end_time' })
  estimatedEndTime: Date | null;

  @Column({ type: 'timestamp', nullable: true, name: 'completed_at' })
  completedAt: Date | null;

  @Column({ type: 'timestamp', nullable: true, name: 'reminded_at' })
  remindedAt: Date | null;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.serviceSessions)
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Order and OrderItem relationships will be added later when those entities are created
  // @ManyToOne(() => Order, (order) => order.serviceSessions)
  // @JoinColumn({ name: 'order_id' })
  // order: Order | null;

  // @ManyToOne(() => OrderItem, (orderItem) => orderItem.serviceSessions)
  // @JoinColumn({ name: 'order_item_id' })
  // orderItem: OrderItem | null;

  @ManyToOne(
    () => WashingMachine,
    (washingMachine) => washingMachine.serviceSessions,
  )
  @JoinColumn({ name: 'machine_id' })
  washingMachine: WashingMachine | null;

  @ManyToOne(
    () => MachineProgram,
    (machineProgram) => machineProgram.serviceSessions,
  )
  @JoinColumn({ name: 'machine_program_id' })
  machineProgram: MachineProgram | null;
}
