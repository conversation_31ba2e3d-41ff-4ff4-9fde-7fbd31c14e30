import {
  Column,
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  PrimaryGeneratedColumn,
  ManyToOne,
  BeforeInsert,
  BeforeUpdate,
  BeforeRemove,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity({ name: 'user_device_tokens' })
export class UserDeviceToken {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({
    name: 'user_id',
    nullable: false,
  })
  userId: string;

  @Column({
    name: 'device_token',
    nullable: false,
  })
  deviceToken: string;

  @Column({
    name: 'device_code',
    nullable: true,
    length: 255,
  })
  deviceCode: string;

  @Column({
    name: 'device_type',
    nullable: true,
    length: 255,
  })
  deviceType: string;

  @Column({
    name: 'device_name',
    nullable: true,
    length: 255,
  })
  deviceName: string;

  @Column({
    name: 'app_version',
    nullable: true,
    length: 255,
  })
  appVersion: string;

  @Column({
    name: 'os_version',
    nullable: true,
    length: 255,
  })
  osVersion: string;

  @Column({
    name: 'device_os',
    nullable: true,
    length: 255,
  })
  deviceOs: string;

  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true,
    nullable: false,
  })
  isActive: boolean;

  @Column({
    name: 'last_active_at',
    type: 'timestamp',
    nullable: true,
  })
  lastActiveAt: Date;

  @CreateDateColumn({
    name: 'created_at',
    nullable: false,
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    nullable: true,
  })
  updatedAt: Date;

  @BeforeUpdate()
  setUpdatedAt() {
    this.updatedAt = new Date();
  }

  @ManyToOne(() => User, (user) => user.deviceTokens)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }
}
