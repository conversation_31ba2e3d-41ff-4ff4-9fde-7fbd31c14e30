import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  BeforeInsert,
} from 'typeorm';
import { User } from './user.entity';
import { PaymentUser } from './payment-user.entity';
import { PaymentTransactionBalance } from './payment-transaction-balance.entity';

export enum PaymentTransactionType {
  DIRECT_PAYMENT = 'DIRECT_PAYMENT',
  POINT_PAYMENT = 'POINT_PAYMENT',
  CASHBACK = 'CASHBACK',
  REFUND = 'REFUND',
  ADJUSTMENT = 'ADJUSTMENT',
}

export enum PaymentTransactionStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
}

@Entity({ name: 'payment_transactions' })
export class PaymentTransaction {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ name: 'user_id', type: 'uuid', nullable: false })
  public userId: string;

  @Column({ name: 'order_id', type: 'uuid', nullable: true })
  public orderId: string;

  @Column({
    name: 'type',
    type: 'varchar',
    enum: PaymentTransactionType,
    nullable: false,
  })
  public type: PaymentTransactionType;

  @Column({
    name: 'amount',
    type: 'integer',
    nullable: false,
    comment: 'Amount in original currency (VND)',
  })
  public amount: number;

  @Column({
    name: 'point',
    type: 'integer',
    nullable: false,
    default: 0,
    comment: 'Points equivalent of amount',
  })
  public point: number;

  @Column({
    name: 'exchange_rate',
    type: 'float',
    nullable: false,
    comment: 'VND/point exchange rate at transaction time',
  })
  public exchangeRate: number;

  @Column({
    name: 'currency',
    type: 'varchar',
    length: 10,
    nullable: false,
    default: 'VND',
  })
  public currency: string;

  @Column({
    name: 'payment_method',
    type: 'varchar',
    length: 50,
    nullable: false,
    comment: 'Payment method: points, momo, vnpay, cash',
  })
  public paymentMethod: string;

  @Column({
    name: 'gateway_transaction_id',
    type: 'varchar',
    length: 100,
    nullable: true,
    unique: true,
    comment: 'Gateway transaction reference',
  })
  public gatewayTransactionId: string;

  @Column({
    name: 'status',
    type: 'varchar',
    enum: PaymentTransactionStatus,
    nullable: false,
    default: PaymentTransactionStatus.PENDING,
  })
  public status: PaymentTransactionStatus;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  public description: string;

  @Column({
    name: 'gateway_response',
    type: 'jsonb',
    nullable: true,
    comment: 'Gateway response data',
  })
  public gatewayResponse: Record<string, any>;

  @CreateDateColumn({
    name: 'created_at',
    nullable: false,
  })
  public createdAt: Date;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
  }

  @ManyToOne(() => User, (user) => user.transactions)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => PaymentTransactionBalance, (balance) => balance.transaction)
  balanceRecords: PaymentTransactionBalance[];
}
