import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import appConfig from '../config/app.config';
import { AppLogger } from '@app/shared/logger/app.logger';
import { CacheModule } from '@app/cache';

@Global()
@Module({
  imports: [
    // Global config
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        process.env.NODE_ENV === 'test' ? ['.env', 'env.test'] : '.env',
      load: [appConfig],
      cache: true,
    }),
    CacheModule.forRoot(),
  ],
  providers: [AppLogger],
  exports: [AppLogger],
})
export class BootstrapModule {}
