import { registerDecorator, ValidationArguments } from 'class-validator';
import { getPlainText } from '@app/shared/helpers/string.helper';

export function MaxLengthWithoutRichText(max: number) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: 'MaxLengthWithoutRichText',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [max],
      validator: {
        validate(value: string, args: ValidationArguments): boolean {
          const plainText: string = getPlainText(value);
          const maxLength = args.constraints[0];
          return plainText.length <= maxLength;
        },
        defaultMessage(args: ValidationArguments): string {
          return `${args.property} must be shorter than or equal to ${max} characters`;
        },
      },
    });
  };
}
