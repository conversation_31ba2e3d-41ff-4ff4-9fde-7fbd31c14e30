import { registerDecorator, ValidationArguments } from 'class-validator';
import { PHONE_NUMBER_REGEX } from '../constants';

export function IsPhoneNumber() {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: 'IsPhoneNumber',
      target: object.constructor,
      propertyName: propertyName,
      validator: {
        validate(value: string, args: ValidationArguments): boolean {
          return PHONE_NUMBER_REGEX.test(value);
        },
        defaultMessage(args: ValidationArguments): string {
          return `${args.property} must be a valid phone number`;
        },
      },
    });
  };
}
