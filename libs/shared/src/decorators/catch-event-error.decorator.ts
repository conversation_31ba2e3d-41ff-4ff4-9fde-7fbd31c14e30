import { releaseQuery<PERSON>unner } from '../helpers/database.helper';

export function CatchEventError(): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: TypedPropertyDescriptor<any>,
  ) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        await originalMethod.apply(this, args);
      } catch (error) {
        console.error(error);
        if (this.logger) {
          this.logger.error(`Error: ${error.message}`, error);
        } else {
          console.error(`Error: ${error.message}`, error);
        }

        if (this.cls) {
          // release or cleanup
          await releaseQueryRunner(this.cls.get('queryRunner'));
          this.cls.set('queryRunner', null);
        }
      }
    };

    return descriptor;
  };
}
