import { applyDecorators } from '@nestjs/common';
import { ApiOkResponse, ApiQuery, getSchemaPath } from '@nestjs/swagger';
import { ResponseOkDto } from '@app/shared/dto/response-ok.dto';

export function ApiDocsPagination() {
  return applyDecorators(
    ApiQuery({
      name: 'skip',
      type: Number,
    }),
    ApiQuery({
      name: 'limit',
      type: Number,
    }),
  );
}

export function ApiResponsePagination(ItemDto: any) {
  return applyDecorators(
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseOkDto) },
          {
            properties: {
              data: {
                properties: {
                  items: {
                    type: 'array',
                    items: { $ref: getSchemaPath(ItemDto) },
                  },
                  count: {
                    type: 'number',
                  },
                  total: {
                    type: 'number',
                  },
                  currentPage: {
                    type: 'number',
                  },
                  totalPage: {
                    type: 'number',
                  },
                },
              },
            },
          },
        ],
      },
    }),
  );
}
