import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import appConfig from '../config/app.config';

export interface EmailValidationOptions {
  blockPlusSymbol?: boolean;
  // TODO: Can be extended with more options in the future
  // blockDots?: boolean;
  // allowedDomains?: string[];
  // blockedDomains?: string[];
  // maxLocalPartLength?: number;
  // minLocalPartLength?: number;
  // requireSubdomain?: boolean;
  // customPattern?: RegExp;
}

@ValidatorConstraint({ name: 'emailValidation', async: false })
@Injectable()
export class EmailValidationConstraint implements ValidatorConstraintInterface {
  validate(value: string, args: ValidationArguments): boolean {
    const options: EmailValidationOptions = args.constraints[0] || {};
    const config = appConfig();

    // Check plus symbol blocking (from options or environment config)
    const blockPlusSymbol =
      options.blockPlusSymbol ?? config.BLOCK_EMAIL_PLUS_SYMBOL;
    if (blockPlusSymbol && value.includes('+')) {
      return false;
    }

    // TODO: Can add more validation rules here in the future

    return true;
  }

  defaultMessage(args: ValidationArguments): string {
    const options: EmailValidationOptions = args.constraints[0] || {};
    const config = appConfig();
    const value = args.value as string;

    // Check which validation failed and return appropriate message
    const blockPlusSymbol =
      options.blockPlusSymbol ?? config.BLOCK_EMAIL_PLUS_SYMBOL;
    if (blockPlusSymbol && value.includes('+')) {
      return `${args.property} cannot contain the '+' symbol`;
    }

    // TODO: Can add more error messages here in the future

    return `${args.property} does not meet the email validation requirements`;
  }
}

export function IsEmailValidation(
  options?: EmailValidationOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: 'emailValidation',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [options],
      validator: EmailValidationConstraint,
    });
  };
}
