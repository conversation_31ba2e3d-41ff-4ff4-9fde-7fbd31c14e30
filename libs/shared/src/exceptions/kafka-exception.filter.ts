import { Catch, ArgumentsHost } from '@nestjs/common';
import { BaseRpcExceptionFilter, KafkaContext } from '@nestjs/microservices';
import { releaseQueryRunner } from '../helpers/database.helper';
import { ClsService } from 'nestjs-cls';
import { AppLogger } from '../logger/app.logger';
import { from } from 'rxjs';

@Catch()
export class KafkaExceptionFilter extends BaseRpcExceptionFilter {
  private readonly maxRetries: number = 2;

  constructor(
    private readonly logger: AppLogger,
    private readonly cls: ClsService,
  ) {
    super();
  }

  catch(exception: unknown, host: ArgumentsHost) {
    return from(
      (async () => {
        // Releases used database connection
        releaseQueryRunner(this.cls?.get('queryRunner') ?? null);
        this.cls?.set('queryRunner', null);
        this.logger.log('Clear CLS successfully');

        const kafkaContext = host.switchToRpc().getContext<KafkaContext>();
        const message = kafkaContext.getMessage();
        const currentRetryCount = this.getRetryCountFromContext(kafkaContext);

        if (currentRetryCount >= this.maxRetries) {
          this.logger.warn(
            `Max retries (${
              this.maxRetries
            }) exceeded for message: ${JSON.stringify(message)}`,
          );

          try {
            await this.commitOffset(kafkaContext);
          } catch (commitError) {
            this.logger.error('Failed to commit offset:', commitError);
          }
          return; // Stop propagating the exception
        }

        // If retry count is below the maximum, proceed with the default Exception Filter logic
        super.catch(exception, host);
      })(),
    );
  }

  private getRetryCountFromContext(context: KafkaContext): number {
    const headers = context.getMessage().headers || {};
    const retryHeader = headers['retryCount'] || headers['retry-count'];
    return retryHeader ? Number(retryHeader) : 0;
  }

  private async commitOffset(context: KafkaContext): Promise<void> {
    const consumer = context.getConsumer && context.getConsumer();
    if (!consumer) {
      throw new Error('Consumer instance is not available from KafkaContext.');
    }

    const topic = context.getTopic && context.getTopic();
    const partition = context.getPartition && context.getPartition();
    const message = context.getMessage();
    const offset = message.offset;

    if (!topic || partition === undefined || offset === undefined) {
      throw new Error(
        'Incomplete Kafka message context for committing offset.',
      );
    }

    await consumer.commitOffsets([
      {
        topic,
        partition,
        // When committing an offset, commit the next number (i.e., current offset + 1)
        offset: (Number(offset) + 1).toString(),
      },
    ]);
  }
}
