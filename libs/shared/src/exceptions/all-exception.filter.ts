import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AppLogger } from '@app/shared/logger/app.logger';
import { ClsService } from 'nestjs-cls';
import { releaseQueryRunner } from '../helpers/database.helper';
import { RpcException } from '@nestjs/microservices';
import { WsException } from '@nestjs/websockets';
import { throwError } from 'rxjs';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(
    private readonly logger: AppLogger,
    private readonly cls: ClsService,
  ) {}

  catch(exception: any, host: ArgumentsHost) {
    // Releases used database connection
    if (this.cls.isActive()) {
      releaseQueryRunner(this.cls?.get('queryRunner') ?? null);
      this.cls?.set('queryRunner', null);
      this.logger.log('Clear CLS successfully');
    }

    const contextType = host.getType();
    // === HTTP ===
    if (contextType === 'http') {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse();
      const request = ctx.getRequest();

      const status =
        exception instanceof HttpException
          ? exception.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR;

      const errorCode = exception?.response?.code ?? HttpStatus[status];

      const responseBody = {
        code: errorCode,
        ...(status === HttpStatus.INTERNAL_SERVER_ERROR
          ? {
              errors: exception?.response?.errors ?? {
                stack: exception?.stack,
              },
            }
          : {
              errors: exception?.response?.errors ?? {},
            }),
      };

      if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
        this.logger.error(JSON.stringify(responseBody));
      } else {
        this.logger.debug(JSON.stringify(responseBody));
      }
      response.status(status).json(responseBody);
      return;
    }

    // === RPC (Kafka, Redis, etc.) ===
    if (contextType === 'rpc') {
      const message =
        exception instanceof RpcException
          ? exception.getError()
          : exception?.message || 'Internal RPC Error';

      this.logger.error('RPC error:', message);
      return throwError(() => new RpcException(exception));
    }

    // === WebSocket ===
    if (contextType === 'ws') {
      const client = host.switchToWs().getClient();
      let event = 'exception';
      let errors: any = { stack: exception?.stack };

      if (exception instanceof WsException) {
        const errorMessage = exception.getError();
        if (errorMessage === 'Unauthorized') {
          event = 'unauthorized';
          errors = {};
        } else {
          errors = errorMessage;
        }
      }

      const response = { event, errors };
      this.logger.error(JSON.stringify(response));
      client.send(response);

      return;
    }

    // === Default ===
    this.logger.error(`Unhandled exception: ${JSON.stringify(exception)}`);
  }
}
