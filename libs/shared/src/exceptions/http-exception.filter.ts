import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Response } from 'express';
import { AppLogger } from '@app/shared/logger/app.logger';
import { isEmpty } from 'lodash';
import { releaseQueryRunner } from '../helpers/database.helper';
import { ClsService } from 'nestjs-cls';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly logger: AppLogger,
    private readonly cls: ClsService,
  ) {}

  catch(exception: any, host: ArgumentsHost) {
    // Releases used database connection
    releaseQueryRunner(this.cls?.get('queryRunner') ?? null);
    this.cls?.set('queryRunner', null);

    // Response to client
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    const errors =
      exception?.response?.errors ??
      (status === HttpStatus.INTERNAL_SERVER_ERROR
        ? { stack: exception?.stack }
        : {});
    const responseBody = {
      code: exception?.response?.code ?? HttpStatus[status],
      message: exception?.message ?? 'Internal Server Error',
      ...(!isEmpty(errors) ? { errors } : {}),
    };
    if (status == HttpStatus.INTERNAL_SERVER_ERROR) {
      this.logger.error(JSON.stringify(responseBody));
    } else {
      this.logger.debug(JSON.stringify(responseBody));
    }
    const redirectUrl = exception?.response?.redirectUrl;
    if (redirectUrl) {
      return response.redirect(redirectUrl);
    }

    response.status(status).json(responseBody);
  }
}
