import { ValidationError } from '@nestjs/common';
import { AppLogger } from '../logger/app.logger';
import { ClsService } from 'nestjs-cls';
import { releaseQueryRunner } from '../helpers/database.helper';

export * from './all-exception.filter';
export * from './http-exception.filter';
export * from './kafka-exception.filter';

export function formatValidationErrors(
  errors: ValidationError[],
  parentProperty = '',
) {
  const formattedErrors = {};

  errors.forEach((validationError: ValidationError) => {
    const propertyName: string = parentProperty
      ? `${parentProperty}.${validationError.property}`
      : validationError.property;

    if (validationError.constraints) {
      formattedErrors[propertyName] = Object.values(
        validationError.constraints,
      )[0];
    } else if (validationError.children.length > 0) {
      const childFormattedErrors = formatValidationErrors(
        validationError.children,
        propertyName,
      );
      Object.assign(formattedErrors, childFormattedErrors);
    }
  });

  return formattedErrors;
}

/**
 * Handles uncaught exceptions and unhandled rejections in the Node.js process.
 * It logs the error, releases the query runner from CLS, and clears the CLS context.
 * This is useful for ensuring that any resources are cleaned up properly when an unexpected error occurs.
 * @param logger
 * @param clsService
 */
export function handleUncaughtExceptions(
  logger: AppLogger,
  clsService: ClsService,
) {
  process.on('uncaughtException', (error) => {
    logger.error(`[Uncaught Exception]: ${JSON.stringify(error)}`);
    releaseQueryRunner(clsService.get('queryRunner'));
    clsService.set('queryRunner', null);
    logger.log('Clear CLS successfully');
  });

  process.on('unhandledRejection', (reason) => {
    logger.error(`[Unhandled Rejection]: ${JSON.stringify(reason)}`);
    releaseQueryRunner(clsService.get('queryRunner'));
    clsService.set('queryRunner', null);
    logger.log('Clear CLS successfully');
  });
}
