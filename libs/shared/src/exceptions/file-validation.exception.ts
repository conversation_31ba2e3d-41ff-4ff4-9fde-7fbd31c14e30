import { BadRequestException } from '@nestjs/common';
import { S3Service } from '@app/aws';
import { getPathFromUrl } from '@app/shared/helpers/url.helper';
import { uniq } from 'lodash';
import { ErrorResponse } from '../types';

export class FileValidationException extends BadRequestException {
  constructor(
    message: string | ErrorResponse,
    s3Service: S3Service,
    fileUrls: string[] = [],
  ) {
    if (fileUrls.length) {
      const filePathsToDelete = fileUrls.map((fileUrl) =>
        getPathFromUrl(fileUrl),
      );
      s3Service.deleteObjects(uniq(filePathsToDelete));
    }
    super(message);
  }
}
