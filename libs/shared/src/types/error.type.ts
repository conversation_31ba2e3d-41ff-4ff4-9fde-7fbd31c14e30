export type ErrorCode =
  // User related errors
  | 'EMAIL_EXISTED'
  | 'PHONE_EXISTED'
  | 'INFO_EXISTED'
  | 'USER_NOT_FOUND'
  | 'EMAIL_ALREADY_VERIFIED'
  | 'INVALID_VERIFY_EMAIL_CODE'
  | 'INVALID_VERIFICATION_CODE'
  | 'USER_EMAIL_NOT_FOUND'
  | 'EMAIL_NOT_EXISTS'
  | 'INVALID_PASSWORD'
  | 'USER_ALREADY_EXISTS'
  | 'USER_NOT_ACTIVATED'
  | 'USER_NOT_OWNER'
  | 'USER_SOCIAL_ACCOUNT_EXISTS'
  | 'USER_SOCIAL_ACCOUNT_NOT_FOUND'
  | 'USER_DEVICE_TOKEN_NOT_FOUND'
  | 'USER_NOTIFICATION_NOT_FOUND'
  | 'USER_SOCIAL_ACCOUNT_ALREADY_LINKED'
  | 'USER_SOCIAL_ACCOUNT_NOT_LINKED'
  | 'USER_SOCIAL_ACCOUNT_ALREADY_EXISTS'
  | 'USER_SOCIAL_ACCOUNT_ALREADY_LINKED_TO_OTHER_USER'
  | 'USER_DEVICE_TOKEN_ALREADY_EXISTS'
  | 'NEW_PASSWORD_SAME_AS_OLD'
  | 'USER_BLOCKED'
  | 'USER_IN_USE'

  // Auth related errors
  | 'INVALID_CREDENTIALS'
  | 'INVALID_REFRESH_TOKEN'
  | 'INVALID_ACCESS_TOKEN'
  | 'TOKEN_EXPIRED'
  | 'INVALID_SOCIAL_PROVIDER'
  | 'SOCIAL_ACCOUNT_NOT_FOUND'
  | 'SOCIAL_ACCOUNT_ALREADY_LINKED'
  | 'SOCIAL_ACCOUNT_NOT_LINKED'
  | 'INVALID_USERNAME'
  | 'INVALID_EMAIL'
  | 'INVALID_PHONE'
  | 'INVALID_CODE'
  | 'CODE_EXPIRED'
  | 'INVALID_OLD_PASSWORD'
  | 'PASSWORD_MISMATCH'

  // File related errors
  | 'FILE_REQUIRED'
  | 'FILE_TOO_LARGE'
  | 'INVALID_FILE_TYPE'
  | 'FILE_NOT_FOUND'
  | 'FILE_UPLOAD_FAILED'
  | 'FILE_SIZE_EXCEEDED'
  | 'UNSUPPORTED_FILE_TYPE'

  // URL related errors
  | 'URL_MISSING_PARAMETERS'
  | 'URL_EXPIRED'
  | 'URL_INVALID_SIGNATURE'

  // AWS related errors
  | 'AWS_SERVICE_ERROR'
  | 'S3_UPLOAD_FAILED'
  | 'CHIME_MEETING_CREATION_FAILED'
  | 'S3_DELETE_FAILED'
  | 'S3_GET_FAILED'

  // Payment related errors
  | 'UNSUPPORTED_GATEWAY'
  | 'INVALID_SIGNATURE'
  | 'GATEWAY_ERROR'
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'INVALID_AMOUNT'
  | 'INVALID_CURRENCY'
  | 'MISSING_REQUIRED_FIELDS'
  | 'PAYMENT_TRANSACTION_NOT_FOUND'
  | 'PAYMENT_ALREADY_PROCESSED'
  | 'PAYMENT_AMOUNT_MISMATCH'

  // Order related errors
  | 'ORDER_NOT_FOUND'
  | 'ORDER_ALREADY_CANCELLED'
  | 'ORDER_ALREADY_COMPLETED'
  | 'ORDER_ALREADY_PAID'
  | 'ORDER_CANNOT_BE_CANCELLED'
  | 'ORDER_CANNOT_BE_COMPLETED'
  | 'ORDER_CANNOT_BE_UPDATED'
  | 'ORDER_STATUS_INVALID'
  | 'ORDER_ITEMS_EMPTY'
  | 'ORDER_AMOUNT_INVALID'
  | 'ORDER_INSUFFICIENT_POINTS'

  // General errors
  | 'PRODUCT_NOT_FOUND'
  | 'STORE_NOT_FOUND'
  | 'UNKNOWN_ERROR'
  | 'INTERNAL_SERVER_ERROR'
  | 'FORBIDDEN'
  | 'NOT_FOUND'
  | 'BAD_REQUEST'
  | 'UNAUTHORIZED'
  | 'CONFLICT';

export interface ErrorResponse {
  code: ErrorCode;
  data?: Record<string, any>;
}
