import { User } from '@app/shared/database/entities';
import { UserDto } from '@app/user/dto';
import { Request } from 'express';
import { Session, SessionData } from 'express-session';

export interface RequestWithUser extends Request {
  user: (User | UserDto) & {
    isSocialLink?: boolean;
    redirectTo?: string;
  };

  session: Session &
    Partial<SessionData> & {
      user: User | UserDto;
      redirectTo?: string;
    };
}
