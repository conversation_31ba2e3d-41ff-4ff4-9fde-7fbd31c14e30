import {
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HttpStatus,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs';
import { plainToInstance } from 'class-transformer';
import { size } from 'lodash';

interface ClassConstructor {
  new (...args: any[]): object;
}

export class ResponseInterceptor implements NestInterceptor {
  constructor(private dto: ClassConstructor) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((response: any) => {
        let data = {};
        const request = context.switchToHttp().getRequest();
        const skip = parseInt(request?.query?.skip ?? 0);
        const limit = parseInt(request?.query?.limit ?? 0);
        if (!Array.isArray(response) && response?.items && limit) {
          const items = plainToInstance(this.dto, response?.items, {
            excludeExtraneousValues: true,
          });
          data = {
            count: response?.count,
            total: size(items),
            totalPage: Math.ceil(response?.count / limit),
            currentPage: skip / limit + 1,
            items,
          };
        } else {
          data = plainToInstance(this.dto, response, {
            excludeExtraneousValues: true,
          });
        }
        return {
          code: HttpStatus[HttpStatus.OK],
          message: 'Success',
          data,
        };
      }),
    );
  }
}
