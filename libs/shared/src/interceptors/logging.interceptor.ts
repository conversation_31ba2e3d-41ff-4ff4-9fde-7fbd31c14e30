import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { AppLogger } from '@app/shared/logger/app.logger';
import { v4 } from 'uuid';
import { KafkaContext } from '@nestjs/microservices';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(
    private readonly logger: AppLogger,
    private readonly cls: ClsService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    try {
      if (!this.cls.get('logId')) {
        this.cls.set('logId', v4());
      }
    } catch (error) {
      // Handle case where CLS context is not available (e.g., in tests)
      console.warn('CLS context not available, skipping logId setting');
    }

    if (context.getType() === 'http') {
      return this.logHttpCall(context, next);
    } else if (context.getType() === 'ws') {
      return this.logWSCall(context, next);
    } else if (context.getType() === 'rpc') {
      return this.logRpcCall(context, next);
    }
  }

  private logHttpCall(context: ExecutionContext, next: CallHandler) {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const userAgent = request.get('user-agent') || '';
    const { ip, method, path: url, query, body } = request;
    const userId = request.user?.user_id;
    const parameters = { ...query, ...body };
    const now = Date.now();

    this.logger.log(
      `${method} ${url} ${userId} ${userAgent} ${ip} ${JSON.stringify(
        parameters,
      )}: ${context.getClass().name} ${context.getHandler().name}`,
    );

    // Set header x-log-id into response
    try {
      const logId = this.cls.get('logId');
      if (logId) {
        response.setHeader('x-log-id', logId);
      }
    } catch (error) {
      // Handle case where CLS context is not available
      console.warn('CLS context not available, skipping x-log-id header');
    }

    return next.handle().pipe(
      tap(() => {
        const response = context.switchToHttp().getResponse();
        const { statusCode } = response;
        const content_length = response.get('content-length');

        this.logger.log(
          `${method} ${url} ${statusCode} ${content_length}: ${
            Date.now() - now
          }ms`,
        );
      }),
    );
  }

  private logWSCall(context: ExecutionContext, next: CallHandler) {
    const client = context.switchToWs().getClient();
    const data = context.switchToWs().getData();

    this.logger.log(
      `Client ID: ${client.id} - Received message: ${JSON.stringify(data)}`,
    );

    const now = Date.now();
    return next
      .handle()
      .pipe(
        tap(() =>
          this.logger.log(
            `Client ID: ${client.id} - Message processed in ${Date.now() - now}ms`,
          ),
        ),
      );
  }

  private logRpcCall(context: ExecutionContext, next: CallHandler) {
    const kafkaContext = context.switchToRpc().getContext() as KafkaContext;
    const message = kafkaContext.getMessage();
    const partition = kafkaContext.getPartition();
    const topic = kafkaContext.getTopic();

    this.logger.log(
      `${topic} ${partition} ${JSON.stringify(
        message,
      )}: ${context.getClass().name} ${context.getHandler().name}`,
    );

    const now = Date.now();
    return next
      .handle()
      .pipe(
        tap(() =>
          this.logger.log(`${topic} ${partition} : ${Date.now() - now}ms`),
        ),
      );
  }
}
