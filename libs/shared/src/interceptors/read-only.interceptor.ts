import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Inject,
} from '@nestjs/common';
import { from, mergeMap, Observable, tap } from 'rxjs';
import { DatabaseService } from '@app/shared/database/services/database.service';
import { DB_SERVICE_NAME } from '../database/constants';

export function ReadOnlyInterceptor(dbServiceName: DB_SERVICE_NAME) {
  @Injectable()
  class DynamicReadOnlyInterceptor implements NestInterceptor {
    constructor(
      @Inject(dbServiceName)
      readonly databaseService: DatabaseService,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
      return from(this.databaseService.createQueryRunner('slave')).pipe(
        mergeMap(() => {
          return next.handle().pipe(
            tap(() => {
              this.databaseService.release();
            }),
          );
        }),
      );
    }
  }

  return DynamicReadOnlyInterceptor;
}
