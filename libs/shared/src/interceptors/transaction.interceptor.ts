import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Inject,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { DatabaseService } from '@app/shared/database/services/database.service';
import { DB_SERVICE_NAME } from '../database/constants';

export function TransactionInterceptor(
  dbServiceName: DB_SERVICE_NAME = DB_SERVICE_NAME.BACKEND,
) {
  @Injectable()
  class DynamicTransactionInterceptor implements NestInterceptor {
    constructor(
      @Inject(dbServiceName) readonly databaseService: DatabaseService,
    ) {}

    intercept(context: ExecutionContext, next: <PERSON><PERSON>and<PERSON>): Observable<any> {
      this.databaseService.startTransaction();

      return next.handle().pipe(
        tap(async () => {
          await this.databaseService.commitTransaction();
          await this.databaseService.release();
        }),
        catchError(async (error) => {
          await this.databaseService.rollbackTransaction();
          throw error;
        }),
      );
    }
  }

  return DynamicTransactionInterceptor;
}
