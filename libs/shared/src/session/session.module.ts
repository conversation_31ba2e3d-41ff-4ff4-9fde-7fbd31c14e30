import { Module, MiddlewareConsumer, Inject } from '@nestjs/common';
import { CacheModule } from '@app/cache/cache.module';
import session from 'express-session';
import RedisStore from 'connect-redis';
import { Redis } from 'ioredis';
import { ConfigService } from '@nestjs/config';
import { REDIS_CLIENT } from '@app/cache/constants';

@Module({
  imports: [CacheModule],
})
export class SessionModule {
  constructor(
    private readonly configService: ConfigService,
    @Inject(REDIS_CLIENT) private readonly redisClient: Redis,
  ) {}

  configure(consumer: MiddlewareConsumer) {
    const redisStore = new RedisStore({
      client: this.redisClient,
      prefix: 'sessions:',
    });

    consumer
      .apply(
        session({
          store: redisStore,
          secret: this.configService.get('APP_KEY'),
          resave: false,
          saveUninitialized: false,
          cookie: {
            maxAge: 1000 * 60 * 60 * 24, // 1 day
          },
        }),
      )
      .forRoutes('*');
  }
}
