import { Modu<PERSON> } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigService } from '@nestjs/config';
import { CacheModule } from '@app/cache';
import { RedisClientService } from '@app/cache/services/redis-client.service';
import { ThrottlerStorageRedisService } from './throttler-storage-redis.service';

@Module({
  imports: [
    CacheModule,
    ThrottlerModule.forRootAsync({
      inject: [ConfigService, RedisClientService],
      useFactory: (
        configService: ConfigService,
        redisClientService: RedisClientService,
      ) => {
        const throttlerStorage = new ThrottlerStorageRedisService(
          redisClientService,
        );
        return {
          throttlers: [
            {
              name: 'default',
              ttl: configService.get('THROTTLE_TTL', 60), // 60 seconds
              limit: configService.get('THROTTLE_LIMIT', 10), // 10 requests per ttl
            },
            {
              name: 'strict',
              ttl: 60, // 1 minute
              limit: 5, // 5 requests per minute
            },
            {
              name: 'sendCode',
              ttl: 300, // 5 minutes
              limit: 3, // 3 requests per 5 minutes
            },
            {
              name: 'deleteAccount',
              ttl: 300, // 5 minutes
              limit: 2, // 2 requests per 5 minutes (ít hơn vì đây là xóa tài khoản)
            },
          ],
          storage: throttlerStorage,
          ignoreUserAgents: [
            // Ignore health check requests
            /health/i,
          ],
          skipIf: (context) => {
            return true;
            // // Skip throttling for admin users or specific IPs
            // const adminIps = configService.get('ADMIN_IPS', '').split(',');
            // const request = context.switchToHttp().getRequest();
            // const clientIp = request.ip || request.connection.remoteAddress;
            // return adminIps.includes(clientIp);
          },
        };
      },
    }),
  ],
  exports: [ThrottlerModule],
})
export class AppThrottlerModule {}
