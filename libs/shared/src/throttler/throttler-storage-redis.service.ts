import { Injectable } from '@nestjs/common';
import { ThrottlerStorage } from '@nestjs/throttler';
import { RedisClientService } from '@app/cache/services/redis-client.service';

interface ThrottlerStorageRecord {
  totalHits: number;
  timeToExpire: number;
  isBlocked: boolean;
  timeToBlockExpire: number;
}

@Injectable()
export class ThrottlerStorageRedisService implements ThrottlerStorage {
  constructor(private readonly redisService: RedisClientService) {}

  private getRedisClient() {
    return (this.redisService as any).client;
  }

  async increment(
    key: string,
    ttl: number,
    limit: number,
    blockDuration: number,
    throttlerName: string,
  ): Promise<ThrottlerStorageRecord> {
    const client = this.getRedisClient();
    const multi = client.multi();

    // Increment the counter
    multi.incr(key);

    // Set expiry if not already set
    multi.expire(key, ttl);

    const results = await multi.exec();
    const totalHits = results[0][1] as number;

    // Get TTL
    const timeToExpireSeconds = await client.ttl(key);

    // Check if blocked
    const isBlocked = totalHits > limit;
    const timeToBlockExpire = isBlocked ? blockDuration : 0;

    return {
      totalHits,
      timeToExpire: timeToExpireSeconds > 0 ? timeToExpireSeconds : 0,
      isBlocked,
      timeToBlockExpire,
    };
  }

  async get(key: string): Promise<ThrottlerStorageRecord> {
    const client = this.getRedisClient();
    const totalHits = await client.get(key);
    const timeToExpire = await client.ttl(key);

    return {
      totalHits: totalHits ? parseInt(totalHits, 10) : 0,
      timeToExpire: timeToExpire > 0 ? timeToExpire : 0,
      isBlocked: false,
      timeToBlockExpire: 0,
    };
  }

  async reset(key: string): Promise<void> {
    const client = this.getRedisClient();
    await client.del(key);
  }
}
