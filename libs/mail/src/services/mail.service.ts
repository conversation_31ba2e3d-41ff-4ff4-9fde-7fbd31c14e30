import { Injectable } from '@nestjs/common';
import { SESService } from '@app/aws/services/ses.service';

@Injectable()
export class MailService {
  constructor(private readonly sesService: SESService) {}

  /**
   * Send email.
   *
   * @param to - Email address to send to
   * @param subject - Email subject
   * @param content - Email content (HTML)
   * @param from - Optional sender email address
   */
  async send(
    to: string,
    subject: string,
    content: string,
    from?: string,
  ): Promise<boolean> {
    return this.sesService.sendEmail(to, subject, content, from);
  }
}
