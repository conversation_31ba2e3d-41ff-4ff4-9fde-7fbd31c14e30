import { Module, OnModuleInit } from '@nestjs/common';
import { MailService } from './services/mail.service';
import Handlerbar from 'handlebars';
import { SESService } from '@app/aws/services/ses.service';

@Module({
  providers: [MailService, SESService],
  exports: [MailService],
})
export class MailModule implements OnModuleInit {
  async onModuleInit() {
    if (!Handlerbar.helpers['gt']) {
      Handlerbar.registerHelper('gt', (a, b) => a > b);
    }
  }
}
