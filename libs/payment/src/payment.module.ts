import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';
import { PaymentService } from './payment.service';
import { PaymentGatewayFactory } from './factories/payment-gateway.factory';
import { PaymentTransactionService } from './services/payment-transaction.service';
import { PaymentBalanceService } from './services/payment-balance.service';
import { PaymentListener } from './listeners/payment.listener';
import { DatabaseModule } from '@app/shared/database';
import { NotificationModule } from '@app/notification';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
    HttpModule,
    DatabaseModule,
    NotificationModule,
  ],
  providers: [
    PaymentService,
    PaymentGatewayFactory,
    PaymentTransactionService,
    PaymentBalanceService,
    PaymentListener,
  ],
  exports: [PaymentService, PaymentTransactionService, PaymentBalanceService],
})
export class PaymentModule {}
