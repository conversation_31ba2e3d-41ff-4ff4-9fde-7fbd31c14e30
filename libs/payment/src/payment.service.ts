import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { ErrorResponse } from '@app/shared/types/error.type';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import {
  PaymentGatewayFactory,
  GatewayType,
} from './factories/payment-gateway.factory';
import {
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentCallbackData,
  PaymentStatusResponse,
} from './types';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { EXCHANGE_RATES, PaymentGatewayType, PaymentStatus } from './constants';
import {
  PaymentCreatedEvent,
  PaymentSuccessEvent,
  PaymentFailedEvent,
  PaymentCancelledEvent,
  PaymentPendingEvent,
} from './events/payment.events';
import { PaymentTransactionService } from './services/payment-transaction.service';
import { pick, get } from 'lodash';
import { PAYMENT_EVENTS } from './constants';
import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionType,
  User,
} from '@app/shared/database';
import { UpdatePaymentUserDto } from './dto/update-payment-user.dto';
import { PaymentBalanceService } from './services/payment-balance.service';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    private readonly paymentGatewayFactory: PaymentGatewayFactory,
    private readonly eventEmitter: EventEmitter2,
    private readonly paymentTransactionService: PaymentTransactionService,
    private readonly paymentBalanceService: PaymentBalanceService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Create payment using gateway specified in DTO
   */
  async createPayment(
    gatewayType: GatewayType,
    createPaymentDto: CreatePaymentDto,
    isMobile: boolean = false, // Changed from userAgent to isMobile
  ): Promise<CreatePaymentResponse> {
    try {
      this.logger.log(`Creating payment with gateway: ${gatewayType}`);

      // Validate gateway type
      if (!this.paymentGatewayFactory.isGatewaySupported(gatewayType)) {
        throw new BadRequestException({
          code: 'UNSUPPORTED_GATEWAY',
          data: {
            gatewayType,
            supportedGateways:
              this.paymentGatewayFactory.getSupportedGateways(),
          },
        } as ErrorResponse);
      }

      // Create gateway instance
      const gateway = this.paymentGatewayFactory.create(gatewayType);

      const apiUrl = this.configService.get<string>('APP_URL');

      // Use isMobile parameter to decide URL type
      const mobileAppConfig = this.configService.get('MOBILE_APP');

      let returnUrl: string;
      let cancelUrl: string;

      if (isMobile && mobileAppConfig) {
        // Use deeplink for mobile devices
        returnUrl =
          createPaymentDto.returnUrl ||
          `${mobileAppConfig.SUCCESS_DEEPLINK_TEMPLATE}${createPaymentDto.orderId}`;
        cancelUrl =
          createPaymentDto.cancelUrl ||
          `${mobileAppConfig.CANCEL_DEEPLINK_TEMPLATE}${createPaymentDto.orderId}`;
      } else {
        // Use API URLs for desktop/web
        returnUrl =
          createPaymentDto.returnUrl ||
          `${apiUrl}/payment/callback/success?orderId=${createPaymentDto.orderId}`;
        cancelUrl =
          createPaymentDto.cancelUrl ||
          `${apiUrl}/payment/callback/cancel?orderId=${createPaymentDto.orderId}`;
      }

      // Convert DTO to gateway request format
      const request: CreatePaymentRequest = {
        ...pick(createPaymentDto, [
          'orderId',
          'amount',
          'currency',
          'description',
          'exchangeRate',
          'customerInfo',
          'metadata',
        ]),
        returnUrl,
        cancelUrl,
      } as CreatePaymentRequest;

      // Create payment using gateway
      const response = await gateway.createPayment(request);

      // Emit payment created event if successful
      if (response.success && response.transactionId) {
        const event = new PaymentCreatedEvent(
          gatewayType,
          response.transactionId,
          createPaymentDto.userId,
          createPaymentDto.orderId,
          createPaymentDto.amount,
          createPaymentDto.currency,
          gatewayType,
          EXCHANGE_RATES[createPaymentDto.currency],
          createPaymentDto.description,
          response,
        );
        this.eventEmitter.emit(PAYMENT_EVENTS.CREATED, event);
      }

      this.logger.log(
        `Payment created successfully with transaction ID: ${response.transactionId}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to create payment with gateway ${gatewayType}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle payment callback from gateway
   */
  async handleCallback(
    gatewayType: GatewayType,
    callbackData: Record<string, any>,
  ): Promise<PaymentCallbackData> {
    try {
      this.logger.log(`Handling callback from gateway: ${gatewayType}`);

      // Validate gateway type
      if (!this.paymentGatewayFactory.isGatewaySupported(gatewayType)) {
        throw new BadRequestException({
          code: 'UNSUPPORTED_GATEWAY',
        } as ErrorResponse);
      }

      // Create gateway instance
      const gateway = this.paymentGatewayFactory.create(gatewayType);

      // Verify callback data
      const verifiedData = await gateway.verifyCallback(callbackData);

      // Get transaction details from database
      const transaction =
        await this.paymentTransactionService.getPaymentTransactionByGatewayId(
          verifiedData.transactionId,
        );

      if (!transaction) {
        throw new NotFoundException({
          code: 'PAYMENT_TRANSACTION_NOT_FOUND',
        } as ErrorResponse);
      }

      // Emit appropriate event based on status with full transaction data
      switch (verifiedData.status) {
        case 'success': {
          const successEvent = new PaymentSuccessEvent(
            gatewayType,
            verifiedData.transactionId,
            transaction.userId,
            transaction.orderId,
            verifiedData.amount,
            verifiedData.currency,
            callbackData,
          );
          this.eventEmitter.emit(PAYMENT_EVENTS.SUCCESS, successEvent);
          break;
        }
        case 'failed': {
          const failedEvent = new PaymentFailedEvent(
            gatewayType,
            verifiedData.transactionId,
            transaction.userId,
            transaction.orderId,
            verifiedData.amount,
            verifiedData.currency,
            callbackData,
          );
          this.eventEmitter.emit(PAYMENT_EVENTS.FAILED, failedEvent);
          break;
        }
        case 'pending': {
          const pendingEvent = new PaymentPendingEvent(
            gatewayType,
            verifiedData.transactionId,
            transaction.userId,
            transaction.orderId,
            verifiedData.amount,
            verifiedData.currency,
            callbackData,
          );
          this.eventEmitter.emit(PAYMENT_EVENTS.PENDING, pendingEvent);
          break;
        }
        case 'cancelled': {
          const cancelledEvent = new PaymentCancelledEvent(
            gatewayType,
            verifiedData.transactionId,
            transaction.userId,
            transaction.orderId,
            verifiedData.amount,
            verifiedData.currency,
            callbackData,
          );
          this.eventEmitter.emit(PAYMENT_EVENTS.CANCELLED, cancelledEvent);
          break;
        }
      }

      this.logger.log(
        `Callback verified successfully for transaction: ${verifiedData.transactionId}`,
      );

      return verifiedData;
    } catch (error) {
      this.logger.error(
        `Failed to handle callback from gateway ${gatewayType}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(
    gatewayType: GatewayType,
    transactionId: string,
  ): Promise<PaymentStatusResponse> {
    try {
      this.logger.log(
        `Checking payment status for transaction: ${transactionId}`,
      );

      // Validate gateway type
      if (!this.paymentGatewayFactory.isGatewaySupported(gatewayType)) {
        throw new BadRequestException({
          code: 'UNSUPPORTED_GATEWAY',
        } as ErrorResponse);
      }

      // Create gateway instance
      const gateway = this.paymentGatewayFactory.create(gatewayType);

      // Check payment status
      const status = await gateway.checkPaymentStatus(transactionId);

      if (status.status === PaymentStatus.SUCCESS) {
        const paymentTransaction: PaymentTransaction =
          await this.paymentTransactionService.getPaymentTransactionByGatewayId(
            transactionId,
          );

        if (
          paymentTransaction &&
          paymentTransaction?.status !== PaymentTransactionStatus.SUCCESS
        ) {
          const successEvent = new PaymentSuccessEvent(
            gatewayType,
            transactionId,
            paymentTransaction.userId,
            paymentTransaction.orderId,
            paymentTransaction.amount,
            paymentTransaction.currency,
            status,
          );
          this.eventEmitter.emit(PAYMENT_EVENTS.SUCCESS, successEvent);
        }
      }

      this.logger.log(`Payment status checked successfully: ${status.status}`);

      return status;
    } catch (error) {
      this.logger.error(
        `Failed to check payment status for transaction ${transactionId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get supported gateways
   */
  getSupportedGateways(): GatewayType[] {
    return this.paymentGatewayFactory.getSupportedGateways();
  }

  /**
   * Get gateway configuration
   */
  getGatewayConfig(gatewayType: GatewayType) {
    return this.paymentGatewayFactory.getGatewayConfig(gatewayType);
  }

  /**
   * Check if gateway supports deep link
   */
  supportsDeepLink(gatewayType: GatewayType): boolean {
    const gateway = this.paymentGatewayFactory.create(gatewayType);
    return gateway.supportsDeepLink();
  }

  /**
   * Check if gateway supports QR code
   */
  supportsQrCode(gatewayType: GatewayType): boolean {
    const gateway = this.paymentGatewayFactory.create(gatewayType);
    return gateway.supportsQrCode();
  }

  /**
   * Generate signature for testing purposes
   */
  generateSignature(
    gatewayType: GatewayType,
    data: Record<string, any>,
  ): string {
    const gateway = this.paymentGatewayFactory.create(gatewayType);
    return gateway.generateSignature(data);
  }

  /**
   * Verify signature for testing purposes
   */
  verifySignature(
    gatewayType: GatewayType,
    data: Record<string, any>,
    signature: string,
  ): boolean {
    const gateway = this.paymentGatewayFactory.create(gatewayType);
    return gateway.verifySignature(data, signature);
  }

  /**
   * Confirm cash payment (for staff use)
   */
  async confirmCashPayment(
    transactionId: string,
    amount: number,
    staffId: string,
    notes?: string,
  ): Promise<PaymentCallbackData> {
    try {
      this.logger.log(
        `Confirming cash payment for transaction: ${transactionId}`,
      );

      // Create callback data to simulate payment confirmation
      const callbackData = {
        transactionId,
        status: 'success',
        amount,
        currency: 'VND',
        gatewayResponse: {
          cashId: 'CASH_001',
          location: 'Main Cash Counter',
          staffId,
          notes,
          confirmedAt: new Date().toISOString(),
        },
      };

      // Handle callback using cash gateway
      const result = await this.handleCallback(
        PaymentGatewayType.CASH,
        callbackData,
      );

      this.logger.log(
        `Cash payment confirmed successfully. Transaction ID: ${transactionId}`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Cash payment confirmation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cancel cash payment (for staff use)
   */
  async cancelCashPayment(
    transactionId: string,
    staffId: string,
    reason?: string,
  ): Promise<PaymentCallbackData> {
    try {
      this.logger.log(
        `Cancelling cash payment for transaction: ${transactionId}`,
      );

      const callbackData = {
        transactionId,
        status: 'cancelled',
        amount: 0,
        currency: 'VND',
        gatewayResponse: {
          cashId: 'CASH_001',
          location: 'Main Cash Counter',
          staffId,
          reason,
          cancelledAt: new Date().toISOString(),
        },
      };

      // Handle callback using cash gateway
      const result = await this.handleCallback(
        PaymentGatewayType.CASH,
        callbackData,
      );

      this.logger.log(
        `Cash payment cancelled successfully. Transaction ID: ${transactionId}`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Cash payment cancellation failed: ${error.message}`);
      throw error;
    }
  }

  async updatePaymentUser(
    userId: string,
    payload: UpdatePaymentUserDto,
    requestUser: User,
  ) {
    const paymentUser =
      await this.paymentBalanceService.getOrCreatePaymentUser(userId);

    const { points, reason } = payload;

    const description = reason || `Adjustment for user points by operator`;

    if (paymentUser.totalPoints !== points) {
      this.logger.log(
        `[Update points] Adjustment for user ${userId} by operator ${requestUser.id}, from ${paymentUser.totalPoints} to ${points}`,
      );
      const transaction =
        await this.paymentTransactionService.createPaymentTransaction({
          userId: userId,
          amount: points,
          type: PaymentTransactionType.ADJUSTMENT,
          status: PaymentTransactionStatus.SUCCESS,
          point: points,
          exchangeRate: 0,
          currency: '',
          paymentMethod: '',
          description,
        });

      await this.paymentBalanceService.changeUserBalanceWithTransaction(
        userId,
        points - paymentUser.totalPoints,
        transaction.id,
        description,
      );
      this.logger.log(
        `[Update points] Adjustment for user ${userId} success, transaction id: ${transaction.id}`,
      );
    }

    return true;
  }
}
