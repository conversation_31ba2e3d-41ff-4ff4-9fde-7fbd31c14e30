import moment from 'moment';

export class PaymentCreatedEvent {
  constructor(
    public readonly gatewayType: string,
    public readonly transactionId: string,
    public readonly userId: string,
    public readonly orderId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly paymentMethod: string,
    public readonly exchangeRate: number,
    public readonly description?: string,
    public readonly gatewayResponse?: any,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class PaymentSuccessEvent {
  constructor(
    public readonly gatewayType: string,
    public readonly transactionId: string,
    public readonly userId: string,
    public readonly orderId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly callbackData: any,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class PaymentFailedEvent {
  constructor(
    public readonly gatewayType: string,
    public readonly transactionId: string,
    public readonly userId: string,
    public readonly orderId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly callbackData: any,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class PaymentCancelledEvent {
  constructor(
    public readonly gatewayType: string,
    public readonly transactionId: string,
    public readonly userId: string,
    public readonly orderId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly callbackData: any,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class PaymentPendingEvent {
  constructor(
    public readonly gatewayType: string,
    public readonly transactionId: string,
    public readonly userId: string,
    public readonly orderId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly callbackData: any,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class PaymentBalanceSuccessEvent {
  constructor(
    public readonly orderId: string,
    public readonly userId: string,
    public readonly transactionId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}
