import { ErrorCode } from '@app/shared/types/error.type';

// Payment Gateway Types
export type PaymentGatewayConfig = {
  credentials: Record<string, string>;
  endpoints: {
    payment: string;
    status: string;
    callback: string;
  };
  features: {
    deepLink: boolean;
    qrCode: boolean;
  };
};

export type CreatePaymentRequest = {
  orderId: string;
  amount: number;
  currency: string;
  description: string;
  returnUrl: string;
  cancelUrl: string;
  exchangeRate?: number;
  customerInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  metadata?: Record<string, any>;
};

export type CreatePaymentResponse = {
  success: boolean;
  paymentUrl?: string;
  qrCode?: string;
  deepLinkUrl?: string; // For mobile app deep links
  transactionId?: string;
  gatewayType?: string; // Payment gateway type
  amount?: number; // Payment amount
  currency?: string; // Payment currency
  expiresAt?: number; // QR code expiration timestamp
  gatewayToken?: string; // Gateway-specific token for app-to-app payment (e.g., zpTransToken for ZaloPay)
  error?: {
    code: ErrorCode;
    message: string;
  };
};

export type PaymentCallbackData = {
  transactionId: string;
  amount: number;
  currency: string;
  status: 'success' | 'failed' | 'pending' | 'cancelled';
  responseCode: string;
  responseMessage: string;
  signature: string;
  rawData: Record<string, any>;
};

export type PaymentStatusResponse = {
  success: boolean;
  status: 'success' | 'failed' | 'pending' | 'cancelled';
  transactionId: string;
  amount: number;
  currency: string;
  responseCode: string;
  responseMessage: string;
  error?: {
    code: ErrorCode;
    message: string;
  };
};
