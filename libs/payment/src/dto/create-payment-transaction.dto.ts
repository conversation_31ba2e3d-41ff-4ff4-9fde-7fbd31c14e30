import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  IsO<PERSON>,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  PaymentTransactionType,
  PaymentTransactionStatus,
} from '@app/shared/database/entities';

export class CreatePaymentTransactionDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Order ID (optional)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  orderId?: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: PaymentTransactionType,
    example: PaymentTransactionType.DIRECT_PAYMENT,
  })
  @IsEnum(PaymentTransactionType)
  type: PaymentTransactionType;

  @ApiProperty({
    description: 'Amount in original currency (VND)',
    example: 50000,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Points equivalent of amount',
    example: 50,
  })
  @IsNumber()
  point: number;

  @ApiProperty({
    description: 'VND/point exchange rate at transaction time',
    example: 1000,
  })
  @IsNumber()
  exchangeRate: number;

  @ApiProperty({
    description: 'Currency unit',
    example: 'VND',
  })
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'Payment method',
    example: 'momopay',
  })
  @IsString()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Gateway transaction reference',
    example: 'MOMO_123456789',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  gatewayTransactionId?: string;

  @ApiProperty({
    description: 'Transaction status',
    enum: PaymentTransactionStatus,
    example: PaymentTransactionStatus.PENDING,
  })
  @IsEnum(PaymentTransactionStatus)
  status: PaymentTransactionStatus;

  @ApiPropertyOptional({
    description: 'Transaction description',
    example: 'Payment for laundry service',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Gateway response data',
    example: { success: true, transactionId: 'MOMO_123456789' },
  })
  @IsOptional()
  @IsObject()
  gatewayResponse?: Record<string, any>;
}
