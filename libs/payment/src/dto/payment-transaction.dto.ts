import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>,
  IsO<PERSON>,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  PaymentTransactionType,
  PaymentTransactionStatus,
} from '@app/shared/database/entities';
import { Expose } from 'class-transformer';

export class PaymentTransactionDto {
  @ApiProperty({
    description: 'Transaction ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  userId: string;

  @ApiPropertyOptional({
    description: 'Order ID (optional)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  orderId?: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: PaymentTransactionType,
    example: PaymentTransactionType.DIRECT_PAYMENT,
  })
  @Expose()
  type: PaymentTransactionType;

  @ApiProperty({
    description: 'Amount in original currency (VND)',
    example: 50000,
  })
  @Expose()
  amount: number;

  @ApiProperty({
    description: 'Points equivalent of amount',
    example: 50,
  })
  @Expose()
  point: number;

  @ApiProperty({
    description: 'VND/point exchange rate at transaction time',
    example: 1000,
  })
  @Expose()
  exchangeRate: number;

  @ApiProperty({
    description: 'Currency unit',
    example: 'VND',
  })
  @Expose()
  currency: string;

  @ApiProperty({
    description: 'Payment method',
    example: 'momopay',
  })
  @Expose()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Gateway transaction reference',
    example: 'MOMO_123456789',
  })
  @Expose()
  @MaxLength(100)
  gatewayTransactionId?: string;

  @ApiProperty({
    description: 'Transaction status',
    enum: PaymentTransactionStatus,
    example: PaymentTransactionStatus.PENDING,
  })
  @Expose()
  status: PaymentTransactionStatus;

  @ApiPropertyOptional({
    description: 'Transaction description',
    example: 'Payment for laundry service',
  })
  @Expose()
  description?: string;

  @ApiPropertyOptional({
    description: 'Gateway response data',
    example: { success: true, transactionId: 'MOMO_123456789' },
  })
  @Expose()
  gatewayResponse?: Record<string, any>;
}
