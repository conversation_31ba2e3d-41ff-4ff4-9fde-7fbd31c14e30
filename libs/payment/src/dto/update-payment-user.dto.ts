import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  Min,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';

export class UpdatePaymentUserDto {
  @ApiProperty({
    description: 'Points',
    example: 100,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  points: number;

  @ApiPropertyOptional({
    description: 'Reason',
    example: 'Update points',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
