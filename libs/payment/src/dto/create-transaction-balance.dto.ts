import { IsString, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTransactionBalanceDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Transaction ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  transactionId: string;

  @ApiProperty({
    description: 'Point balance before transaction',
    example: 100,
  })
  @IsNumber()
  balanceBefore: number;

  @ApiProperty({
    description: 'Point balance after transaction',
    example: 150,
  })
  @IsNumber()
  balanceAfter: number;

  @ApiProperty({
    description: 'Points changed (+/-)',
    example: 50,
  })
  @IsNumber()
  amountChange: number;

  @ApiProperty({
    description: 'Description of the balance change',
    example: 'Points added from payment gateway transaction',
    required: false,
  })
  @IsString()
  description?: string;
}
