import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum } from 'class-validator';
import { ErrorCode } from '@app/shared/types/error.type';
import { PaymentGatewayType, PAYMENT_GATEWAY_TYPES } from '../constants';

export class PaymentErrorDto {
  @ApiProperty({ example: 'GATEWAY_ERROR' })
  code: ErrorCode;

  @ApiProperty({ example: 'Payment gateway error occurred' })
  message: string;
}

export class CreatePaymentResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({
    required: false,
    example: 'https://payment.gateway.com/pay?token=abc123',
  })
  paymentUrl?: string;

  @ApiProperty({
    required: false,
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  })
  qrCode?: string;

  @ApiProperty({ required: false, example: 'MOMO_1703123456789_abc123def' })
  transactionId?: string;

  @ApiProperty({
    required: false,
    example: 'ZP123456789',
    description:
      'Gateway-specific token for app-to-app payment (e.g., zpTransToken for ZaloPay, accessToken for MoMo)',
  })
  gatewayToken?: string;

  @ApiProperty({
    required: false,
    example: 'zalopay://payment?token=ZP123456789',
    description: 'Deep link URL for mobile app payment',
  })
  deepLinkUrl?: string;

  @ApiProperty({
    required: false,
    example: 'zalopay',
    description: 'Payment gateway type',
  })
  gatewayType?: string;

  @ApiProperty({
    required: false,
    example: 100000,
    description: 'Payment amount',
  })
  amount?: number;

  @ApiProperty({
    required: false,
    example: 'VND',
    description: 'Payment currency',
  })
  currency?: string;

  @ApiProperty({
    required: false,
    example: 1703123456789,
    description: 'Expiration timestamp',
  })
  expiresAt?: number;

  @ApiProperty({ required: false, type: PaymentErrorDto })
  error?: PaymentErrorDto;
}

export class CheckPaymentStatusDto {
  @IsEnum(PaymentGatewayType)
  @ApiProperty({
    enum: PaymentGatewayType,
    example: PaymentGatewayType.MOMOPAY,
    description: 'Payment gateway used',
  })
  gateway: PaymentGatewayType;

  @IsString()
  @ApiProperty({
    example: 'MOMO_1703123456789_abc123def',
    description: 'Transaction ID to check',
  })
  transactionId: string;
}

export class PaymentStatusResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({
    enum: ['success', 'failed', 'pending', 'cancelled'],
    example: 'success',
  })
  status: 'success' | 'failed' | 'pending' | 'cancelled';

  @ApiProperty({ example: 'MOMO_1703123456789_abc123def' })
  transactionId: string;

  @ApiProperty({ example: 100000 })
  amount: number;

  @ApiProperty({ example: 'VND' })
  currency: string;

  @ApiProperty({ example: '0' })
  responseCode: string;

  @ApiProperty({ example: 'Transaction successful' })
  responseMessage: string;

  @ApiProperty({ required: false, type: PaymentErrorDto })
  error?: PaymentErrorDto;
}
