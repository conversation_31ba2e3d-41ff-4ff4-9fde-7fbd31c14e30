import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  IsEnum,
  IsObject,
  ValidateNested,
  IsUUID,
  IsUrl,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { PaymentGatewayType, CURRENCY_TYPE } from '../constants';

export class CustomerInfoDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false, example: 'Nguyễn Văn A' })
  name?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false, example: '<EMAIL>' })
  email?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false, example: '0123456789' })
  phone?: string;
}

export class CreatePaymentDto {
  @ApiProperty({
    description: 'Payment gateway type',
    enum: PaymentGatewayType,
    example: PaymentGatewayType.ZALOPAY,
  })
  @IsEnum(PaymentGatewayType)
  gatewayType: PaymentGatewayType;

  @ApiProperty({
    description: 'Order ID',
    example: 'ORDER_123456789',
  })
  @IsString()
  orderId: string;

  @ApiHideProperty()
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'Payment amount',
    example: 50000,
  })
  @IsNumber()
  @Min(1)
  amount: number;

  @ApiProperty({
    description: 'Payment currency',
    enum: CURRENCY_TYPE,
    example: CURRENCY_TYPE.VND,
  })
  @IsEnum(CURRENCY_TYPE)
  currency: CURRENCY_TYPE;

  @ApiProperty({
    description: 'Payment description',
    example: 'Payment for order ORDER_123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description:
      'Return URL after successful payment (can be HTTP URL or deeplink)',
    examples: {
      httpUrl: {
        summary: 'HTTP URL',
        value: 'https://example.com/payment/success',
      },
      deeplink: {
        summary: 'Deeplink',
        value: 'niinuma-laundry://payment/success',
      },
    },
    required: false,
  })
  @IsOptional()
  @IsString()
  returnUrl?: string;

  @ApiProperty({
    description:
      'Cancel URL when payment is cancelled (can be HTTP URL or deeplink)',
    examples: {
      httpUrl: {
        summary: 'HTTP URL',
        value: 'https://example.com/payment/cancel',
      },
      deeplink: {
        summary: 'Deeplink',
        value: 'niinuma-laundry://payment/cancel',
      },
    },
    required: false,
  })
  @IsOptional()
  @IsString()
  cancelUrl?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerInfoDto)
  @ApiProperty({ required: false, type: CustomerInfoDto })
  customerInfo?: CustomerInfoDto;

  @IsOptional()
  @IsObject()
  @ApiProperty({
    required: false,
    example: { orderType: 'laundry', storeId: 'STORE_001' },
  })
  metadata?: Record<string, any>;
}
