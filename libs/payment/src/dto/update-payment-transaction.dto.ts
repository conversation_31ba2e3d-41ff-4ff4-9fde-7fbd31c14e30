import { IsOptional, IsEnum, IsObject } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentTransactionStatus } from '@app/shared/database/entities';

export class UpdatePaymentTransactionDto {
  @ApiPropertyOptional({
    description: 'Transaction status',
    enum: PaymentTransactionStatus,
    example: PaymentTransactionStatus.SUCCESS,
  })
  @IsOptional()
  @IsEnum(PaymentTransactionStatus)
  status?: PaymentTransactionStatus;

  @ApiPropertyOptional({
    description: 'Gateway response data',
    example: { success: true, transactionId: 'MOMO_123456789' },
  })
  @IsOptional()
  @IsObject()
  gatewayResponse?: Record<string, any>;
}
