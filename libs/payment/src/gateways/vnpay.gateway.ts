import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { createHmac } from 'crypto';
import { firstValueFrom } from 'rxjs';
import { BaseGateway } from './base.gateway';
import { ErrorResponse, ErrorCode } from '@app/shared/types/error.type';
import {
  PaymentGatewayConfig,
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentCallbackData,
  PaymentStatusResponse,
} from '../types';
import { PaymentGatewayType, PaymentStatus, CURRENCY_TYPE } from '../constants';
import { omit } from 'lodash';
import { generateTransactionId, generateVNPayTransactionRef } from '../utils';

/**
 * VNPay Gateway Implementation
 *
 * Implements VNPay payment gateway integration according to official API documentation.
 * Supports payment creation via URL redirection, callback verification, and status checking.
 *
 * @see https://sandbox.vnpayment.vn/apis/docs/thanh-toan-pay/pay.html
 */
@Injectable()
export class VNPayGateway extends BaseGateway {
  logger = new Logger(VNPayGateway.name);
  constructor(
    config: PaymentGatewayConfig,
    private readonly httpService: HttpService,
  ) {
    super(config);
  }

  /**
   * Creates a new payment order with VNPay
   *
   * Generates a payment URL for redirection to VNPay payment gateway:
   * - Validates amount (must be positive integer)
   * - Formats amount in cents (amount * 100)
   * - Creates date in GMT+7 timezone (Vietnam time) with format: yyyymmddHHmmss
   * - Sorts parameters alphabetically before signature generation (required by VNPay)
   * - Uses generateSignature() method for HMAC-SHA512 signature generation
   * - Builds URL with query parameters for GET request
   * - Supports bank code selection via request.metadata.bankCode
   * - Note: VNPay does not support app-to-app payment (gatewayToken is null)
   *
   * Implementation follows official VNPay Node.js documentation:
   * - Uses sortObject() to sort parameters alphabetically
   * - Uses generateSignature() method for consistent signature generation
   * - Applies proper URL encoding for final payment URL
   * - Follows exact parameter order and format as official documentation
   *
   * @param request - Payment creation request containing order details
   * @returns Promise<CreatePaymentResponse> - Payment creation result with redirect URL
   *
   * @example
   * ```typescript
   * const result = await gateway.createPayment({
   *   orderId: 'ORDER_123',
   *   amount: 50000,
   *   currency: 'VND',
   *   description: 'Payment for order #123',
   *   returnUrl: 'https://yourapp.com/success',
   *   metadata: {
   *     bankCode: 'NCB' // Optional: specify bank code
   *   }
   * });
   *
   * if (result.success) {
   *   console.log('Payment URL:', result.paymentUrl);
   *   // Redirect user to this URL for payment
   *   window.location.href = result.paymentUrl;
   * }
   * ```
   *
   * @throws {BadRequestException} When payment creation fails
   */
  async createPayment(
    request: CreatePaymentRequest,
  ): Promise<CreatePaymentResponse> {
    try {
      const { tmnCode, hashSecret } = this.config.credentials;
      const { payment: endpoint } = this.config.endpoints;

      // Validate amount (must be integer)
      if (!Number.isInteger(request.amount) || request.amount <= 0) {
        return {
          success: false,
          error: {
            code: 'GATEWAY_ERROR' as ErrorCode,
            message: 'Amount must be a positive integer',
          },
        };
      }

      const transactionId = generateVNPayTransactionRef();

      // VNPay amount format: amount * 100 (cents format)
      const vnpAmount = request.amount * 100;

      // Create date in format: yyyymmddHHmmss (Vietnam timezone)
      const now = new Date();
      const vietnamTime = new Date(now.getTime() + 7 * 60 * 60 * 1000); // GMT+7
      const createDate = vietnamTime
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '');

      // Build parameters according to VNPay documentation
      const vnpParams: Record<string, any> = {
        vnp_Version: '2.1.0',
        vnp_Command: 'pay',
        vnp_TmnCode: tmnCode,
        vnp_Locale: 'vn',
        vnp_CurrCode: 'VND',
        vnp_TxnRef: transactionId,
        vnp_OrderInfo: this.sanitizeOrderInfo(request.description),
        vnp_OrderType: 'other',
        vnp_Amount: vnpAmount.toString(),
        vnp_ReturnUrl: request.returnUrl,
        vnp_IpAddr: '127.0.0.1', // Default IP address, can be overridden by request metadata
        vnp_CreateDate: createDate,
      };

      // Add bank code if provided in request metadata
      if (request.metadata?.bankCode) {
        vnpParams.vnp_BankCode = request.metadata.bankCode;
      }

      // Sort parameters alphabetically (required by VNPay)
      const sortedParams = this.sortObject(vnpParams);

      // Generate HMAC-SHA512 signature
      const signature = this.generateSignature(sortedParams);

      // Add signature to parameters
      sortedParams.vnp_SecureHash = signature;

      // Build final URL with all parameters
      const finalQueryString = Object.keys(sortedParams)
        .map((key) => `${key}=${encodeURIComponent(sortedParams[key])}`)
        .join('&');

      const paymentUrl = `${endpoint}?${finalQueryString}`;

      return {
        success: true,
        paymentUrl,
        transactionId,
        gatewayType: PaymentGatewayType.VNPAY,
        amount: request.amount,
        currency: request.currency || 'VND',
        expiresAt: Date.now() + 15 * 60 * 1000, // 15 minutes
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GATEWAY_ERROR' as ErrorCode,
          message: error.message,
        },
      };
    }
  }

  /**
   * Verifies payment callback data from VNPay
   *
   * Validates the callback signature and extracts payment information.
   * VNPay sends callback data with HMAC-SHA512 signature for security verification.
   *
   * @param data - Raw callback data received from VNPay
   * @returns Promise<PaymentCallbackData> - Verified payment callback data
   *
   * @example
   * ```typescript
   * const callbackData = {
   *   vnp_Amount: '5000000',
   *   vnp_BankCode: 'NCB',
   *   vnp_BankTranNo: 'VNP********',
   *   vnp_CardType: 'ATM',
   *   vnp_OrderInfo: 'Payment for order #123',
   *   vnp_PayDate: '**************',
   *   vnp_ResponseCode: '00',
   *   vnp_TmnCode: 'DEMOV210',
   *   vnp_TransactionNo: '********',
   *   vnp_TransactionStatus: '00',
   *   vnp_TxnRef: 'VNPAY_1234567890',
   *   vnp_SecureHash: 'abc123...'
   * };
   *
   * const verifiedData = await gateway.verifyCallback(callbackData);
   * console.log('Payment status:', verifiedData.status);
   * console.log('Amount:', verifiedData.amount); // 50000 (converted from cents)
   * ```
   *
   * @throws {BadRequestException} When signature verification fails or data is invalid
   */
  async verifyCallback(
    data: Record<string, any>,
  ): Promise<PaymentCallbackData> {
    try {
      const signature = data.vnp_SecureHash;
      const receivedData = omit(data, ['vnp_SecureHash', 'vnp_SecureHashType']);

      if (!this.verifySignature(receivedData, signature)) {
        throw new BadRequestException({
          code: 'INVALID_SIGNATURE',
        } as ErrorResponse);
      }

      const status = this.mapResponseCodeToStatus(data.vnp_ResponseCode);

      return {
        transactionId: data.vnp_TxnRef,
        amount: parseInt(data.vnp_Amount) / 100, // Convert from cents
        currency: data.vnp_CurrCode || CURRENCY_TYPE.VND,
        status,
        responseCode: data.vnp_ResponseCode,
        responseMessage: data.vnp_Message,
        signature,
        rawData: data,
      };
    } catch (error) {
      throw new BadRequestException({ code: 'GATEWAY_ERROR' } as ErrorResponse);
    }
  }

  /**
   * Checks the status of a payment transaction
   *
   * Queries VNPay API to get the current status of a payment transaction.
   * Uses generateSignature() method for HMAC-SHA512 signature generation.
   * Implementation follows official VNPay Node.js documentation for consistency.
   *
   * @param transactionId - The transaction ID to check
   * @returns Promise<PaymentStatusResponse> - Current payment status information
   *
   * @example
   * ```typescript
   * const status = await gateway.checkPaymentStatus('VNPAY_1234567890');
   *
   * if (status.success) {
   *   console.log('Status:', status.status); // 'success', 'failed', 'pending'
   *   console.log('Amount:', status.amount);
   *   console.log('Message:', status.responseMessage);
   * }
   * ```
   */
  async checkPaymentStatus(
    transactionId: string,
  ): Promise<PaymentStatusResponse> {
    try {
      const { tmnCode, hashSecret } = this.config.credentials;
      const { status: endpoint } = this.config.endpoints;

      // Create date in format: yyyymmddHHmmss (Vietnam timezone)
      const now = new Date();
      const vietnamTime = new Date(now.getTime() + 7 * 60 * 60 * 1000); // GMT+7
      const transDate = vietnamTime
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '');

      // Build parameters according to VNPay documentation
      const vnpParams: Record<string, any> = {
        vnp_RequestId: transDate,
        vnp_Version: '2.1.0',
        vnp_Command: 'querydr',
        vnp_TmnCode: tmnCode,
        vnp_TxnRef: transactionId,
        vnp_OrderInfo: 'Query transaction status',
        vnp_IpAddr: '127.0.0.1',
        vnp_TransactionDate: transDate,
        vnp_CreateDate: transDate,
      };

      // Make secure hash
      const hmac = createHmac('sha512', hashSecret);
      const signatureData = [
        vnpParams.vnp_RequestId,
        vnpParams.vnp_Version,
        vnpParams.vnp_Command,
        vnpParams.vnp_TmnCode,
        vnpParams.vnp_TxnRef,
        vnpParams.vnp_TransactionDate,
        vnpParams.vnp_CreateDate,
        vnpParams.vnp_IpAddr,
        vnpParams.vnp_OrderInfo,
      ].join('|');
      vnpParams.vnp_SecureHash = hmac
        .update(Buffer.from(signatureData, 'utf-8'))
        .digest('hex');

      this.logger.debug('endpoint: ' + endpoint);
      this.logger.debug('vnpParams: ' + JSON.stringify(vnpParams));

      // Make HTTP request to VNPay API for status check
      try {
        const response = await firstValueFrom(
          this.httpService.post(endpoint, vnpParams, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 30000, // 30 seconds timeout
          }),
        );
        this.logger.debug('response', response);
        if (response.data.vnp_ResponseCode === '00') {
          return {
            success: true,
            status: this.mapTransactionStatusToStatus(
              response.data.vnp_TransactionStatus,
            ),
            transactionId,
            amount: parseInt(response.data.vnp_Amount) / 100 || 0, // Convert from cents
            currency: response.data.vnp_CurrCode || CURRENCY_TYPE.VND,
            responseCode: response.data.vnp_ResponseCode,
            responseMessage:
              response.data.vnp_Message || 'Transaction processed successfully',
          };
        } else {
          return {
            success: false,
            status: PaymentStatus.FAILED,
            transactionId,
            amount: 0,
            currency: CURRENCY_TYPE.VND,
            responseCode: response.data.vnp_ResponseCode,
            responseMessage: response.data.vnp_Message || 'Transaction failed',
            error: {
              code: 'GATEWAY_ERROR' as ErrorCode,
              message: response.data.vnp_Message || 'VNPay status check failed',
            },
          };
        }
      } catch (error) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          transactionId,
          amount: 0,
          currency: CURRENCY_TYPE.VND,
          responseCode: 'ERROR',
          responseMessage: error.message,
          error: {
            code: 'NETWORK_ERROR' as ErrorCode,
            message:
              error.message || 'Network error when checking VNPay status',
          },
        };
      }
    } catch (error) {
      return {
        success: false,
        status: PaymentStatus.FAILED,
        transactionId,
        amount: 0,
        currency: CURRENCY_TYPE.VND,
        responseCode: 'ERROR',
        responseMessage: error.message,
        error: {
          code: 'GATEWAY_ERROR' as ErrorCode,
          message: error.message,
        },
      };
    }
  }

  /**
   * Sorts object keys alphabetically (required by VNPay for signature generation)
   *
   * @param obj - Object to sort
   * @returns Sorted object with alphabetically ordered keys
   */
  private sortObject(obj: Record<string, any>): Record<string, any> {
    const sorted: Record<string, any> = {};
    Object.keys(obj)
      .sort()
      .forEach((key) => {
        sorted[key] = obj[key];
      });
    return sorted;
  }

  /**
   * Generates HMAC-SHA512 signature for VNPay API requests
   *
   * Creates signature using specific format: Sort all key names alphabetically and concatenate
   * Format: key1=value1&key2=value2&...&keyN=valueN
   * Uses Buffer.from() and utf-8 encoding as per VNPay Node.js documentation
   *
   * @param data - Request data object containing payment information
   * @returns string - HMAC-SHA512 signature in hexadecimal format
   *
   * @example
   * ```typescript
   * const requestData = {
   *   vnp_Version: '2.1.0',
   *   vnp_Command: 'pay',
   *   vnp_TmnCode: 'DEMOV210',
   *   vnp_Amount: '5000000',
   *   vnp_CurrCode: 'VND',
   *   vnp_TxnRef: 'VNPAY_1234567890',
   *   vnp_OrderInfo: 'Payment for order #123',
   *   vnp_OrderType: 'other',
   *   vnp_Locale: 'vn',
   *   vnp_ReturnUrl: 'https://yourapp.com/success',
   *   vnp_CreateDate: '**************',
   *   vnp_IpAddr: '127.0.0.1'
   * };
   *
   * const signature = gateway.generateSignature(requestData);
   * console.log('Signature:', signature); // 128-character hex string
   * ```
   */
  generateSignature(data: Record<string, any>): string {
    const { hashSecret } = this.config.credentials;

    // Sort parameters alphabetically (required by VNPay)
    const sortedData = this.sortObject(data);

    // Create query string without encoding (as per VNPay documentation)
    const queryString = Object.keys(sortedData)
      .map((key) => `${key}=${encodeURIComponent(sortedData[key])}`)
      .join('&');

    // Generate HMAC-SHA512 signature using Buffer.from() and utf-8 encoding
    const hmac = createHmac('sha512', hashSecret);
    return hmac.update(Buffer.from(queryString, 'utf-8')).digest('hex');
  }

  /**
   * Verifies HMAC-SHA512 signature for VNPay callback data
   *
   * Validates that the received signature matches the expected signature
   * calculated from the callback data using generateSignature() method.
   *
   * @param data - Callback data received from VNPay (without signature fields)
   * @param signature - Signature received in the callback
   * @returns boolean - True if signature is valid, false otherwise
   *
   * @example
   * ```typescript
   * const callbackData = {
   *   vnp_Amount: '5000000',
   *   vnp_BankCode: 'NCB',
   *   vnp_BankTranNo: 'VNP********',
   *   vnp_CardType: 'ATM',
   *   vnp_OrderInfo: 'Payment for order #123',
   *   vnp_PayDate: '**************',
   *   vnp_ResponseCode: '00',
   *   vnp_TmnCode: 'DEMOV210',
   *   vnp_TransactionNo: '********',
   *   vnp_TransactionStatus: '00',
   *   vnp_TxnRef: 'VNPAY_1234567890'
   * };
   *
   * const receivedSignature = 'abc123...';
   * const isValid = gateway.verifySignature(callbackData, receivedSignature);
   *
   * if (isValid) {
   *   console.log('Signature is valid');
   * } else {
   *   console.log('Signature is invalid');
   * }
   * ```
   */
  verifySignature(data: Record<string, any>, signature: string): boolean {
    const expectedSignature = this.generateSignature(data);
    return expectedSignature === signature;
  }

  /**
   * Maps VNPay transaction status codes to internal payment status
   *
   * Converts VNPay's transaction status codes (vnp_TransactionStatus) to standardized payment status values.
   * This is different from response codes and represents the actual transaction state at VNPay system.
   * Based on official VNPay API documentation for querydr API.
   *
   * @see https://sandbox.vnpayment.vn/apis/docs/truy-van-hoan-tien/querydr&refund.html#Bang-ma-loi-PAY
   *
   * @param transactionStatus - VNPay transaction status code (string)
   * @returns PaymentStatus - Mapped payment status
   *
   * @example
   * ```typescript
   * const status = gateway.mapTransactionStatusToStatus('00'); // Returns 'success'
   * const status2 = gateway.mapTransactionStatusToStatus('01'); // Returns 'pending'
   * const status3 = gateway.mapTransactionStatusToStatus('02'); // Returns 'failed'
   * ```
   *
   * Transaction Status Mapping (from VNPay querydr API documentation):
   * SUCCESS:
   * - '00': Giao dịch thanh toán thành công
   * - '07': Giao dịch bị nghi ngờ gian lận
   * FAILED:
   * - '02': Giao dịch bị lỗi
   * - '09': GD Hoàn trả bị từ chối
   * PENDING:
   * - '01': Giao dịch chưa hoàn tất
   * - '04': Giao dịch đảo (Khách hàng đã bị trừ tiền tại Ngân hàng nhưng GD chưa thành công ở VNPAY)
   * - '05': VNPAY đang xử lý giao dịch này (GD hoàn tiền)
   * - '06': VNPAY đã gửi yêu cầu hoàn tiền sang Ngân hàng (GD hoàn tiền)
   */
  private mapTransactionStatusToStatus(
    transactionStatus: string,
  ): PaymentStatus {
    // According to VNPay querydr API documentation
    switch (transactionStatus) {
      // SUCCESS cases
      case '00':
        return PaymentStatus.SUCCESS; // Giao dịch thanh toán thành công
      case '07':
        return PaymentStatus.SUCCESS; // Giao dịch bị nghi ngờ gian lận (but still successful payment)

      // FAILED cases
      case '02':
        return PaymentStatus.FAILED; // Giao dịch bị lỗi
      case '09':
        return PaymentStatus.FAILED; // GD Hoàn trả bị từ chối

      // PENDING cases
      case '01':
        return PaymentStatus.PENDING; // Giao dịch chưa hoàn tất
      case '04':
        return PaymentStatus.PENDING; // Giao dịch đảo (Khách hàng đã bị trừ tiền tại Ngân hàng nhưng GD chưa thành công ở VNPAY)
      case '05':
        return PaymentStatus.PENDING; // VNPAY đang xử lý giao dịch này (GD hoàn tiền)
      case '06':
        return PaymentStatus.PENDING; // VNPAY đã gửi yêu cầu hoàn tiền sang Ngân hàng (GD hoàn tiền)

      default:
        return PaymentStatus.PENDING; // Unknown status, treat as pending
    }
  }

  /**
   * Maps VNPay response codes to internal payment status
   *
   * Converts VNPay's numeric response codes to standardized payment status values.
   * Based on official VNPay API documentation.
   *
   * @param responseCode - VNPay response code (string)
   * @returns PaymentStatus - Mapped payment status
   *
   * @example
   * ```typescript
   * const status = gateway.mapResponseCodeToStatus('00'); // Returns 'success'
   * const status2 = gateway.mapResponseCodeToStatus('07'); // Returns 'success' (suspicious but successful)
   * const status3 = gateway.mapResponseCodeToStatus('24'); // Returns 'failed'
   * ```
   *
   * Response Code Mapping:
   * - '00': Giao dịch thành công
   * - '07': Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)
   * - '09': Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng
   * - '10': Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần
   * - '11': Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch
   * - '12': Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa
   * - '13': Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP). Xin quý khách vui lòng thực hiện lại giao dịch
   * - '24': Giao dịch không thành công do: Khách hàng hủy giao dịch
   * - '51': Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch
   * - '65': Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày
   * - '75': Ngân hàng thanh toán đang bảo trì
   * - '79': Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định. Xin quý khách vui lòng thực hiện lại giao dịch
   * - '99': Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)
   */
  private mapResponseCodeToStatus(responseCode: string): PaymentStatus {
    // According to VNPay documentation
    switch (responseCode) {
      case '00':
        return PaymentStatus.SUCCESS; // Giao dịch thành công
      case '07':
        return PaymentStatus.SUCCESS; // Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)
      case '09':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng
      case '10':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần
      case '11':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch
      case '12':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa
      case '13':
        return PaymentStatus.FAILED; // Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP). Xin quý khách vui lòng thực hiện lại giao dịch
      case '24':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Khách hàng hủy giao dịch
      case '51':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch
      case '65':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày
      case '75':
        return PaymentStatus.FAILED; // Ngân hàng thanh toán đang bảo trì
      case '79':
        return PaymentStatus.FAILED; // Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định. Xin quý khách vui lòng thực hiện lại giao dịch
      case '99':
        return PaymentStatus.FAILED; // Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)
      default:
        return PaymentStatus.PENDING; // Unknown status
    }
  }

  private sanitizeOrderInfo(info: string): string {
    let clean = info.replace(/[^a-zA-Z0-9\s]/g, '');
    clean = clean.replace(/\s+/g, '+');
    return clean;
  }
}
