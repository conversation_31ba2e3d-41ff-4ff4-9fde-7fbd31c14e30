import { Injectable, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { createHmac } from 'crypto';
import { firstValueFrom } from 'rxjs';
import { BaseGateway } from './base.gateway';
import { ErrorResponse, ErrorCode } from '@app/shared/types/error.type';
import {
  PaymentGatewayConfig,
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentCallbackData,
  PaymentStatusResponse,
} from '../types';
import { PaymentGatewayType, PaymentStatus, CURRENCY_TYPE } from '../constants';
import { omit } from 'lodash';
import { generateSecureRandomString } from '../utils';

/**
 * ZaloPay Gateway Implementation v2
 *
 * Implements ZaloPay payment gateway integration according to official API v2 documentation.
 * Supports payment creation, callback verification, and status checking.
 *
 * @see https://docs.zalopay.vn/v2/general/overview.html
 */
@Injectable()
export class ZaloPayGateway extends BaseGateway {
  constructor(
    config: PaymentGatewayConfig,
    private readonly httpService: HttpService,
  ) {
    super(config);
  }

  /**
   * Creates a new payment order with ZaloPay v2
   *
   * Generates a payment request according to ZaloPay API v2 specification:
   * - Creates app_trans_id with format: yymmdd_Order-identifier (Vietnam timezone GMT+7)
   * - Formats item as JSON array with required fields
   * - Generates HMAC-SHA256 signature for security
   * - Validates app_time (must not exceed 15 minutes from payment time)
   * - Supports expire_duration_seconds (300-2592000 seconds)
   * - Returns gateway token for app-to-app payment integration
   *
   * @param request - Payment creation request containing order details
   * @returns Promise<CreatePaymentResponse> - Payment creation result with URLs, transaction info, and gatewayToken
   *
   * @example
   * ```typescript
   * const result = await gateway.createPayment({
   *   orderId: 'ORDER_123',
   *   amount: 50000,
   *   currency: 'VND',
   *   description: 'Payment for order #123',
   *   customerInfo: {
   *     name: 'John Doe',
   *     email: '<EMAIL>',
   *     phone: '0123456789'
   *   }
   * });
   *
   * if (result.success) {
   *   console.log('Payment URL:', result.paymentUrl);
   *   console.log('Transaction ID:', result.transactionId);
   *   console.log('Gateway Token:', result.gatewayToken); // For app-to-app payment
   * }
   * ```
   *
   * @throws {BadRequestException} When payment creation fails
   */
  async createPayment(
    request: CreatePaymentRequest,
  ): Promise<CreatePaymentResponse> {
    try {
      const { appId, key1, key2 } = this.config.credentials;
      const { payment: endpoint } = this.config.endpoints;

      // Generate app_trans_id with correct format: yymmdd_Order-identifier (Vietnam timezone GMT+7)
      const now = new Date();
      const vietnamTime = new Date(now.getTime() + 7 * 60 * 60 * 1000); // GMT+7
      const yymmdd = vietnamTime.toISOString().slice(2, 10).replace(/-/g, '');
      const orderIdentifier = generateSecureRandomString(9);
      const appTransId = `${yymmdd}_${orderIdentifier}`;

      // Validate app_time (must not be over 15 minutes from payment time)
      const appTime = Date.now();

      // Format item as JSON array according to ZaloPay specification
      const itemData = [
        {
          itemid: request.orderId || 'item_001',
          itemname: request.description || 'Payment Item',
          itemprice: request.amount,
          itemquantity: 1,
        },
      ];

      const requestData = {
        app_id: appId,
        app_user:
          request.customerInfo?.name ||
          request.customerInfo?.email ||
          'Customer',
        app_time: appTime,
        amount: request.amount,
        app_trans_id: appTransId,
        embed_data: JSON.stringify(request.metadata || {}),
        item: JSON.stringify(itemData),
        callback_url: this.config.endpoints.callback,
        description: request.description,
        phone: request.customerInfo?.phone || '',
        expire_duration_seconds: 900, // 15 minutes default
      };

      const signature = this.generateSignature(requestData);
      requestData['mac'] = signature;

      // Make HTTP request to ZaloPay API
      try {
        const response = await firstValueFrom(
          this.httpService.post(endpoint, requestData, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            timeout: 30000, // 30 seconds timeout
          }),
        );

        if (response.data.return_code === 1) {
          const paymentUrl = response.data.order_url;
          const gatewayToken = response.data.zp_trans_token; // ZaloPay transaction token for app-to-app
          const deepLinkUrl = `zalopay://payment?amount=${request.amount}&transactionId=${appTransId}&orderId=${request.orderId}`;

          return {
            success: true,
            paymentUrl,
            deepLinkUrl,
            transactionId: appTransId,
            gatewayType: PaymentGatewayType.ZALOPAY,
            amount: request.amount,
            currency: request.currency,
            expiresAt: Date.now() + 15 * 60 * 1000, // 15 minutes
            gatewayToken, // Gateway-specific token for app-to-app payment
          };
        } else {
          return {
            success: false,
            error: {
              code: 'GATEWAY_ERROR' as ErrorCode,
              message:
                response.data.return_message ||
                'ZaloPay payment creation failed',
            },
          };
        }
      } catch (error) {
        return {
          success: false,
          error: {
            code: 'NETWORK_ERROR' as ErrorCode,
            message:
              error.message || 'Network error when creating ZaloPay payment',
          },
        };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GATEWAY_ERROR' as ErrorCode,
          message: error.message,
        },
      };
    }
  }

  /**
   * Verifies payment callback data from ZaloPay v2
   *
   * Validates the callback signature and extracts payment information.
   * ZaloPay sends callback data with HMAC-SHA256 signature for security verification.
   *
   * @param data - Raw callback data received from ZaloPay
   * @returns Promise<PaymentCallbackData> - Verified payment callback data
   *
   * @example
   * ```typescript
   * const callbackData = {
   *   app_id: '553',
   *   app_trans_id: '240101_abc123',
   *   app_user: 'John Doe',
   *   amount: '50000',
   *   app_time: '1704067200000',
   *   embed_data: '{"orderId":"123"}',
   *   item: '[{"itemid":"123","itemname":"Product","itemprice":50000,"itemquantity":1}]',
   *   return_code: '1',
   *   return_message: 'Success',
   *   zp_trans_id: 'ZP123456789',
   *   mac: 'abc123...'
   * };
   *
   * const verifiedData = await gateway.verifyCallback(callbackData);
   * console.log('Payment status:', verifiedData.status);
   * console.log('Amount:', verifiedData.amount);
   * ```
   *
   * @throws {BadRequestException} When signature verification fails or data is invalid
   */
  async verifyCallback(
    data: Record<string, any>,
  ): Promise<PaymentCallbackData> {
    try {
      const signature = data.mac;
      const receivedData = omit(data, ['mac']);

      if (!this.verifySignature(receivedData, signature)) {
        throw new BadRequestException({
          code: 'INVALID_SIGNATURE',
        } as ErrorResponse);
      }

      const status = this.mapReturnCodeToStatus(data.return_code);

      return {
        transactionId: data.app_trans_id,
        amount: parseInt(data.amount),
        currency: CURRENCY_TYPE.VND,
        status,
        responseCode: data.return_code,
        responseMessage: data.return_message,
        signature,
        rawData: data,
      };
    } catch (error) {
      throw new BadRequestException({ code: 'GATEWAY_ERROR' } as ErrorResponse);
    }
  }

  /**
   * Checks the status of a payment transaction using ZaloPay v2 API
   *
   * Queries ZaloPay API v2 to get the current status of a payment transaction.
   * Uses specific signature format for status checking: appid|app_trans_id|timestamp
   *
   * @param transactionId - The transaction ID to check (format: yymmdd_identifier)
   * @returns Promise<PaymentStatusResponse> - Current payment status information
   *
   * @example
   * ```typescript
   * const status = await gateway.checkPaymentStatus('240101_abc123');
   *
   * if (status.success) {
   *   console.log('Status:', status.status); // 'success', 'failed', 'pending'
   *   console.log('Amount:', status.amount);
   *   console.log('Message:', status.responseMessage);
   * }
   * ```
   */
  async checkPaymentStatus(
    transactionId: string,
  ): Promise<PaymentStatusResponse> {
    try {
      const { appId, key1, key2 } = this.config.credentials;
      const { status: endpoint } = this.config.endpoints;

      const timestamp = Date.now(); // milliseconds
      const requestData = {
        app_id: appId,
        app_trans_id: transactionId,
        timestamp: timestamp,
        mac: '',
      };

      // For v2 status check, signature format is: appid|app_trans_id|key1
      const signatureData = `${appId}|${transactionId}|${key1}`;
      const signature = createHmac('sha256', key1)
        .update(signatureData)
        .digest('hex');

      requestData.mac = signature;

      // Make HTTP request to ZaloPay API v2 for status check
      try {
        const response = await firstValueFrom(
          this.httpService.post(endpoint, requestData, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            timeout: 30000, // 30 seconds timeout
          }),
        );

        if (response.data.return_code === 1) {
          return {
            success: true,
            status: this.mapReturnCodeToStatus(
              response.data.return_code.toString(),
            ),
            transactionId,
            amount: parseInt(response.data.amount) || 0,
            currency: CURRENCY_TYPE.VND,
            responseCode: response.data.return_code.toString(),
            responseMessage:
              response.data.return_message ||
              'Transaction processed successfully',
          };
        } else {
          return {
            success: false,
            status: PaymentStatus.FAILED,
            transactionId,
            amount: 0,
            currency: CURRENCY_TYPE.VND,
            responseCode: response.data.return_code.toString(),
            responseMessage:
              response.data.return_message || 'Transaction failed',
            error: {
              code: 'GATEWAY_ERROR' as ErrorCode,
              message:
                response.data.return_message || 'ZaloPay status check failed',
            },
          };
        }
      } catch (error) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          transactionId,
          amount: 0,
          currency: CURRENCY_TYPE.VND,
          responseCode: 'ERROR',
          responseMessage: error.message,
          error: {
            code: 'NETWORK_ERROR' as ErrorCode,
            message:
              error.message || 'Network error when checking ZaloPay status',
          },
        };
      }
    } catch (error) {
      return {
        success: false,
        status: PaymentStatus.FAILED,
        transactionId,
        amount: 0,
        currency: CURRENCY_TYPE.VND,
        responseCode: 'ERROR',
        responseMessage: error.message,
        error: {
          code: 'GATEWAY_ERROR' as ErrorCode,
          message: error.message,
        },
      };
    }
  }

  /**
   * Generates HMAC-SHA256 signature for ZaloPay API v2 requests
   *
   * Creates signature using specific format: appid|app_trans_id|appuser|amount|apptime|embeddata|item
   * Only specific fields are used for signature generation, not all request fields.
   *
   * @param data - Request data object containing payment information
   * @returns string - HMAC-SHA256 signature in hexadecimal format
   *
   * @example
   * ```typescript
   * const requestData = {
   *   app_id: '553',
   *   app_trans_id: '240101_abc123',
   *   app_user: 'John Doe',
   *   amount: 50000,
   *   app_time: 1704067200000,
   *   embed_data: '{"orderId":"123"}',
   *   item: '[{"itemid":"123","itemname":"Product","itemprice":50000,"itemquantity":1}]'
   * };
   *
   * const signature = gateway.generateSignature(requestData);
   * console.log('Signature:', signature); // 64-character hex string
   * ```
   */
  generateSignature(data: Record<string, any>): string {
    const { key1 } = this.config.credentials;

    // ZaloPay v2 signature format: appid|app_trans_id|appuser|amount|apptime|embeddata|item
    // Note: Only specific fields are used for signature, not all fields
    const signatureData = [
      data.app_id,
      data.app_trans_id,
      data.app_user,
      data.amount,
      data.app_time,
      data.embed_data,
      data.item,
    ].join('|');

    return createHmac('sha256', key1).update(signatureData).digest('hex');
  }

  /**
   * Verifies HMAC-SHA256 signature for ZaloPay v2 callback data
   *
   * Validates that the received signature matches the expected signature
   * calculated from the callback data using the same format as signature generation.
   *
   * @param data - Callback data received from ZaloPay (without mac field)
   * @param signature - Signature received in the callback
   * @returns boolean - True if signature is valid, false otherwise
   *
   * @example
   * ```typescript
   * const callbackData = {
   *   app_id: '553',
   *   app_trans_id: '240101_abc123',
   *   app_user: 'John Doe',
   *   amount: '50000',
   *   app_time: '1704067200000',
   *   embed_data: '{"orderId":"123"}',
   *   item: '[{"itemid":"123","itemname":"Product","itemprice":50000,"itemquantity":1}]'
   * };
   *
   * const receivedSignature = 'abc123...';
   * const isValid = gateway.verifySignature(callbackData, receivedSignature);
   *
   * if (isValid) {
   *   console.log('Signature is valid');
   * } else {
   *   console.log('Signature is invalid');
   * }
   * ```
   */
  verifySignature(data: Record<string, any>, signature: string): boolean {
    const { key1 } = this.config.credentials;

    // For callback verification, ZaloPay sends specific fields
    // Format: appid|app_trans_id|appuser|amount|apptime|embeddata|item
    const signatureData = [
      data.app_id,
      data.app_trans_id,
      data.app_user,
      data.amount,
      data.app_time,
      data.embed_data,
      data.item,
    ].join('|');

    const expectedSignature = createHmac('sha256', key1)
      .update(signatureData)
      .digest('hex');

    return expectedSignature === signature;
  }

  /**
   * Maps ZaloPay v2 return codes to internal payment status
   *
   * Converts ZaloPay's numeric return codes to standardized payment status values.
   * Based on official ZaloPay API v2 documentation.
   *
   * @param returnCode - ZaloPay return code (string)
   * @returns PaymentStatus - Mapped payment status
   *
   * @example
   * ```typescript
   * const status = gateway.mapReturnCodeToStatus('1'); // Returns 'success'
   * const status2 = gateway.mapReturnCodeToStatus('2'); // Returns 'failed'
   * const status3 = gateway.mapReturnCodeToStatus('6'); // Returns 'pending'
   * ```
   *
   * Return Code Mapping (v2):
   * - '1': Success
   * - '2': Failed
   * - '3': Processing (mapped to Failed)
   * - '4': Rejected (mapped to Failed)
   * - '5': Refunded (mapped to Failed)
   * - '6': Pending
   * - '7': Cancelled (mapped to Failed)
   * - '8': Expired (mapped to Failed)
   */
  private mapReturnCodeToStatus(returnCode: string): PaymentStatus {
    // According to ZaloPay v2 documentation
    switch (returnCode) {
      case '1':
        return PaymentStatus.SUCCESS; // Success
      case '2':
        return PaymentStatus.FAILED; // Failed
      case '3':
        return PaymentStatus.FAILED; // Processing
      case '4':
        return PaymentStatus.FAILED; // Rejected
      case '5':
        return PaymentStatus.FAILED; // Refunded
      case '6':
        return PaymentStatus.PENDING; // Pending
      case '7':
        return PaymentStatus.FAILED; // Cancelled
      case '8':
        return PaymentStatus.FAILED; // Expired
      default:
        return PaymentStatus.PENDING;
    }
  }
}
