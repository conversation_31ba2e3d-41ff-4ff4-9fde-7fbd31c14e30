import { Injectable, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { createHmac } from 'crypto';
import { firstValueFrom } from 'rxjs';
import { BaseGateway } from './base.gateway';
import { ErrorCode, ErrorResponse } from '@app/shared/types/error.type';
import { PaymentGatewayType, PaymentStatus, CURRENCY_TYPE } from '../constants';
import {
  PaymentGatewayConfig,
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentCallbackData,
  PaymentStatusResponse,
} from '../types';
import { omit } from 'lodash';
import { generateTransactionId } from '../utils';

/**
 * MoMo Gateway Implementation
 *
 * Implements MoMo payment gateway integration according to official API documentation.
 * Supports payment creation, callback verification, and status checking.
 *
 * @see https://developers.momo.vn/v3/docs/payment/api/wallet/onetime/
 */
@Injectable()
export class MoMoPayGateway extends BaseGateway {
  constructor(
    config: PaymentGatewayConfig,
    private readonly httpService: HttpService,
  ) {
    super(config);
  }

  /**
   * Creates a new payment order with MoMo
   *
   * Generates a payment request according to MoMo API specification:
   * - Validates amount (1,000 VND to 50,000,000 VND)
   * - Formats items as JSON array with required fields
   * - Generates HMAC-SHA256 signature for security
   * - Supports userInfo for customer notifications
   * - Includes QR code generation support
   *
   * @param request - Payment creation request containing order details
   * @returns Promise<CreatePaymentResponse> - Payment creation result with URLs and transaction info
   *
   * @example
   * ```typescript
   * const result = await gateway.createPayment({
   *   orderId: 'ORDER_123',
   *   amount: 50000,
   *   currency: 'VND',
   *   description: 'Payment for order #123',
   *   customerInfo: {
   *     name: 'John Doe',
   *     email: '<EMAIL>',
   *     phone: '0123456789'
   *   }
   * });
   *
   * if (result.success) {
   *   console.log('Payment URL:', result.paymentUrl);
   *   console.log('QR Code URL:', result.qrCode);
   *   console.log('Deep Link URL:', result.deepLinkUrl);
   * }
   * ```
   *
   * @throws {BadRequestException} When payment creation fails
   */
  async createPayment(
    request: CreatePaymentRequest,
  ): Promise<CreatePaymentResponse> {
    try {
      const { partnerCode, accessKey, secretKey } = this.config.credentials;
      const { payment: endpoint } = this.config.endpoints;

      // Validate amount (1,000 VND to 50,000,000 VND)
      if (request.amount < 1000 || request.amount > 50000000) {
        return {
          success: false,
          error: {
            code: 'GATEWAY_ERROR' as ErrorCode,
            message: 'Amount must be between 1,000 VND and 50,000,000 VND',
          },
        };
      }

      const requestId = generateTransactionId('MOMOPAY');

      // Format items according to MoMo specification
      const items = [
        {
          id: request.orderId || 'item_001',
          name: request.description || 'Payment Item',
          description: request.description || 'Payment for order',
          category: 'service',
          imageUrl: '',
          manufacturer: 'Laundry Service',
          price: request.amount,
          currency: CURRENCY_TYPE.VND,
          quantity: 1,
          unit: 'order',
          totalPrice: request.amount,
          taxAmount: 0,
        },
      ];

      // Format userInfo if customerInfo is provided
      const userInfo = request.customerInfo
        ? {
            name: request.customerInfo.name || '',
            phoneNumber: request.customerInfo.phone || '',
            email: request.customerInfo.email || '',
          }
        : undefined;

      const requestData = {
        partnerCode,
        requestId,
        amount: request.amount,
        orderId: request.orderId,
        orderInfo: request.description,
        redirectUrl: request.returnUrl,
        ipnUrl: this.config.endpoints.callback,
        requestType: 'captureWallet',
        extraData: request.metadata ? JSON.stringify(request.metadata) : '',
        items,
        userInfo,
        lang: 'vi', // Default to Vietnamese
        autoCapture: true,
      };

      const signature = this.generateSignature(requestData);
      requestData['signature'] = signature;

      // Make HTTP request to MoMo API
      try {
        const response = await firstValueFrom(
          this.httpService.post(endpoint, requestData, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 30000, // 30 seconds timeout
          }),
        );

        if (response.data.resultCode === '0') {
          const paymentUrl = response.data.payUrl;
          const deepLinkUrl =
            response.data.deeplink ||
            `momo://payment?amount=${request.amount}&transactionId=${requestId}&orderId=${request.orderId}`;
          const qrCodeUrl = response.data.qrCodeUrl;

          return {
            success: true,
            paymentUrl,
            deepLinkUrl,
            qrCode: qrCodeUrl,
            transactionId: requestId,
            gatewayType: PaymentGatewayType.MOMOPAY,
            amount: request.amount,
            currency: CURRENCY_TYPE.VND,
            expiresAt: Date.now() + 15 * 60 * 1000, // 15 minutes
          };
        } else {
          return {
            success: false,
            error: {
              code: 'GATEWAY_ERROR' as ErrorCode,
              message: response.data.message || 'MoMo payment creation failed',
            },
          };
        }
      } catch (error) {
        return {
          success: false,
          error: {
            code: 'NETWORK_ERROR' as ErrorCode,
            message:
              error.message || 'Network error when creating MoMo payment',
          },
        };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GATEWAY_ERROR' as ErrorCode,
          message: error.message,
        },
      };
    }
  }

  /**
   * Verifies payment callback data from MoMo
   *
   * Validates the callback signature and extracts payment information.
   * MoMo sends callback data with HMAC-SHA256 signature for security verification.
   *
   * @param data - Raw callback data received from MoMo
   * @returns Promise<PaymentCallbackData> - Verified payment callback data
   *
   * @example
   * ```typescript
   * const callbackData = {
   *   partnerCode: 'MOMOT5BZ20231213_TEST',
   *   orderId: 'ORDER_123',
   *   requestId: 'MOMOPAY_1234567890',
   *   amount: '50000',
   *   orderInfo: 'Payment for order #123',
   *   orderType: 'momo_wallet',
   *   transId: '4088878653',
   *   resultCode: '0',
   *   message: 'Successful.',
   *   payType: 'qr',
   *   signature: 'abc123...'
   * };
   *
   * const verifiedData = await gateway.verifyCallback(callbackData);
   * console.log('Payment status:', verifiedData.status);
   * console.log('Amount:', verifiedData.amount);
   * ```
   *
   * @throws {BadRequestException} When signature verification fails or data is invalid
   */
  async verifyCallback(
    data: Record<string, any>,
  ): Promise<PaymentCallbackData> {
    try {
      const signature = data.signature;
      const receivedData = omit(data, ['signature']);

      if (!this.verifySignature(receivedData, signature)) {
        throw new BadRequestException({
          code: 'INVALID_SIGNATURE',
        } as ErrorResponse);
      }

      const status = this.mapResultCodeToStatus(data.resultCode.toString());

      return {
        transactionId: data.orderId,
        amount: parseInt(data.amount),
        currency: CURRENCY_TYPE.VND,
        status,
        responseCode: data.resultCode,
        responseMessage: data.message,
        signature,
        rawData: data,
      };
    } catch (error) {
      throw new BadRequestException({ code: 'GATEWAY_ERROR' } as ErrorResponse);
    }
  }

  /**
   * Checks the status of a payment transaction
   *
   * Queries MoMo API to get the current status of a payment transaction.
   * Uses specific signature format for status checking with HMAC-SHA256.
   *
   * @param transactionId - The transaction ID to check
   * @returns Promise<PaymentStatusResponse> - Current payment status information
   *
   * @example
   * ```typescript
   * const status = await gateway.checkPaymentStatus('MOMOPAY_1234567890');
   *
   * if (status.success) {
   *   console.log('Status:', status.status); // 'success', 'failed', 'pending'
   *   console.log('Amount:', status.amount);
   *   console.log('Message:', status.responseMessage);
   * }
   * ```
   */
  async checkPaymentStatus(
    transactionId: string,
  ): Promise<PaymentStatusResponse> {
    try {
      const { partnerCode, accessKey, secretKey } = this.config.credentials;
      const { status: endpoint } = this.config.endpoints;

      const requestData = {
        partnerCode,
        accessKey,
        requestId: `STATUS_${Date.now()}`,
        orderId: transactionId,
        requestType: 'transactionStatus',
      };

      const signature = this.generateSignature(requestData);
      requestData['signature'] = signature;

      // Make HTTP request to MoMo API for status check
      try {
        const response = await firstValueFrom(
          this.httpService.post(endpoint, requestData, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 30000, // 30 seconds timeout
          }),
        );

        if (response.data.resultCode === 0) {
          return {
            success: true,
            status: this.mapResultCodeToStatus(
              response.data.resultCode.toString(),
            ),
            transactionId,
            amount: parseInt(response.data.amount) || 0,
            currency: CURRENCY_TYPE.VND,
            responseCode: response.data.resultCode.toString(),
            responseMessage:
              response.data.message || 'Transaction processed successfully',
          };
        } else {
          return {
            success: false,
            status: PaymentStatus.FAILED,
            transactionId,
            amount: 0,
            currency: CURRENCY_TYPE.VND,
            responseCode: response.data.resultCode.toString(),
            responseMessage: response.data.message || 'Transaction failed',
            error: {
              code: 'GATEWAY_ERROR' as ErrorCode,
              message: response.data.message || 'MoMo status check failed',
            },
          };
        }
      } catch (error) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          transactionId,
          amount: 0,
          currency: CURRENCY_TYPE.VND,
          responseCode: 'ERROR',
          responseMessage: error.message,
          error: {
            code: 'NETWORK_ERROR' as ErrorCode,
            message: error.message || 'Network error when checking MoMo status',
          },
        };
      }
    } catch (error) {
      return {
        success: false,
        status: PaymentStatus.FAILED,
        transactionId,
        amount: 0,
        currency: CURRENCY_TYPE.VND,
        responseCode: 'ERROR',
        responseMessage: error.message,
        error: {
          code: 'MOMOPAY_STATUS_ERROR' as ErrorCode,
          message: error.message,
        },
      };
    }
  }

  /**
   * Generates HMAC-SHA256 signature for MoMo API requests
   *
   * Creates signature using specific format: Sort all key names alphabetically and concatenate
   * Format: accessKey=$accessKey&amount=$amount&extraData=$extraData&ipnUrl=$ipnUrl&orderId=$orderId&orderInfo=$orderInfo&partnerCode=$partnerCode&redirectUrl=$redirectUrl&requestId=$requestId&requestType=$requestType
   *
   * @param data - Request data object containing payment information
   * @returns string - HMAC-SHA256 signature in hexadecimal format
   *
   * @example
   * ```typescript
   * const requestData = {
   *   partnerCode: 'MOMOT5BZ20231213_TEST',
   *   requestId: 'MOMOPAY_1234567890',
   *   amount: 50000,
   *   orderId: 'ORDER_123',
   *   orderInfo: 'Payment for order #123',
   *   redirectUrl: 'https://yourapp.com/success',
   *   ipnUrl: 'https://yourapp.com/callback',
   *   requestType: 'captureWallet',
   *   extraData: '{"orderId":"123"}'
   * };
   *
   * const signature = gateway.generateSignature(requestData);
   * console.log('Signature:', signature); // 64-character hex string
   * ```
   */
  generateSignature(data: Record<string, any>): string {
    const { secretKey } = this.config.credentials;

    // MoMo signature format: Sort all key names alphabetically and concatenate
    // accessKey=$accessKey&amount=$amount&extraData=$extraData&ipnUrl=$ipnUrl&orderId=$orderId&orderInfo=$orderInfo&partnerCode=$partnerCode&redirectUrl=$redirectUrl&requestId=$requestId&requestType=$requestType
    const sortedKeys = Object.keys(data).sort();
    const queryString = sortedKeys
      .map((key) => `${key}=${data[key]}`)
      .join('&');

    return createHmac('sha256', secretKey).update(queryString).digest('hex');
  }

  /**
   * Verifies HMAC-SHA256 signature for MoMo callback data
   *
   * Validates that the received signature matches the expected signature
   * calculated from the callback data using the same format as signature generation.
   *
   * @param data - Callback data received from MoMo (without signature field)
   * @param signature - Signature received in the callback
   * @returns boolean - True if signature is valid, false otherwise
   *
   * @example
   * ```typescript
   * const callbackData = {
   *   partnerCode: 'MOMOT5BZ20231213_TEST',
   *   orderId: 'ORDER_123',
   *   requestId: 'MOMOPAY_1234567890',
   *   amount: '50000',
   *   orderInfo: 'Payment for order #123',
   *   orderType: 'momo_wallet',
   *   transId: '4088878653',
   *   resultCode: '0',
   *   message: 'Successful.',
   *   payType: 'qr',
   *   responseTime: '1721720663942',
   *   extraData: '{"orderId":"123"}'
   * };
   *
   * const receivedSignature = 'abc123...';
   * const isValid = gateway.verifySignature(callbackData, receivedSignature);
   *
   * if (isValid) {
   *   console.log('Signature is valid');
   * } else {
   *   console.log('Signature is invalid');
   * }
   * ```
   */
  verifySignature(data: Record<string, any>, signature: string): boolean {
    const expectedSignature = this.generateSignature(data);
    return expectedSignature === signature;
  }

  /**
   * Maps MoMo result codes to internal payment status
   *
   * Converts MoMo's numeric result codes to standardized payment status values.
   * Based on official MoMo API documentation.
   *
   * @param resultCode - MoMo result code (string)
   * @returns PaymentStatus - Mapped payment status
   *
   * @example
   * ```typescript
   * const status = gateway.mapResultCodeToStatus('0'); // Returns 'success'
   * const status2 = gateway.mapResultCodeToStatus('1000'); // Returns 'failed'
   * const status3 = gateway.mapResultCodeToStatus('1001'); // Returns 'failed'
   * ```
   *
   * Result Code Mapping:
   * - '0': Success
   * - '1000': Invalid signature
   * - '1001': Order already confirmed
   * - '1002': Order ID not found
   * - '1003': Invalid partner code
   * - '1004': Invalid request ID
   * - '1005': Invalid amount
   * - '1006': Invalid order info
   * - '1007': Invalid return URL
   * - '1008': Invalid IPN URL
   * - '1009': Invalid request type
   * - '1010': Invalid extra data
   * - '1011': Invalid items
   * - '1012': Invalid user info
   * - '1013': Invalid delivery info
   * - '1014': Invalid reference ID
   * - '1015': Invalid auto capture
   * - '1016': Invalid language
   * - '1017': Invalid store name
   * - '1018': Invalid store ID
   * - '1019': Invalid sub partner code
   * - '1020': Invalid order group ID
   * - '1021': Invalid promotion info
   * - '1022': Invalid payment option
   * - '1023': Invalid user fee
   * - '1024': Invalid promotion code
   * - '1025': Invalid promotion amount
   * - '1026': Invalid promotion type
   * - '1027': Invalid promotion name
   * - '1028': Invalid promotion description
   * - '1029': Invalid promotion start date
   * - '1030': Invalid promotion end date
   * - '1031': Invalid promotion conditions
   * - '1032': Invalid promotion usage limit
   * - '1033': Invalid promotion usage count
   * - '1034': Invalid promotion status
   * - '1035': Invalid promotion merchant rate
   * - '1036': Invalid promotion sponsor amount
   * - '1037': Invalid promotion voucher ID
   * - '1038': Invalid promotion voucher type
   * - '1039': Invalid promotion voucher name
   * - '1040': Invalid promotion voucher description
   * - '1041': Invalid promotion voucher start date
   * - '1042': Invalid promotion voucher end date
   * - '1043': Invalid promotion voucher conditions
   * - '1044': Invalid promotion voucher usage limit
   * - '1045': Invalid promotion voucher usage count
   * - '1046': Invalid promotion voucher status
   * - '1047': Invalid promotion voucher merchant rate
   * - '1048': Invalid promotion voucher sponsor amount
   * - '1049': Invalid promotion voucher voucher ID
   * - '1050': Invalid promotion voucher voucher type
   */
  private mapResultCodeToStatus(resultCode: string): PaymentStatus {
    // According to MoMo documentation
    switch (resultCode) {
      case '0':
        return PaymentStatus.SUCCESS; // Success
      case '1000':
        return PaymentStatus.FAILED; // Invalid signature
      case '1001':
        return PaymentStatus.FAILED; // Order already confirmed
      case '1002':
        return PaymentStatus.FAILED; // Order ID not found
      case '1003':
        return PaymentStatus.FAILED; // Invalid partner code
      case '1004':
        return PaymentStatus.FAILED; // Invalid request ID
      case '1005':
        return PaymentStatus.FAILED; // Invalid amount
      case '1006':
        return PaymentStatus.FAILED; // Invalid order info
      case '1007':
        return PaymentStatus.FAILED; // Invalid return URL
      case '1008':
        return PaymentStatus.FAILED; // Invalid IPN URL
      case '1009':
        return PaymentStatus.FAILED; // Invalid request type
      case '1010':
        return PaymentStatus.FAILED; // Invalid extra data
      case '1011':
        return PaymentStatus.FAILED; // Invalid items
      case '1012':
        return PaymentStatus.FAILED; // Invalid user info
      case '1013':
        return PaymentStatus.FAILED; // Invalid delivery info
      case '1014':
        return PaymentStatus.FAILED; // Invalid reference ID
      case '1015':
        return PaymentStatus.FAILED; // Invalid auto capture
      case '1016':
        return PaymentStatus.FAILED; // Invalid language
      case '1017':
        return PaymentStatus.FAILED; // Invalid store name
      case '1018':
        return PaymentStatus.FAILED; // Invalid store ID
      case '1019':
        return PaymentStatus.FAILED; // Invalid sub partner code
      case '1020':
        return PaymentStatus.FAILED; // Invalid order group ID
      case '1021':
        return PaymentStatus.FAILED; // Invalid promotion info
      case '1022':
        return PaymentStatus.FAILED; // Invalid payment option
      case '1023':
        return PaymentStatus.FAILED; // Invalid user fee
      case '1024':
        return PaymentStatus.FAILED; // Invalid promotion code
      case '1025':
        return PaymentStatus.FAILED; // Invalid promotion amount
      case '1026':
        return PaymentStatus.FAILED; // Invalid promotion type
      case '1027':
        return PaymentStatus.FAILED; // Invalid promotion name
      case '1028':
        return PaymentStatus.FAILED; // Invalid promotion description
      case '1029':
        return PaymentStatus.FAILED; // Invalid promotion start date
      case '1030':
        return PaymentStatus.FAILED; // Invalid promotion end date
      case '1031':
        return PaymentStatus.FAILED; // Invalid promotion conditions
      case '1032':
        return PaymentStatus.FAILED; // Invalid promotion usage limit
      case '1033':
        return PaymentStatus.FAILED; // Invalid promotion usage count
      case '1034':
        return PaymentStatus.FAILED; // Invalid promotion status
      case '1035':
        return PaymentStatus.FAILED; // Invalid promotion merchant rate
      case '1036':
        return PaymentStatus.FAILED; // Invalid promotion sponsor amount
      case '1037':
        return PaymentStatus.FAILED; // Invalid promotion voucher ID
      case '1038':
        return PaymentStatus.FAILED; // Invalid promotion voucher type
      case '1039':
        return PaymentStatus.FAILED; // Invalid promotion voucher name
      case '1040':
        return PaymentStatus.FAILED; // Invalid promotion voucher description
      case '1041':
        return PaymentStatus.FAILED; // Invalid promotion voucher start date
      case '1042':
        return PaymentStatus.FAILED; // Invalid promotion voucher end date
      case '1043':
        return PaymentStatus.FAILED; // Invalid promotion voucher conditions
      case '1044':
        return PaymentStatus.FAILED; // Invalid promotion voucher usage limit
      case '1045':
        return PaymentStatus.FAILED; // Invalid promotion voucher usage count
      case '1046':
        return PaymentStatus.FAILED; // Invalid promotion voucher status
      case '1047':
        return PaymentStatus.FAILED; // Invalid promotion voucher merchant rate
      case '1048':
        return PaymentStatus.FAILED; // Invalid promotion voucher sponsor amount
      case '1049':
        return PaymentStatus.FAILED; // Invalid promotion voucher voucher ID
      case '1050':
        return PaymentStatus.FAILED; // Invalid promotion voucher voucher type
      default:
        return PaymentStatus.PENDING; // Unknown status
    }
  }
}
