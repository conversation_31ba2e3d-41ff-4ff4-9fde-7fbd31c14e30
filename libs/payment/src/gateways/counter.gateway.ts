import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { BaseGateway } from './base.gateway';
import {
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentGatewayConfig,
  PaymentCallbackData,
  PaymentStatusResponse,
} from '../types';
import { PaymentStatus } from '../constants';
import { PaymentGatewayType, CURRENCY_TYPE } from '../constants';
import { omit } from 'lodash';
import { generateTransactionId } from '../utils';

@Injectable()
export class CashGateway extends BaseGateway {
  constructor(config?: PaymentGatewayConfig) {
    super(
      config || {
        credentials: {
          // Cash payment doesn't need external credentials
          cashId: 'CASH_001',
          location: 'Main Cash Counter',
        },
        endpoints: {
          payment: '/cash/payment',
          status: '/cash/status',
          callback: '/cash/callback',
        },
        features: {
          deepLink: false,
          qrCode: false,
        },
      },
    );
  }

  async createPayment(
    request: CreatePaymentRequest,
  ): Promise<CreatePaymentResponse> {
    // For cash payment, we generate a simple transaction ID
    const transactionId = generateTransactionId('CASH');

    return {
      success: true,
      transactionId,
      gatewayType: PaymentGatewayType.CASH,
      amount: request.amount,
      currency: request.currency,
      expiresAt: Date.now() + 15 * 60 * 1000, // 15 minutes
    };
  }

  async verifyCallback(
    data: Record<string, any>,
  ): Promise<PaymentCallbackData> {
    // For cash payment, verification is based on manual confirmation
    // This would typically be called by staff at the cash counter
    const isValid =
      data.status === PaymentStatus.SUCCESS &&
      data.transactionId &&
      data.amount > 0;

    return {
      transactionId: data.transactionId,
      status: isValid ? PaymentStatus.SUCCESS : PaymentStatus.FAILED,
      amount: data.amount || 0,
      currency: data.currency || CURRENCY_TYPE.VND,
      responseCode: isValid ? 'PAID' : 'REFUSED',
      responseMessage: isValid
        ? 'Payment confirmed at cash counter'
        : 'Payment refused at cash counter',
      signature: this.generateSignature(data),
      rawData: data,
    };
  }

  async checkPaymentStatus(
    transactionId: string,
  ): Promise<PaymentStatusResponse> {
    // For cash payment, status is managed internally
    // This would typically check against internal database
    return {
      success: true,
      status: PaymentStatus.PENDING, // Default status, would be updated by staff
      transactionId,
      amount: 0, // Would be set by actual payment
      currency: CURRENCY_TYPE.VND,
      responseCode: 'PENDING',
      responseMessage: 'Waiting for cash counter confirmation',
    };
  }

  generateSignature(data: Record<string, any>): string {
    // For cash payment, we use a simple hash
    const dataString = JSON.stringify(data);
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(dataString).digest('hex');
  }

  verifySignature(data: Record<string, any>, signature: string): boolean {
    // For cash payment, we verify the signature
    const expectedSignature = this.generateSignature(data);
    return expectedSignature === signature;
  }
}
