import {
  PaymentGatewayConfig,
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentCallbackData,
  PaymentStatusResponse,
} from '../types';

export abstract class BaseGateway {
  protected config: PaymentGatewayConfig;

  constructor(config: PaymentGatewayConfig) {
    this.config = config;
  }

  abstract createPayment(
    request: CreatePaymentRequest,
  ): Promise<CreatePaymentResponse>;
  abstract verifyCallback(
    data: Record<string, any>,
  ): Promise<PaymentCallbackData>;
  abstract checkPaymentStatus(
    transactionId: string,
  ): Promise<PaymentStatusResponse>;
  abstract generateSignature(data: Record<string, any>): string;
  abstract verifySignature(
    data: Record<string, any>,
    signature: string,
  ): boolean;

  getConfig(): PaymentGatewayConfig {
    return this.config;
  }

  supportsDeepLink(): boolean {
    return this.config.features.deepLink;
  }

  supportsQrCode(): boolean {
    return this.config.features.qrCode;
  }
}

// Re-export types for backward compatibility
export type {
  PaymentGatewayConfig,
  CreatePaymentRequest,
  CreatePaymentResponse,
  PaymentCallbackData,
  PaymentStatusResponse,
};
