// Payment gateway types
export enum PaymentGatewayType {
  ZALOPAY = 'zalopay',
  MOMOPAY = 'momopay',
  VNPAY = 'vnpay',
  CASH = 'cash',
}

// Array for validation and iteration
export const PAYMENT_GATEWAY_TYPES = Object.values(PaymentGatewayType);

// Payment status enum
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// Array for validation and iteration
export const PAYMENT_STATUSES = Object.values(PaymentStatus);

// Currency types enum
export enum CURRENCY_TYPE {
  VND = 'VND',
  USD = 'USD',
}

// Array for validation and iteration
export const PAYMENT_CURRENCIES = Object.values(CURRENCY_TYPE);

// Exchange rates for different currencies (VND/point)
export const EXCHANGE_RATES = {
  [CURRENCY_TYPE.VND]: 1000, // 1000 VND = 1 point
  [CURRENCY_TYPE.USD]: 25000, // 25000 VND (equivalent to 1 USD) = 1 point
} as const;

// Payment response codes for different gateways
export const PAYMENT_RESPONSE_CODES = {
  ZALOPAY: {
    SUCCESS: '00',
    FAILED: '01',
    PENDING: '02',
  },
  MOMOPAY: {
    SUCCESS: '0',
    FAILED: '1000',
    PENDING: '1001',
  },
  VNPAY: {
    SUCCESS: '00',
    FAILED: '01',
    PENDING: '02',
  },
  CASH: {
    SUCCESS: 'SUCCESS',
    FAILED: 'FAILED',
    PENDING: 'PENDING',
  },
} as const;

// Payment events constants
export const PAYMENT_EVENTS = {
  CREATED: 'payment.created',
  SUCCESS: 'payment.success',
  FAILED: 'payment.failed',
  CANCELLED: 'payment.cancelled',
  PENDING: 'payment.pending',
  BALANCE_SUCCESS: 'payment.balance.success',
} as const;

export type PaymentEventType =
  (typeof PAYMENT_EVENTS)[keyof typeof PAYMENT_EVENTS];

// Error codes
export const PAYMENT_ERROR_CODES = {
  INVALID_GATEWAY: 'INVALID_GATEWAY',
  INVALID_AMOUNT: 'INVALID_AMOUNT',
  INVALID_CURRENCY: 'INVALID_CURRENCY',
  GATEWAY_ERROR: 'GATEWAY_ERROR',
  CALLBACK_ERROR: 'CALLBACK_ERROR',
} as const;

// Default values
export const PAYMENT_DEFAULTS = {
  CURRENCY: CURRENCY_TYPE.VND,
  LOCALE: 'vn',
  ORDER_TYPE: 'other',
} as const;

// Timeouts (in milliseconds)
export const PAYMENT_TIMEOUTS = {
  PAYMENT_CREATION: 30000, // 30 seconds
  STATUS_CHECK: 10000, // 10 seconds
  CALLBACK_VERIFICATION: 5000, // 5 seconds
} as const;

// Retry configuration
export const PAYMENT_RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  DELAY: 1000, // 1 second
  BACKOFF_MULTIPLIER: 2,
} as const;

export const PAYMENT_ERROR_MESSAGES = {
  UNSUPPORTED_GATEWAY: 'Unsupported payment gateway',
  INVALID_SIGNATURE: 'Invalid signature received from gateway',
  GATEWAY_ERROR: 'Payment gateway error occurred',
  NETWORK_ERROR: 'Network error while communicating with gateway',
  TIMEOUT_ERROR: 'Request timeout while communicating with gateway',
  INVALID_AMOUNT: 'Invalid payment amount',
  INVALID_CURRENCY: 'Invalid currency code',
  MISSING_REQUIRED_FIELDS: 'Missing required fields for payment',
} as const;
