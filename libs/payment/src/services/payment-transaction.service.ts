import { Injectable, Inject } from '@nestjs/common';
import {
  Order,
  OrderStatus,
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionType,
} from '@app/shared/database/entities';
import { PaymentTransactionRepositoryInterface } from '@app/shared/database/repositories';
import {
  CreatePaymentTransactionDto,
  UpdatePaymentTransactionDto,
} from '../dto';
import { validate as uuidValidate } from 'uuid';

@Injectable()
export class PaymentTransactionService {
  constructor(
    @Inject('PaymentTransactionRepositoryInterface')
    private paymentTransactionRepo: PaymentTransactionRepositoryInterface,
  ) {}

  async createPaymentTransaction(
    data: CreatePaymentTransactionDto,
  ): Promise<PaymentTransaction> {
    const transaction = this.paymentTransactionRepo.create(data);
    return this.paymentTransactionRepo.save(transaction);
  }

  async updatePaymentTransaction(
    id: string,
    data: UpdatePaymentTransactionDto,
  ): Promise<PaymentTransaction> {
    await this.paymentTransactionRepo.update(id, data);
    return this.paymentTransactionRepo.findOneById(id);
  }

  async getPaymentTransactionByGatewayId(
    gatewayTransactionId: string,
  ): Promise<PaymentTransaction> {
    return this.paymentTransactionRepo.findOneBy({ gatewayTransactionId });
  }

  async getPaymentTransactionByIdOrGatewayId(
    idOrGatewayTransactionId: string,
  ): Promise<PaymentTransaction> {
    return this.paymentTransactionRepo.findOneBy({
      ...(uuidValidate(idOrGatewayTransactionId)
        ? { id: idOrGatewayTransactionId }
        : { gatewayTransactionId: idOrGatewayTransactionId }),
    });
  }

  // Additional methods for testing and debugging
  async getAllPaymentTransactions(): Promise<PaymentTransaction[]> {
    return this.paymentTransactionRepo.find();
  }

  async getPaymentTransactionsByUserId(
    userId: string,
  ): Promise<PaymentTransaction[]> {
    return this.paymentTransactionRepo.findBy({ userId });
  }

  async getPaymentTransactionsByStatus(
    status: PaymentTransactionStatus,
  ): Promise<PaymentTransaction[]> {
    return this.paymentTransactionRepo.findBy({ status });
  }

  async deletePaymentTransaction(id: string): Promise<void> {
    await this.paymentTransactionRepo.delete(id);
  }

  async sumPointByOrders(
    orderIds: string[],
    types?: PaymentTransactionType[],
  ): Promise<{ orderId: string; point: number }[]> {
    const query = this.paymentTransactionRepo
      .createQueryBuilder('payment_transaction')
      .select('payment_transaction.order_id', 'orderId')
      .addSelect('SUM(payment_transaction.point)', 'point')
      .where('payment_transaction.order_id IN (:...orderIds)', { orderIds })
      .andWhere('payment_transaction.status = :status', {
        status: PaymentTransactionStatus.SUCCESS,
      })
      .andWhere('payment_transaction.type IN (:...types)', {
        types: types || [
          PaymentTransactionType.DIRECT_PAYMENT,
          PaymentTransactionType.POINT_PAYMENT,
        ],
      })
      .groupBy('payment_transaction.order_id');

    const rawResults = await query.getRawMany();

    // Transform string point values to numbers
    return rawResults.map((result) => ({
      orderId: result.orderId,
      point: Number(result.point) || 0,
    }));
  }

  async calculateAmountSpentByUser(
    userId: string[],
  ): Promise<{ userId: string; amount: number }[]> {
    return await this.paymentTransactionRepo
      .createQueryBuilder('payment_transaction')
      .innerJoin(
        Order,
        'order',
        'order.id = payment_transaction.order_id AND order.status IN (:...orderStatus)',
        {
          orderStatus: [OrderStatus.COMPLETED, OrderStatus.PAID],
        },
      )
      .select('payment_transaction.user_id', 'userId')
      .addSelect('SUM(payment_transaction.amount)', 'amount')
      .where('payment_transaction.status = :status', {
        status: PaymentTransactionStatus.SUCCESS,
      })
      .andWhere('payment_transaction.type = :type', {
        type: PaymentTransactionType.DIRECT_PAYMENT,
      })
      .andWhere('payment_transaction.user_id IN (:...userIds)', {
        userIds: userId,
      })
      .groupBy('payment_transaction.user_id')
      .getRawMany();
  }
}
