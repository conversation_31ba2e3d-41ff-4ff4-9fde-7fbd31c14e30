import { PAYMENT_CURRENCIES, PAYMENT_RESPONSE_CODES } from '../constants';
import { BadRequestException } from '@nestjs/common';
import { ErrorResponse } from '@app/shared/types/error.type';
import { randomUUID } from 'crypto';

/**
 * Generate a secure random string for transaction IDs
 * @param prefix - Optional prefix for the transaction ID
 * @param length - Length of the random part (default: 9)
 * @returns Secure random string
 */
export function generateTransactionId(
  prefix?: string,
  length: number = 9,
): string {
  const timestamp = Date.now();
  const random = Date.now().toString().slice(-6);

  if (prefix) {
    return `${prefix}_${timestamp}_${random}`;
  }

  return `${timestamp}_${random}`;
}

/**
 * Generate VNPay transaction reference (vnp_TxnRef)
 *
 * According to VNPay documentation:
 * - Alphanumeric[1,100]
 * - Must be unique for merchant system
 * - Must not be duplicated within a day
 * - Example: 23554
 *
 * @returns VNPay transaction reference
 */
export function generateVNPayTransactionRef(): string {
  const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const timestamp = Date.now().toString().slice(-6);

  return `${today}${timestamp}`;
}

/**
 * Generate a secure random string
 * @param length - Length of the random string (default: 9)
 * @returns Secure random string
 */
export function generateSecureRandomString(length: number = 9): string {
  return randomUUID().substring(0, length);
}

/**
 * Format amount for VNPay (multiply by 100)
 */
export function formatAmountForVNPay(amount: number): number {
  return amount * 100;
}

/**
 * Parse amount from VNPay (divide by 100)
 */
export function parseAmountFromVNPay(amount: number): number {
  return amount / 100;
}

/**
 * Validate currency code
 */
export function isValidCurrency(currency: string): boolean {
  return PAYMENT_CURRENCIES.includes(currency as any);
}

/**
 * Validate payment amount
 */
export function isValidAmount(amount: number): boolean {
  return amount > 0 && Number.isInteger(amount);
}

/**
 * Generate VNPay date format (YYYYMMDDHHmmss)
 */
export function generateVNPayDate(): string {
  return new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
}

/**
 * Parse VNPay date format
 */
export function parseVNPayDate(dateString: string): Date {
  const year = parseInt(dateString.substring(0, 4));
  const month = parseInt(dateString.substring(4, 6)) - 1;
  const day = parseInt(dateString.substring(6, 8));
  const hour = parseInt(dateString.substring(8, 10));
  const minute = parseInt(dateString.substring(10, 12));
  const second = parseInt(dateString.substring(12, 14));

  return new Date(year, month, day, hour, minute, second);
}

/**
 * Map gateway response code to status
 */
export function mapResponseCodeToStatus(
  gatewayType: string,
  responseCode: string,
): 'success' | 'failed' | 'pending' {
  const codes =
    PAYMENT_RESPONSE_CODES[
      gatewayType.toUpperCase() as keyof typeof PAYMENT_RESPONSE_CODES
    ];

  if (!codes) {
    return 'pending';
  }

  if (Array.isArray(codes.SUCCESS)) {
    if (codes.SUCCESS.includes(responseCode)) {
      return 'success';
    }
  } else if (codes.SUCCESS === responseCode) {
    return 'success';
  }

  if (Array.isArray(codes.FAILED)) {
    if (codes.FAILED.includes(responseCode)) {
      return 'failed';
    }
  }

  return 'pending';
}

/**
 * Validate callback data structure
 */
export function validateCallbackData(
  gatewayType: string,
  data: Record<string, any>,
): boolean {
  const requiredFields = getRequiredCallbackFields(gatewayType);

  for (const field of requiredFields) {
    if (!data[field]) {
      return false;
    }
  }

  return true;
}

/**
 * Get required callback fields for each gateway
 */
function getRequiredCallbackFields(gatewayType: string): string[] {
  switch (gatewayType.toLowerCase()) {
    case 'momopay':
      return ['requestId', 'resultCode', 'amount', 'signature'];
    case 'zalopay':
      return ['app_trans_id', 'return_code', 'amount', 'mac'];
    case 'vnpay':
      return ['vnp_TxnRef', 'vnp_ResponseCode', 'vnp_Amount', 'vnp_SecureHash'];
    default:
      return [];
  }
}

/**
 * Extract transaction ID from callback data
 */
export function extractTransactionId(
  gatewayType: string,
  data: Record<string, any>,
): string {
  switch (gatewayType.toLowerCase()) {
    case 'momopay':
      return data.requestId;
    case 'zalopay':
      return data.app_trans_id;
    case 'vnpay':
      return data.vnp_TxnRef;
    default:
      throw new BadRequestException({
        code: 'UNSUPPORTED_GATEWAY',
      } as ErrorResponse);
  }
}

/**
 * Extract amount from callback data
 */
export function extractAmount(
  gatewayType: string,
  data: Record<string, any>,
): number {
  switch (gatewayType.toLowerCase()) {
    case 'momopay':
    case 'zalopay':
      return parseInt(data.amount);
    case 'vnpay':
      return parseAmountFromVNPay(parseInt(data.vnp_Amount));
    default:
      throw new BadRequestException({
        code: 'UNSUPPORTED_GATEWAY',
      } as ErrorResponse);
  }
}

/**
 * Extract response code from callback data
 */
export function extractResponseCode(
  gatewayType: string,
  data: Record<string, any>,
): string {
  switch (gatewayType.toLowerCase()) {
    case 'momopay':
      return data.resultCode;
    case 'zalopay':
      return data.return_code;
    case 'vnpay':
      return data.vnp_ResponseCode;
    default:
      throw new BadRequestException({
        code: 'UNSUPPORTED_GATEWAY',
      } as ErrorResponse);
  }
}
