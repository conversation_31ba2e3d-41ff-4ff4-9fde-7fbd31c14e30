import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { ErrorResponse } from '@app/shared/types/error.type';
import { BaseGateway } from '../gateways/base.gateway';
import { PaymentGatewayConfig } from '../types';
import { MoMoPayGateway } from '../gateways/momopay.gateway';
import { ZaloPayGateway } from '../gateways/zalopay.gateway';
import { VNPayGateway } from '../gateways/vnpay.gateway';
import { CashGateway } from '../gateways/cash.gateway';
import { PaymentGatewayType } from '../constants';

export type GatewayType = PaymentGatewayType;

@Injectable()
export class PaymentGatewayFactory {
  private readonly gatewayConfigs: Record<GatewayType, PaymentGatewayConfig>;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.gatewayConfigs = {
      [PaymentGatewayType.MOMOPAY]: {
        credentials: {
          partnerCode: this.configService.get<string>(
            'PAYMENT.MOMOPAY.PARTNER_CODE',
          ),
          accessKey: this.configService.get<string>(
            'PAYMENT.MOMOPAY.ACCESS_KEY',
          ),
          secretKey: this.configService.get<string>(
            'PAYMENT.MOMOPAY.SECRET_KEY',
          ),
        },
        endpoints: {
          payment: this.configService.get<string>(
            'PAYMENT.MOMOPAY.PAYMENT_URL',
          ),
          status: this.configService.get<string>('PAYMENT.MOMOPAY.STATUS_URL'),
          callback: this.configService.get<string>(
            'PAYMENT.MOMOPAY.CALLBACK_URL',
          ),
        },
        features: {
          deepLink: true,
          qrCode: true,
        },
      },
      [PaymentGatewayType.ZALOPAY]: {
        credentials: {
          appId: this.configService.get<string>('PAYMENT.ZALOPAY.APP_ID'),
          key1: this.configService.get<string>('PAYMENT.ZALOPAY.KEY1'),
          key2: this.configService.get<string>('PAYMENT.ZALOPAY.KEY2'),
        },
        endpoints: {
          payment: this.configService.get<string>(
            'PAYMENT.ZALOPAY.PAYMENT_URL',
          ),
          status: this.configService.get<string>('PAYMENT.ZALOPAY.STATUS_URL'),
          callback: this.configService.get<string>(
            'PAYMENT.ZALOPAY.CALLBACK_URL',
          ),
        },
        features: {
          deepLink: true,
          qrCode: false,
        },
      },
      [PaymentGatewayType.VNPAY]: {
        credentials: {
          tmnCode: this.configService.get<string>('PAYMENT.VNPAY.TMN_CODE'),
          hashSecret: this.configService.get<string>(
            'PAYMENT.VNPAY.HASH_SECRET',
          ),
        },
        endpoints: {
          payment: this.configService.get<string>('PAYMENT.VNPAY.PAYMENT_URL'),
          status: this.configService.get<string>('PAYMENT.VNPAY.STATUS_URL'),
          callback: this.configService.get<string>(
            'PAYMENT.VNPAY.CALLBACK_URL',
          ),
        },
        features: {
          deepLink: false,
          qrCode: true,
        },
      },
      [PaymentGatewayType.CASH]: {
        credentials: {
          cashId:
            this.configService.get<string>('PAYMENT.CASH.CASH_ID') ||
            'CASH_001',
          location:
            this.configService.get<string>('PAYMENT.CASH.CASH_LOCATION') ||
            'Main Cash Counter',
        },
        endpoints: {
          payment:
            this.configService.get<string>('PAYMENT.CASH.PAYMENT_URL') ||
            '/cash/payment',
          status:
            this.configService.get<string>('PAYMENT.CASH.STATUS_URL') ||
            '/cash/status',
          callback:
            this.configService.get<string>('PAYMENT.CASH.CALLBACK_URL') ||
            '/cash/callback',
        },
        features: {
          deepLink: false,
          qrCode: false,
        },
      },
    };
  }

  create(gatewayType: GatewayType): BaseGateway {
    const config = this.gatewayConfigs[gatewayType];

    if (!config) {
      throw new BadRequestException({
        code: 'UNSUPPORTED_GATEWAY',
      } as ErrorResponse);
    }

    switch (gatewayType) {
      case PaymentGatewayType.MOMOPAY:
        return new MoMoPayGateway(config, this.httpService);
      case PaymentGatewayType.ZALOPAY:
        return new ZaloPayGateway(config, this.httpService);
      case PaymentGatewayType.VNPAY:
        return new VNPayGateway(config, this.httpService);
      case PaymentGatewayType.CASH:
        return new CashGateway(config);
      default:
        throw new BadRequestException({
          code: 'UNSUPPORTED_GATEWAY',
        } as ErrorResponse);
    }
  }

  getSupportedGateways(): GatewayType[] {
    return Object.keys(this.gatewayConfigs) as GatewayType[];
  }

  getGatewayConfig(gatewayType: GatewayType): PaymentGatewayConfig {
    const config = this.gatewayConfigs[gatewayType];

    if (!config) {
      throw new BadRequestException({
        code: 'UNSUPPORTED_GATEWAY',
      } as ErrorResponse);
    }

    return config;
  }

  isGatewaySupported(gatewayType: string): gatewayType is GatewayType {
    return this.getSupportedGateways().includes(gatewayType as GatewayType);
  }
}
