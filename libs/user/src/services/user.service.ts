import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  PaymentTransactionStatus,
  PaymentTransactionType,
  ServiceSessionStatus,
  SOCIAL_PROVIDER,
  User,
  USER_LANGUAGE,
  UserRole,
  UserSocial,
  UserStatus,
} from '@app/shared/database/entities';
import {
  PaymentTransactionRepositoryInterface,
  PaymentUserRepositoryInterface,
  SystemConfigurationRepositoryInterface,
  UserDeviceTokenRepositoryInterface,
  UserNotificationRepositoryInterface,
  UserRepositoryInterface,
  UserSocialRepositoryInterface,
} from '@app/shared/database/repositories';
import * as bcrypt from 'bcryptjs';
import { comparePassword } from '@app/shared/helpers/password.helper';
import { UpdateUserDto, UserDto, UserSocialDto } from '@app/user/dto';
import { ConfigService } from '@nestjs/config';
import { ChangeEmailDto } from '../dto/change-email.dto';
import {
  randomNumberString,
  randomString,
} from '@app/shared/helpers/string.helper';
import { MailService } from '@app/mail/services/mail.service';
import moment from 'moment';
import { USER_EVENT_TYPE } from '@app/user/constants';
import {
  getMailTemplate,
  mapDataToTemplate,
} from '@app/shared/helpers/email.helper';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  UserCreatedEvent,
  UserDeletedEvent,
  UserUpdatedEvent,
} from '../events/user.event';
import { In, Not } from 'typeorm';
import { AppLogger } from '@app/shared/logger/app.logger';
import { RedisClientService } from '@app/cache/services/redis-client.service';
import { MAIL_TEMPLATE_TYPE } from '@app/mail/constants';
import { ErrorResponse, ErrorCode } from '@app/shared/types';
import { generateSignedUrl } from '@app/shared/helpers/url.helper';
import { first, map, uniqBy } from 'lodash';
import { plainToInstance } from 'class-transformer';
import { SearchUserDto } from '../dto/search-user.dto';
import { PaginationResponse } from '@app/shared/types/common.type';
import { ServiceSessionService } from '@app/store/services/service-session.service';
import { ServiceSessionDto } from '@app/store/dto';
import { OrderService } from '@app/order';
import { SYSTEM_SETTING_KEY } from '@app/system/const/system.constant';
import { PAYMENT_USER_DEFAULT_POINTS } from '@app/payment/constants';

@Injectable()
export class UserService {
  constructor(
    private readonly logger: AppLogger,
    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,
    @Inject('UserSocialRepositoryInterface')
    private readonly userSocialRepository: UserSocialRepositoryInterface,
    @Inject('UserDeviceTokenRepositoryInterface')
    private readonly userDeviceTokenRepository: UserDeviceTokenRepositoryInterface,
    @Inject('UserNotificationRepositoryInterface')
    private readonly userNotificationRepository: UserNotificationRepositoryInterface,
    @Inject('PaymentUserRepositoryInterface')
    private readonly paymentUserRepository: PaymentUserRepositoryInterface,
    @Inject('SystemConfigurationRepositoryInterface')
    private readonly systemConfigurationRepository: SystemConfigurationRepositoryInterface,
    @Inject('PaymentTransactionRepositoryInterface')
    private readonly paymentTransactionRepository: PaymentTransactionRepositoryInterface,
    private readonly configService: ConfigService,
    private readonly serviceSessionService: ServiceSessionService,
    private readonly orderService: OrderService,
    private readonly mailService: MailService,
    private readonly eventEmitter: EventEmitter2,
    private readonly redisService: RedisClientService,
  ) {}

  private readonly SALT_COST = 11;

  /**
   * Create a new user.
   * @param user
   * @returns {Promise<User>}
   */
  async create(user: User, skipEvent: boolean = false): Promise<User> {
    const { avatar, email, phone, password, name } = user;

    user.username = user.username ?? email ?? phone;

    const errors: ErrorCode[] = [];
    const existingUsers: User[] = [];

    if (phone) {
      const currentUser = await this.userRepository.findOne({
        where: { phone },
      });
      if (currentUser) {
        if (currentUser.verifiedAt) {
          errors.push('PHONE_EXISTED');
        } else {
          existingUsers.push(currentUser);
        }
      }
    }

    if (email) {
      const currentUser = await this.userRepository.findOne({
        where: { email },
      });
      if (currentUser) {
        if (currentUser.verifiedAt) {
          errors.push('EMAIL_EXISTED');
        } else {
          existingUsers.push(currentUser);
        }
      }
    }

    if (errors.length > 0) {
      throw new ConflictException({
        code: 'INFO_EXISTED',
        errors,
      } as ErrorResponse);
    }

    if (existingUsers.length > 0) {
      for (const user of uniqBy(existingUsers, 'id')) {
        await this.deleteUser(user.id, true);
      }
    }

    const hashedPassword = await bcrypt.hash(password, this.SALT_COST);
    const newUser = await this.userRepository.save({
      ...user,
      password: hashedPassword,
    } as User);

    if (!skipEvent) {
      this.eventEmitter.emit(
        USER_EVENT_TYPE.CREATED,
        new UserCreatedEvent(newUser),
      );
    }

    return newUser;
  }

  /**
   * Update user.
   *
   * @param user
   * @param payload
   * @return {Promise<boolean>}
   */
  async update(id: string, payload: UpdateUserDto): Promise<boolean> {
    const user = await this.getUserById(id);
    const errors: ErrorCode[] = [];
    const existingUsers: User[] = [];

    // Not allow update status if user is in use or has pending order
    if (
      payload?.status &&
      ((await this.checkUserInUse(id)) ||
        (await this.orderService.checkPendingOrder({ userId: id })))
    ) {
      throw new ConflictException({
        code: 'USER_IN_USE',
        message: 'User is in use',
      } as ErrorResponse);
    }

    if (payload?.email && payload.email !== user.email) {
      const currentUser = await this.userRepository.findOne({
        select: { id: true, verifiedAt: true },
        where: { email: payload.email, id: Not(id) },
      });
      if (currentUser) {
        if (currentUser?.verifiedAt) {
          errors.push('EMAIL_EXISTED');
        } else {
          existingUsers.push(currentUser);
        }
      }
    }

    if (payload?.phone && payload.phone !== user.phone) {
      const currentUser = await this.userRepository.findOne({
        select: { id: true, verifiedAt: true },
        where: { phone: payload.phone, id: Not(id) },
      });
      if (currentUser) {
        if (currentUser?.verifiedAt) {
          errors.push('PHONE_EXISTED');
        } else {
          existingUsers.push(currentUser);
        }
      }
    }

    if (errors.length > 0) {
      throw new ConflictException({
        code: 'INFO_EXISTED',
        errors,
      } as ErrorResponse);
    }

    if (existingUsers.length > 0) {
      for (const user of uniqBy(existingUsers, 'id')) {
        await this.deleteUser(user.id, true);
      }
    }

    const result = !!(await this.userRepository.update(user.id, payload))
      .affected;

    if (result) {
      this.eventEmitter.emit(
        USER_EVENT_TYPE.UPDATED,
        new UserUpdatedEvent(user, payload),
      );
    }

    return result;
  }

  /**
   * Update user password.
   * @param id
   * @param oldPassword
   * @param newPassword
   * @returns {Promise<boolean>}
   */
  async updatePassword(
    id: string,
    oldPassword: string,
    newPassword: string,
  ): Promise<boolean> {
    const user = await this.getUserById(id);
    const language = user?.language ?? USER_LANGUAGE.EN;

    if (oldPassword === newPassword) {
      throw new BadRequestException({
        code: 'NEW_PASSWORD_SAME_AS_OLD',
      } as ErrorResponse);
    }

    const isPasswordMatch = await comparePassword(oldPassword, user.password);
    if (!isPasswordMatch) {
      throw new BadRequestException({
        code: 'INVALID_PASSWORD',
      } as ErrorResponse);
    }

    const hashPassword = await bcrypt.hash(newPassword, this.SALT_COST);
    const result = !!(
      await this.userRepository.update(user.id, {
        password: hashPassword,
      })
    )?.affected;

    // if (result) {
    //   const template = getMailTemplate(
    //     MAIL_TEMPLATE_TYPE.CHANGE_PASSWORD_SUCCESS,
    //     language,
    //   );
    //   const content = await mapDataToTemplate(template, {
    //     name: user.name,
    //   });

    //   await this.mailService.send(user.email, template.subject, content);
    // }

    return result;
  }

  /**
   * Send mail verify email.
   *
   * @param user
   * @returns {Promise<boolean>}
   */
  async sendMailVerifyEmail(user: User): Promise<boolean> {
    if (user.email && !user.verifiedAt) {
      const code: string = randomNumberString(6);
      const codeEmailVerifyExpireMinutes = this.configService.get(
        'CODE_EMAIL_VERIFY_EXPIRE_MINUTES',
      );
      const expiresIn: number = codeEmailVerifyExpireMinutes * 60;

      await this.redisService.set(`user-verify-email:${user.id}`, code, {
        EX: expiresIn,
      });

      const language: USER_LANGUAGE = user.language ?? USER_LANGUAGE.EN;

      const template = getMailTemplate(
        MAIL_TEMPLATE_TYPE.VERIFY_EMAIL,
        language,
      );
      const content = await mapDataToTemplate(template, {
        name: user.name ?? user.email,
        code,
        codeEmailVerifyExpireMinutes,
      });

      await this.mailService.send(user.email, template.subject, content);

      return true;
    }

    return true;
  }

  /**
   * Verify email.
   *
   * @param id
   * @param code
   * @returns {Promise<boolean>}
   */
  async verifyEmail(
    id: string,
    code: string,
    language: USER_LANGUAGE = USER_LANGUAGE.EN,
  ): Promise<boolean> {
    const user = await this.getUserById(id);

    if (user.verifiedAt) {
      throw new ConflictException({
        code: 'EMAIL_ALREADY_VERIFIED',
      } as ErrorResponse);
    }

    const currentCode = await this.redisService.get(`user-verify-email:${id}`);
    if (currentCode !== code) {
      throw new ConflictException({
        code: 'INVALID_VERIFY_EMAIL_CODE',
      } as ErrorResponse);
    }

    await this.userRepository.update(id, {
      status: UserStatus.ACTIVE,
      verifiedAt: moment().toDate(),
      ...(user.language !== language ? { language } : {}),
    });

    await this.redisService.del(`user-verify-email:${id}`);

    return true;
  }

  /**
   * Send verification code to new email.
   *
   * @param id
   * @param newEmail
   * @returns {Promise<boolean>}
   */
  async sendChangeEmailCode(id: string, newEmail: string): Promise<boolean> {
    const checkEmailExists = await this.userRepository.existsBy({
      email: newEmail,
      id: Not(id),
    });
    if (checkEmailExists) {
      throw new ConflictException({ code: 'EMAIL_EXISTED' } as ErrorResponse);
    }

    const user = await this.getUserById(id);

    const code: string = randomNumberString(6);
    const expiresIn: number =
      this.configService.get('CODE_CHANGE_EMAIL_EXPIRE_MINUTES') * 60;

    await this.redisService.set(`user-change-email:${user.id}`, code, {
      EX: expiresIn,
    });

    const template = getMailTemplate(
      MAIL_TEMPLATE_TYPE.VERIFY_NEW_EMAIL,
      user.language,
    );
    const content = await mapDataToTemplate(template, {
      name: user.name,
      code,
    });

    await this.mailService.send(newEmail, template.subject, content);

    return true;
  }

  /**
   * Verify and change email.
   *
   * @param id
   * @param payload
   * @returns {Promise<boolean>}
   */
  async verifyAndChangeEmail(
    id: string,
    payload: ChangeEmailDto,
  ): Promise<boolean> {
    const user: User = await this.getUserById(id);

    const checkEmailExists = await this.userRepository.existsBy({
      email: payload.newEmail,
      id: Not(id),
    });
    if (checkEmailExists) {
      throw new ConflictException({ code: 'EMAIL_EXISTED' } as ErrorResponse);
    }

    const currentCode = await this.redisService.get(`user-change-email:${id}`);
    if (currentCode !== payload.code) {
      throw new ConflictException({
        code: 'INVALID_VERIFICATION_CODE',
      } as ErrorResponse);
    }

    const result = await this.userRepository.update(id, {
      email: payload.newEmail,
      username: payload.newEmail,
      verifiedAt: moment().toDate(),
    });

    if (result?.affected) {
      await this.redisService.del(`user-change-email:${id}`);
    }

    return true;
  }

  /**
   * Forgot password.
   *
   * @param email
   * @returns {Promise<boolean>}
   */
  async forgotPassword(
    email: string,
    requestLanguage?: USER_LANGUAGE,
  ): Promise<boolean> {
    const user = await this.getVerifiedUserByEmail(email);

    if (user) {
      if (user.status === UserStatus.INACTIVE) {
        throw new BadRequestException({
          code: 'USER_NOT_ACTIVATED',
        } as ErrorResponse);
      }

      if (user.status === UserStatus.BLOCKED) {
        throw new BadRequestException({
          code: 'USER_BLOCKED',
        } as ErrorResponse);
      }

      const userSocials = await this.userSocialRepository.find({
        where: {
          userId: user.id,
        },
      });

      if (userSocials.length > 0) {
        throw new BadRequestException({
          code: 'USER_SOCIAL_ACCOUNT_EXISTS',
          errors: {
            provider: map(userSocials, 'provider'),
          },
        } as ErrorResponse);
      }
    }

    const forgotPasswordUrlExpireMinutes = this.configService.get(
      'CODE_FORGOT_PASSWORD_EXPIRE_MINUTES',
    );
    const expiresIn: number = forgotPasswordUrlExpireMinutes * 60;
    const code = randomNumberString(6);

    await this.redisService.set(`user-forgot-password:${user.id}`, code, {
      EX: expiresIn,
    });

    const name = user.name;
    const language = requestLanguage || user?.language || USER_LANGUAGE.EN;

    const template = getMailTemplate(
      MAIL_TEMPLATE_TYPE.RESET_PASSWORD,
      language,
    );
    const content = await mapDataToTemplate(template, {
      name,
      code,
      verifyCodeResetPasswordMinute: forgotPasswordUrlExpireMinutes,
    });

    await this.mailService.send(email, template.subject, content);

    return true;
  }

  /**
   * Verify forgot password code and generate signed URL data.
   *
   * @param email
   * @param code
   * @returns {Promise<{ signature: string; expires: number }>}
   */
  async verifyForgotPasswordCode(
    email: string,
    code: string,
  ): Promise<{ signature: string; expires: number }> {
    const user = await this.getVerifiedUserByEmail(email);

    const currentCode = await this.redisService.get(
      `user-forgot-password:${user.id}`,
    );
    if (currentCode !== code) {
      throw new NotFoundException({
        code: 'INVALID_VERIFICATION_CODE',
      } as ErrorResponse);
    }

    const expiresIn = 6 * 60 * 10; // 60 phút

    const signedUrlData = generateSignedUrl(
      '/auth/reset-password',
      expiresIn,
      { email },
      this.configService,
    );

    return {
      signature: signedUrlData.signature,
      expires: signedUrlData.expires,
    };
  }

  /**
   * Reset password.
   *
   * @param email
   * @param newPassword
   * @returns {Promise<boolean>}
   */
  async resetPassword(email: string, newPassword: string): Promise<boolean> {
    const user = await this.getVerifiedUserByEmail(email);

    const hashPassword = await bcrypt.hash(newPassword, this.SALT_COST);

    await this.userRepository.update(user.id, {
      password: hashPassword,
    });

    // Remove code from Redis after successful reset
    await this.redisService.del(`user-forgot-password:${user.id}`);

    return true;
  }

  /**
   * Unlink LINE account from user.
   *
   * @param userId
   * @returns {Promise<boolean>}
   */
  async unlinkLine(userId: string): Promise<boolean> {
    const userLineSocial = await this.userSocialRepository.findOne({
      where: {
        userId,
        provider: SOCIAL_PROVIDER.LINE,
      },
    });
    if (!userLineSocial) {
      throw new NotFoundException({
        code: 'USER_SOCIAL_NOT_FOUND',
      });
    }

    await this.userSocialRepository.deleteBy({ id: userLineSocial.id });

    return true;
  }

  /**
   * Get user by JWT token.
   *
   * @param userId
   * @returns {Promise<User>}
   */
  async getByJwtToken(userId: string): Promise<User> {
    return this.getUserById(userId);
  }

  /**
   * Get authenticated user.
   * @param username
   * @param password
   * @returns {Promise<User>}
   */
  async getAuthenticated(username: string, password: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: [{ email: username }, { phone: username }],
    });

    if (!user || !user.verifiedAt) {
      throw new NotFoundException({ code: 'USER_NOT_FOUND' } as ErrorResponse);
    }

    const userSocials = await this.userSocialRepository.find({
      where: {
        userId: user.id,
      },
    });

    if (userSocials.length > 0) {
      throw new BadRequestException({
        code: 'USER_SOCIAL_ACCOUNT_EXISTS',
        errors: {
          provider: map(userSocials, 'provider'),
        },
      } as ErrorResponse);
    }

    const isPasswordMatch = await comparePassword(password, user.password);
    if (!isPasswordMatch) {
      throw new BadRequestException({
        code: 'INVALID_PASSWORD',
      } as ErrorResponse);
    }

    return user;
  }

  /**
   * Get authenticated user via social login.
   * @param data
   * @returns {Promise<User | UserDto>}
   */
  async getAuthenticatedViaSocial({
    provider,
    socialId,
    email,
    name,
    avatar,
    userId,
    language,
    isSkipCreateUser,
    isSkipProcessUser,
  }: {
    provider: SOCIAL_PROVIDER;
    socialId: string;
    email?: string;
    name?: string;
    avatar?: string;
    userId?: string;
    language?: USER_LANGUAGE;
    isSkipCreateUser: boolean;
    isSkipProcessUser?: boolean;
  }): Promise<User | UserDto> {
    let user: User;
    let userSocial: UserSocial;
    const username: string = `${provider}_${socialId}`;
    if (email) {
      user = await this.userRepository.findOneBy({ email });
      if (user && user.email === email) {
        if (username !== user.username) {
          throw new ConflictException({
            code: 'EMAIL_EXISTED',
          } as ErrorResponse);
        }
      }
    }

    userSocial = await this.userSocialRepository.findOne({
      where: {
        provider,
        socialId,
      },
      select: {
        id: true,
        userId: true,
        provider: true,
        socialId: true,
      },
    });

    user = null;
    if (userSocial) {
      if (userId && userId !== userSocial.userId) {
        throw new ConflictException({
          code: 'USER_SOCIAL_ACCOUNT_ALREADY_LINKED',
        } as ErrorResponse);
      }
      user = await this.getUserById(userSocial.userId);
    } else {
      if (isSkipCreateUser) {
        throw new ConflictException({
          code: 'USER_SOCIAL_ACCOUNT_NOT_LINKED',
        } as ErrorResponse);
      }

      if (!userId) {
        user = await this.create(
          {
            username,
            password: randomString(10),
            name: name ?? null,
            email: email ?? null,
            avatar: avatar ?? '',
            language: language ?? USER_LANGUAGE.EN,
            status: UserStatus.ACTIVE,
            verifiedAt: new Date(),
          } as User,
          true,
        );
      } else {
        user = await this.getUserById(userId);
      }

      userSocial = await this.userSocialRepository.save({
        userId: user.id,
        socialId,
        provider,
      } as UserSocial);
    }

    if (user?.status === UserStatus.INACTIVE) {
      throw new BadRequestException({
        code: 'USER_NOT_ACTIVATED',
      } as ErrorResponse);
    }

    if (user?.status === UserStatus.BLOCKED) {
      throw new BadRequestException({
        code: 'USER_BLOCKED',
      } as ErrorResponse);
    }

    await this.userRepository.update(user.id, {
      language: language ?? user.language,
    });

    return isSkipProcessUser ? user : await this.processUser(user);
  }

  /**
   * Send verification code to delete account.
   *
   * @param userId
   * @returns {Promise<boolean>}
   */
  async sendDeleteAccountCode(userId: string): Promise<boolean> {
    const user = await this.getUserById(userId);

    const email = user.email ?? '';

    if (!email) {
      throw new BadRequestException({
        code: 'USER_EMAIL_NOT_FOUND',
      } as ErrorResponse);
    }

    const language = user?.language ?? USER_LANGUAGE.EN;

    const code: string = randomNumberString(6);

    const verifyCodeDeleteUserMinute = this.configService.get(
      'CODE_DELETE_USER_MINUTES',
    );
    const expiresIn: number = verifyCodeDeleteUserMinute * 60;

    await this.redisService.set(`user-delete-${user.id}`, code, {
      EX: expiresIn,
    });

    const template = getMailTemplate(
      MAIL_TEMPLATE_TYPE.VERIFY_DELETE_USER,
      language,
    );
    const content = await mapDataToTemplate(template, {
      name: user.name,
      code,
      verifyCodeDeleteUserMinute,
    });

    await this.mailService.send(user.email, template.subject, content);

    return true;
  }

  /**
   * Verify and delete account with code.
   *
   * @param id
   * @param code
   * @returns {Promise<boolean>}
   */
  async verifyAndDeleteAccount(id: string, code: string): Promise<boolean> {
    const user: User = await this.getUserById(id);

    const deleteCode = await this.redisService.get(`user-delete-${id}`);
    if (deleteCode !== code) {
      throw new NotFoundException({
        code: 'INVALID_VERIFICATION_CODE',
      } as ErrorResponse);
    }

    await this._performUserDeletion(user);

    // Remove code from Redis after successful verification
    await this.redisService.del(`user-delete-${id}`);

    return true;
  }

  /**
   * Delete user directly (Admin only).
   *
   * @param id
   * @returns {Promise<boolean>}
   */
  async deleteUser(id: string, isSkipEvent: boolean = false): Promise<boolean> {
    const user: User = await this.getUserById(id);
    await this._performUserDeletion(user, isSkipEvent);

    return true;
  }

  /**
   * Perform user deletion (soft delete).
   *
   * @param user
   * @returns {Promise<void>}
   */
  private async _performUserDeletion(
    user: User,
    isSkipEvent: boolean = false,
  ): Promise<void> {
    const email: string = user.email ?? '';
    const phone: string = user.phone ?? '';
    const suffixDeleted: string = '_deleted:' + moment().unix();

    await this.userRepository.update(user.id, {
      username: user.username + suffixDeleted,
      deletedAt: moment().toDate(),
      ...(email && { email: email + suffixDeleted }),
      ...(phone && { phone: phone + suffixDeleted }),
    });

    await this.userSocialRepository.deleteBy({
      userId: user.id,
    });

    await this.userDeviceTokenRepository.deleteBy({
      userId: user.id,
    });

    await this.userNotificationRepository.deleteBy({
      userId: user.id,
    });

    if (!isSkipEvent) {
      this.eventEmitter.emit(
        USER_EVENT_TYPE.DELETED,
        new UserDeletedEvent(user),
      );
    }
  }

  /**
   * Get user by email or throw NotFoundException
   * @param email
   * @returns {Promise<User>}
   */
  async getByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOneBy({ email });
    if (!user) {
      throw new NotFoundException({ code: 'USER_NOT_FOUND' } as ErrorResponse);
    }
    return user;
  }

  /**
   * Get verified user by email or throw NotFoundException
   * @param email
   * @returns {Promise<User>}
   */
  async getVerifiedUserByEmail(email: string): Promise<User> {
    const user = await this.getByEmail(email);
    if (!user.verifiedAt) {
      throw new NotFoundException({
        code: 'USER_NOT_ACTIVATED',
      } as ErrorResponse);
    }
    if (user.status === UserStatus.BLOCKED) {
      throw new NotFoundException({ code: 'USER_BLOCKED' } as ErrorResponse);
    }
    return user;
  }

  /**
   * Get user by ID or throw NotFoundException
   * @param id
   * @returns {Promise<User>}
   */
  async getUserById(id: string): Promise<User> {
    const user = await this.userRepository.findOneById(id);
    if (!user) {
      throw new NotFoundException({ code: 'USER_NOT_FOUND' } as ErrorResponse);
    }
    return user;
  }

  async processUser(user: User): Promise<UserDto> {
    return first(await this.processUsers([user]));
  }

  async processUsers(users: User[]): Promise<UserDto[]> {
    if (!users || users.length === 0) {
      return [];
    }

    const userSocials = await this.userSocialRepository.findBy({
      userId: In(map(users, 'id')),
    });

    const paymentUsers = await this.paymentUserRepository.findBy({
      userId: In(map(users, 'id')),
    });

    const usersNotHavePayment = users.filter((user) => {
      return !paymentUsers.find(
        (paymentUser) => paymentUser.userId === user.id,
      );
    });

    if (usersNotHavePayment.length > 0) {
      const initialPoint = await this.systemConfigurationRepository.findOneBy({
        key: SYSTEM_SETTING_KEY.INITIAL_POINT,
      });
      const newPaymentUsers = await this.paymentUserRepository.save(
        usersNotHavePayment.map((user) => {
          return this.paymentUserRepository.create({
            userId: user.id,
            totalPoints:
              Number(initialPoint?.value) || PAYMENT_USER_DEFAULT_POINTS,
          });
        }),
      );
      paymentUsers.push(...newPaymentUsers);
    }

    const latestServiceSessions =
      await this.serviceSessionService.getLatestServiceSessionsByUserId(
        map(users, 'id'),
      );

    const amountSpentByUser =
      await this.orderService.calculateAmountSpentByUser(map(users, 'id'));

    const paymentStatuses = await this.paymentTransactionRepository
      .createQueryBuilder('payment_transaction')
      .select('payment_transaction.user_id', 'userId')
      .addSelect('1', 'hasPayment')
      .where('payment_transaction.status = :status', {
        status: PaymentTransactionStatus.SUCCESS,
      })
      .where('payment_transaction.user_id IN (:...userIds)', {
        userIds: map(users, 'id'),
      })
      .andWhere('payment_transaction.type = :type', {
        type: PaymentTransactionType.DIRECT_PAYMENT,
      })
      .groupBy('payment_transaction.user_id')
      .getRawMany();

    return users.map((user: User) => {
      const userDto = plainToInstance(UserDto, user);

      // Socials
      const userSocialsByUser = userSocials.filter((userSocial: UserSocial) => {
        return userSocial.userId === user.id;
      });

      userDto.socials = plainToInstance(UserSocialDto, userSocialsByUser);

      // Points
      const paymentUser = paymentUsers.find((paymentUser) => {
        return paymentUser.userId === user.id;
      });
      userDto.points = paymentUser?.totalPoints ?? 0;

      // Latest service session
      const latestServiceSession = latestServiceSessions.find(
        (serviceSession) => serviceSession.userId === user.id,
      );
      userDto.latestServiceSession = latestServiceSession
        ? plainToInstance(ServiceSessionDto, latestServiceSession)
        : null;

      // Amount spent and used points
      const amountSpent = amountSpentByUser.find(
        (item) => item.userId === user.id,
      );
      userDto.amountSpent = amountSpent?.amountSpent ?? 0;
      userDto.usedPoints = amountSpent?.usedPoints ?? 0;

      // Payment status
      const paymentStatus = paymentStatuses.find(
        (item) => item.userId === user.id,
      );
      userDto.hasPayment = !!(paymentStatus?.hasPayment ?? false);

      return userDto;
    });
  }
  /**
   * Get users with pagination and filtering
   * @param dto - Search parameters including roles, statuses, keyword, pagination
   * @returns {Promise<PaginationResponse<User>>}
   */
  async getList(dto: SearchUserDto): Promise<PaginationResponse<User>> {
    const skip = dto.skip || 0;
    const limit = dto.limit || 15;

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .orderBy('user.createdAt', 'DESC')
      .where('user.verifiedAt IS NOT NULL');

    if (dto.keyword && dto.keyword.trim()) {
      // Split keywords by space and filter out empty strings
      const keywords = dto.keyword
        .trim()
        .split(/\s+/)
        .filter((keyword) => keyword.length > 0);

      if (keywords.length > 0) {
        // Build AND conditions for each keyword
        const keywordConditions = keywords.map((keyword, index) => {
          const paramName = `keyword${index}`;
          return `(
            user.name ILIKE :${paramName} OR 
            user.email ILIKE :${paramName} OR
            user.phone ILIKE :${paramName}
          )`;
        });

        // Add parameters for each keyword
        const keywordParams = keywords.reduce((params, keyword, index) => {
          params[`keyword${index}`] = `%${keyword}%`;
          return params;
        }, {});

        // Join all conditions with AND
        const whereCondition = keywordConditions.join(' AND ');
        queryBuilder.andWhere(whereCondition, keywordParams);
      }
    }

    if (dto.roles) {
      queryBuilder.andWhere('user.role IN (:...roles)', { roles: dto.roles });
    }

    if (dto.statuses) {
      queryBuilder.andWhere('user.status IN (:...statuses)', {
        statuses: dto.statuses,
      });
    }

    const [items, count] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      count,
    };
  }

  /**
   * Admin change user password
   * @param id - User ID
   * @param currentPassword - Current password for verification
   * @param newPassword - New password
   * @returns {Promise<boolean>}
   */
  async adminChangePassword(
    id: string,
    currentPassword: string,
    newPassword: string,
  ): Promise<boolean> {
    const user = await this.getUserById(id);
    const language = user?.language ?? USER_LANGUAGE.EN;

    // Verify current password
    const isPasswordMatch = await comparePassword(
      currentPassword,
      user.password,
    );
    if (!isPasswordMatch) {
      throw new BadRequestException({
        code: 'INVALID_PASSWORD',
      } as ErrorResponse);
    }

    // Check if new password is same as current password
    if (currentPassword === newPassword) {
      throw new BadRequestException({
        code: 'NEW_PASSWORD_SAME_AS_OLD',
      } as ErrorResponse);
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, this.SALT_COST);

    // Update password
    const result = await this.userRepository.update(id, {
      password: hashedPassword,
    });

    // if (result.affected) {
    //   const template = getMailTemplate(
    //     MAIL_TEMPLATE_TYPE.CHANGE_PASSWORD_SUCCESS,
    //     language,
    //   );
    //   const content = await mapDataToTemplate(template, {
    //     name: user.name,
    //   });

    //   await this.mailService.send(user.email, template.subject, content);
    // }

    return !!result.affected;
  }

  async checkUserInUse(
    id: string,
    restrictStatuses: ServiceSessionStatus[] = [ServiceSessionStatus.IN_USE],
  ): Promise<boolean> {
    const latestServiceSession =
      await this.serviceSessionService.getLatestServiceSessionsByUserId([id]);
    return latestServiceSession.some((serviceSession) =>
      restrictStatuses.includes(serviceSession.status),
    );
  }
}
