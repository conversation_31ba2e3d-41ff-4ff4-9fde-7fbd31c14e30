import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  UserNotification,
  USER_NOTIFICATION_STATUS,
  USER_NOTIFICATION_TYPE,
} from '@app/shared/database/entities';
import { UserNotificationRepositoryInterface } from '@app/shared/database/repositories';
import {
  CreateUserNotificationDto,
  UpdateUserNotificationDto,
  FindUserNotificationDto,
} from '../dto/user-notification.dto';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class UserNotificationService {
  constructor(
    @Inject('UserNotificationRepositoryInterface')
    private readonly userNotificationRepository: UserNotificationRepositoryInterface,
  ) {}

  /**
   * Create a new user notification
   */
  async create(
    createUserNotificationDto: CreateUserNotificationDto,
  ): Promise<UserNotification> {
    const userNotification = this.userNotificationRepository.create(
      createUserNotificationDto,
    );
    return await this.userNotificationRepository.save(userNotification);
  }

  /**
   * Find all user notifications with optional filters
   */
  async findAll(
    findUserNotificationDto?: FindUserNotificationDto,
  ): Promise<{ data: UserNotification[]; total: number }> {
    const queryBuilder =
      this.userNotificationRepository.createQueryBuilder('userNotification');

    if (findUserNotificationDto?.userId) {
      queryBuilder.andWhere('userNotification.userId = :userId', {
        userId: findUserNotificationDto.userId,
      });
    }

    if (findUserNotificationDto?.deviceToken) {
      queryBuilder.andWhere('userNotification.deviceToken = :deviceToken', {
        deviceToken: findUserNotificationDto.deviceToken,
      });
    }

    if (findUserNotificationDto?.type) {
      queryBuilder.andWhere('userNotification.type = :type', {
        type: findUserNotificationDto.type,
      });
    }

    if (findUserNotificationDto?.status) {
      queryBuilder.andWhere('userNotification.status = :status', {
        status: findUserNotificationDto.status,
      });
    }

    // Order by latest first
    queryBuilder.orderBy('userNotification.createdAt', 'DESC');

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total };
  }

  /**
   * Find a user notification by ID
   */
  async findOne(id: string): Promise<UserNotification> {
    const userNotification =
      await this.userNotificationRepository.findOneById(id);

    if (!userNotification) {
      throw new NotFoundException({
        code: 'USER_NOTIFICATION_NOT_FOUND',
      } as ErrorResponse);
    }

    return userNotification;
  }

  /**
   * Find user notifications by user ID
   */
  async findByUserId(
    userId: string,
    limit?: number,
  ): Promise<UserNotification[]> {
    const options: any = {
      where: { userId },
      order: { createdAt: 'DESC' },
    };

    if (limit) {
      options.take = limit;
    }

    return await this.userNotificationRepository.find(options);
  }

  /**
   * Find unread notifications for a user
   */
  async findUnreadByUserId(userId: string): Promise<UserNotification[]> {
    return await this.userNotificationRepository.findBy({
      userId,
      status: USER_NOTIFICATION_STATUS.UNSENT,
    });
  }

  /**
   * Find notifications by type
   */
  async findByType(
    type: USER_NOTIFICATION_TYPE,
    limit?: number,
  ): Promise<UserNotification[]> {
    const options: any = {
      where: { type },
      order: { createdAt: 'DESC' },
    };

    if (limit) {
      options.take = limit;
    }

    return await this.userNotificationRepository.find(options);
  }

  /**
   * Find notifications by status
   */
  async findByStatus(
    status: USER_NOTIFICATION_STATUS,
    limit?: number,
  ): Promise<UserNotification[]> {
    const options: any = {
      where: { status },
      order: { createdAt: 'DESC' },
    };

    if (limit) {
      options.take = limit;
    }

    return await this.userNotificationRepository.find(options);
  }

  /**
   * Update a user notification
   */
  async update(
    id: string,
    updateUserNotificationDto: UpdateUserNotificationDto,
  ): Promise<UserNotification> {
    await this.findOne(id); // Check if exists
    await this.userNotificationRepository.update(id, updateUserNotificationDto);
    return await this.findOne(id);
  }

  /**
   * Mark notification as sent
   */
  async markAsSent(id: string): Promise<UserNotification> {
    return await this.update(id, { status: USER_NOTIFICATION_STATUS.SENT });
  }

  /**
   * Mark notification as failed
   */
  async markAsFailed(id: string): Promise<UserNotification> {
    return await this.update(id, { status: USER_NOTIFICATION_STATUS.FAILURE });
  }

  /**
   * Mark multiple notifications as sent
   */
  async markMultipleAsSent(ids: string[]): Promise<void> {
    await Promise.all(ids.map((id) => this.markAsSent(id)));
  }

  /**
   * Remove a user notification
   */
  async remove(id: string): Promise<void> {
    await this.findOne(id); // Check if exists
    await this.userNotificationRepository.delete(id);
  }

  /**
   * Remove all notifications for a user
   */
  async removeByUserId(userId: string): Promise<void> {
    await this.userNotificationRepository.deleteBy({ userId });
  }

  /**
   * Remove old notifications (older than specified days)
   */
  async removeOldNotifications(days: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    await this.userNotificationRepository
      .createQueryBuilder('notification')
      .delete()
      .where('notification.createdAt < :cutoffDate', { cutoffDate })
      .execute();
  }

  /**
   * Get count of notifications for a user
   */
  async countByUserId(userId: string): Promise<number> {
    return await this.userNotificationRepository.count({ userId });
  }

  /**
   * Get count of unread notifications for a user
   */
  async countUnreadByUserId(userId: string): Promise<number> {
    return await this.userNotificationRepository.count({
      userId,
      status: USER_NOTIFICATION_STATUS.UNSENT,
    });
  }

  /**
   * Get notification statistics
   */
  async getStatistics(): Promise<{
    total: number;
    sent: number;
    unsent: number;
    failed: number;
  }> {
    const [total, sent, unsent, failed] = await Promise.all([
      this.userNotificationRepository.count({}),
      this.userNotificationRepository.count({
        status: USER_NOTIFICATION_STATUS.SENT,
      }),
      this.userNotificationRepository.count({
        status: USER_NOTIFICATION_STATUS.UNSENT,
      }),
      this.userNotificationRepository.count({
        status: USER_NOTIFICATION_STATUS.FAILURE,
      }),
    ]);

    return { total, sent, unsent, failed };
  }

  /**
   * Create and send notification (helper method)
   */
  async createAndSend(
    createUserNotificationDto: CreateUserNotificationDto,
  ): Promise<UserNotification> {
    const notification = await this.create(createUserNotificationDto);

    // Here you would integrate with actual notification service (FCM, etc.)
    // For now, we just mark it as sent
    try {
      // TODO: Implement actual notification sending logic
      await this.markAsSent(notification.id);
      return notification;
    } catch (error) {
      await this.markAsFailed(notification.id);
      throw error;
    }
  }
}
