import {
  Injectable,
  Inject,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { UserSocialRepositoryInterface } from '@app/shared/database/repositories';
import { UserSocial, SOCIAL_PROVIDER } from '@app/shared/database/entities';
import {
  CreateUserSocialDto,
  UpdateUserSocialDto,
  FindUserSocialDto,
} from '../dto/user-social.dto';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class UserSocialService {
  constructor(
    @Inject('UserSocialRepositoryInterface')
    private readonly userSocialRepository: UserSocialRepositoryInterface,
  ) {}

  /**
   * Create a new user social account
   */
  async create(createUserSocialDto: CreateUserSocialDto): Promise<UserSocial> {
    // Check if user already has this provider
    const existingSocial = await this.userSocialRepository.findOneBy({
      userId: createUserSocialDto.userId,
      provider: createUserSocialDto.provider,
    });

    if (existingSocial) {
      throw new ConflictException({
        code: 'USER_SOCIAL_ACCOUNT_ALREADY_EXISTS',
      } as ErrorResponse);
    }

    // Check if social ID is already linked to another user
    const existingSocialId = await this.userSocialRepository.findOneBy({
      socialId: createUserSocialDto.socialId,
      provider: createUserSocialDto.provider,
    });

    if (existingSocialId) {
      throw new ConflictException({
        code: 'USER_SOCIAL_ACCOUNT_ALREADY_LINKED_TO_OTHER_USER',
      } as ErrorResponse);
    }

    const userSocial = this.userSocialRepository.create(createUserSocialDto);
    return await this.userSocialRepository.save(userSocial);
  }

  /**
   * Find all user social accounts with optional filters
   */
  async findAll(
    findUserSocialDto?: FindUserSocialDto,
  ): Promise<{ data: UserSocial[]; total: number }> {
    const queryBuilder =
      this.userSocialRepository.createQueryBuilder('userSocial');

    if (findUserSocialDto?.userId) {
      queryBuilder.andWhere('userSocial.userId = :userId', {
        userId: findUserSocialDto.userId,
      });
    }

    if (findUserSocialDto?.socialId) {
      queryBuilder.andWhere('userSocial.socialId = :socialId', {
        socialId: findUserSocialDto.socialId,
      });
    }

    if (findUserSocialDto?.provider) {
      queryBuilder.andWhere('userSocial.provider = :provider', {
        provider: findUserSocialDto.provider,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total };
  }

  /**
   * Find a user social account by ID
   */
  async findOne(id: string): Promise<UserSocial> {
    const userSocial = await this.userSocialRepository.findOneById(id);

    if (!userSocial) {
      throw new NotFoundException({
        code: 'USER_SOCIAL_ACCOUNT_NOT_FOUND',
      } as ErrorResponse);
    }

    return userSocial;
  }

  /**
   * Find user social accounts by user ID
   */
  async findByUserId(userId: string): Promise<UserSocial[]> {
    return await this.userSocialRepository.findBy({ userId });
  }

  /**
   * Find user social account by social ID and provider
   */
  async findBySocialIdAndProvider(
    socialId: string,
    provider: SOCIAL_PROVIDER,
  ): Promise<UserSocial | null> {
    return await this.userSocialRepository.findOneBy({ socialId, provider });
  }

  /**
   * Update a user social account
   */
  async update(
    id: string,
    updateUserSocialDto: UpdateUserSocialDto,
  ): Promise<UserSocial> {
    const userSocial = await this.findOne(id);

    // If updating social ID, check for conflicts
    if (updateUserSocialDto.socialId) {
      const existingSocialId = await this.userSocialRepository.findOneBy({
        socialId: updateUserSocialDto.socialId,
        provider: updateUserSocialDto.provider || userSocial.provider,
      });

      if (existingSocialId && existingSocialId.id !== id) {
        throw new ConflictException({
          code: 'USER_SOCIAL_ACCOUNT_ALREADY_LINKED_TO_OTHER_USER',
        } as ErrorResponse);
      }
    }

    await this.userSocialRepository.update(id, updateUserSocialDto);
    return await this.findOne(id);
  }

  /**
   * Remove a user social account
   */
  async remove(id: string): Promise<void> {
    const userSocial = await this.findOne(id);
    await this.userSocialRepository.delete(id);
  }

  /**
   * Remove all social accounts for a user
   */
  async removeByUserId(userId: string): Promise<void> {
    await this.userSocialRepository.deleteBy({ userId });
  }

  /**
   * Check if user has a specific social provider
   */
  async hasProvider(
    userId: string,
    provider: SOCIAL_PROVIDER,
  ): Promise<boolean> {
    return await this.userSocialRepository.existsBy({ userId, provider });
  }

  /**
   * Get count of social accounts for a user
   */
  async countByUserId(userId: string): Promise<number> {
    return await this.userSocialRepository.count({ userId });
  }
}
