import {
  Inject,
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { UserDeviceToken } from '@app/shared/database/entities';
import { UserDeviceTokenRepositoryInterface } from '@app/shared/database/repositories';
import {
  CreateUserDeviceTokenDto,
  UpdateUserDeviceTokenDto,
  FindUserDeviceTokenDto,
} from '../dto/user-device-token.dto';
import { ErrorResponse } from '@app/shared/types';
import { validate as uuidValidate } from 'uuid';

@Injectable()
export class UserDeviceTokenService {
  constructor(
    @Inject('UserDeviceTokenRepositoryInterface')
    private readonly userDeviceTokenRepository: UserDeviceTokenRepositoryInterface,
  ) {}

  /**
   * Create a new user device token
   */
  async create(
    createUserDeviceTokenDto: CreateUserDeviceTokenDto,
  ): Promise<UserDeviceToken> {
    // Check if device token already exists
    const existingToken = await this.userDeviceTokenRepository.findOneBy({
      deviceToken: createUserDeviceTokenDto.deviceToken,
    });

    if (existingToken) {
      // If token exists but for different user, update it
      if (existingToken.userId !== createUserDeviceTokenDto.userId) {
        await this.userDeviceTokenRepository.update(existingToken.id, {
          userId: createUserDeviceTokenDto.userId,
        });
        return await this.findOne(existingToken.id);
      }
      // If same user, return existing token
      return existingToken;
    }

    const userDeviceToken = this.userDeviceTokenRepository.create(
      createUserDeviceTokenDto,
    );
    return await this.userDeviceTokenRepository.save(userDeviceToken);
  }

  /**
   * Find all user device tokens with optional filters
   */
  async findAll(
    findUserDeviceTokenDto?: FindUserDeviceTokenDto,
  ): Promise<{ data: UserDeviceToken[]; total: number }> {
    const queryBuilder =
      this.userDeviceTokenRepository.createQueryBuilder('userDeviceToken');

    if (findUserDeviceTokenDto?.userId) {
      queryBuilder.andWhere('userDeviceToken.userId = :userId', {
        userId: findUserDeviceTokenDto.userId,
      });
    }

    if (findUserDeviceTokenDto?.deviceToken) {
      queryBuilder.andWhere('userDeviceToken.deviceToken = :deviceToken', {
        deviceToken: findUserDeviceTokenDto.deviceToken,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total };
  }

  /**
   * Find a user device token by ID
   */
  async findOne(id: string): Promise<UserDeviceToken> {
    const userDeviceToken =
      await this.userDeviceTokenRepository.findOneById(id);

    if (!userDeviceToken) {
      throw new NotFoundException({
        code: 'USER_DEVICE_TOKEN_NOT_FOUND',
      } as ErrorResponse);
    }

    return userDeviceToken;
  }

  /**
   * Find user device tokens by user ID
   */
  async findByUserId(userId: string): Promise<UserDeviceToken[]> {
    return await this.userDeviceTokenRepository.findBy({ userId });
  }

  /**
   * Find user device token by device token
   */
  async findByDeviceToken(
    deviceToken: string,
  ): Promise<UserDeviceToken | null> {
    return await this.userDeviceTokenRepository.findOneBy({ deviceToken });
  }

  /**
   * Update a user device token
   */
  async update(
    id: string,
    updateUserDeviceTokenDto: UpdateUserDeviceTokenDto,
  ): Promise<UserDeviceToken> {
    const userDeviceToken = await this.findOne(id);

    // If updating device token, check for conflicts
    if (updateUserDeviceTokenDto.deviceToken) {
      const existingToken = await this.userDeviceTokenRepository.findOneBy({
        deviceToken: updateUserDeviceTokenDto.deviceToken,
      });

      if (existingToken && existingToken.id !== id) {
        throw new ConflictException({
          code: 'USER_DEVICE_TOKEN_ALREADY_EXISTS',
        } as ErrorResponse);
      }
    }

    await this.userDeviceTokenRepository.update(id, updateUserDeviceTokenDto);
    return await this.findOne(id);
  }

  /**
   * Remove a user device token
   */
  async remove(id: string): Promise<void> {
    const userDeviceToken = await this.findOne(id);
    await this.userDeviceTokenRepository.delete(id);
  }

  /**
   * Remove user device token by device token
   */
  async removeByDeviceToken(deviceToken: string): Promise<void> {
    await this.userDeviceTokenRepository.deleteBy({ deviceToken });
  }

  /**
   * Remove all device tokens for a user
   */
  async removeByUserId(userId: string): Promise<void> {
    await this.userDeviceTokenRepository.deleteBy({ userId });
  }

  /**
   * Check if device token exists
   */
  async exists(deviceToken: string): Promise<boolean> {
    return await this.userDeviceTokenRepository.existsBy({ deviceToken });
  }

  /**
   * Get count of device tokens for a user
   */
  async countByUserId(userId: string): Promise<number> {
    return await this.userDeviceTokenRepository.count({ userId });
  }

  /**
   * Get all device tokens for a user (just the token strings)
   */
  async getDeviceTokensByUserId(userId: string): Promise<string[]> {
    const deviceTokens = await this.findByUserId(userId);
    return deviceTokens.map((token) => token.deviceToken);
  }

  /**
   * Register or update a device by deviceCode for a user. Returns the new/rotated device token entity.
   */
  async createOrUpdate(
    userId: string,
    payload: CreateUserDeviceTokenDto,
  ): Promise<UserDeviceToken> {
    const {
      deviceCode,
      deviceName,
      appVersion,
      osVersion,
      deviceType,
      deviceOs,
      deviceToken,
    } = payload;

    // Try to find existing record by userId + deviceCode
    const existing = await this.userDeviceTokenRepository.findOneBy({
      userId,
      deviceCode,
    });

    if (existing) {
      await this.userDeviceTokenRepository.update(existing.id, {
        deviceToken,
        deviceName,
        appVersion,
        osVersion,
        deviceType,
        deviceOs,
        isActive: true,
        lastActiveAt: new Date(),
      });
      return await this.findOne(existing.id);
    }

    const created = this.userDeviceTokenRepository.create({
      userId,
      deviceCode,
      deviceToken,
      deviceName,
      appVersion,
      osVersion,
      deviceType,
      deviceOs,
      isActive: true,
      lastActiveAt: new Date(),
    });

    return await this.userDeviceTokenRepository.save(created);
  }

  /**
   * Unregister a device by deviceCode or id for a user
   */
  async deleteByDeviceCodeOrId(
    userId: string,
    deviceCodeOrId: string,
  ): Promise<boolean> {
    const result = await this.userDeviceTokenRepository.deleteBy({
      userId,
      ...(uuidValidate(deviceCodeOrId)
        ? { id: deviceCodeOrId }
        : { deviceCode: deviceCodeOrId }),
    });

    return result.affected > 0;
  }
}
