import { Exclude, Expose } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import {
  USER_NOTIFICATION_TYPE,
  USER_NOTIFICATION_STATUS,
} from '@app/shared/database/entities';

@Exclude()
export class UserNotificationDto {
  @Expose()
  isDigestMail?: boolean;

  @Expose()
  isSubscribeEmail?: boolean;

  @Expose()
  isLineNotify?: boolean;
}

export class CreateUserNotificationDto {
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  deviceToken: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  content: string;

  @IsEnum(USER_NOTIFICATION_TYPE)
  @IsOptional()
  type?: USER_NOTIFICATION_TYPE = USER_NOTIFICATION_TYPE.NOTIFICATION;

  @IsEnum(USER_NOTIFICATION_STATUS)
  @IsOptional()
  status?: USER_NOTIFICATION_STATUS = USER_NOTIFICATION_STATUS.UNSENT;
}

export class UpdateUserNotificationDto {
  @IsString()
  @IsOptional()
  deviceToken?: string;

  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsEnum(USER_NOTIFICATION_TYPE)
  @IsOptional()
  type?: USER_NOTIFICATION_TYPE;

  @IsEnum(USER_NOTIFICATION_STATUS)
  @IsOptional()
  status?: USER_NOTIFICATION_STATUS;
}

export class FindUserNotificationDto {
  @IsUUID()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  deviceToken?: string;

  @IsEnum(USER_NOTIFICATION_TYPE)
  @IsOptional()
  type?: USER_NOTIFICATION_TYPE;

  @IsEnum(USER_NOTIFICATION_STATUS)
  @IsOptional()
  status?: USER_NOTIFICATION_STATUS;
}
