import {
  IsDateString,
  IsEmail,
  IsEnum,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { ApiHideProperty } from '@nestjs/swagger';
import {
  USER_LANGUAGE,
  UserGender,
  UserStatus,
} from '@app/shared/database/entities/user.entity';
import { IsPhoneNumber } from '@app/shared/decorators/is-phone-number.decorator';

export class UpdateUserDto {
  @ApiHideProperty()
  @IsOptional()
  @IsEmail({
    ignore_max_length: true,
  })
  @MaxLength(255)
  email?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  @MaxLength(255)
  username?: string;

  @IsString()
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @MaxLength(150)
  name?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  address?: string;

  @IsDateString()
  @IsOptional()
  birthday?: Date;

  @IsEnum(USER_LANGUAGE)
  @IsOptional()
  language?: USER_LANGUAGE;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsOptional()
  @IsBoolean()
  isAllowedNotify?: boolean;

  @IsOptional()
  @IsEnum(UserGender)
  gender?: UserGender;

  @ApiHideProperty()
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;
}
