import {
  Is<PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON>Optional,
  IsString,
  IsUUID,
} from 'class-validator';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities';

export class CreateUserSocialDto {
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  socialId: string;

  @IsEnum(SOCIAL_PROVIDER)
  @IsNotEmpty()
  provider: SOCIAL_PROVIDER;
}

export class UpdateUserSocialDto {
  @IsString()
  @IsOptional()
  socialId?: string;

  @IsEnum(SOCIAL_PROVIDER)
  @IsOptional()
  provider?: SOCIAL_PROVIDER;
}

export class FindUserSocialDto {
  @IsUUID()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  socialId?: string;

  @IsEnum(SOCIAL_PROVIDER)
  @IsOptional()
  provider?: SOCIAL_PROVIDER;
}

export class UserSocialDto {
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  socialId: string;

  @IsEnum(SOCIAL_PROVIDER)
  @IsNotEmpty()
  provider: SOCIAL_PROVIDER;
}
