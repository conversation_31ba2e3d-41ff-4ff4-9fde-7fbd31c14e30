import {
  USER_LANGUAGE,
  User<PERSON><PERSON>,
  User<PERSON><PERSON>,
} from '@app/shared/database/entities/user.entity';
import {
  IsEmail,
  isEmpty,
  IsEnum,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Matches,
  <PERSON>Length,
  <PERSON><PERSON>ength,
  ValidateIf,
} from 'class-validator';
import { IsPhoneNumber } from '@app/shared/decorators/is-phone-number.decorator';
import { IsEmailValidation } from '@app/shared/decorators/email-validation.decorator';

export class CreateUserDto {
  @IsNotEmpty()
  @IsEmail({
    ignore_max_length: true,
  })
  @IsEmailValidation() // Uses environment config BLOCK_EMAIL_PLUS_SYMBOL
  @MaxLength(255)
  email: string;

  @IsNotEmpty()
  @IsPhoneNumber()
  phone: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  name: string;

  @IsNotEmpty()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!\"#\$%&'()\^@\[\];:,\.\/=~\|`{\+\*}<>?_\-\"ー一−‐―-])\S+$/,
  )
  @MinLength(8)
  @MaxLength(128)
  password: string;

  @IsOptional()
  @IsUrl()
  @ValidateIf((values) => !isEmpty(values.avatar))
  avatar?: string;

  @IsString()
  @IsOptional()
  @IsEnum(USER_LANGUAGE)
  language?: string;

  @IsOptional()
  @IsEnum(UserGender)
  gender?: UserGender;

  @IsNotEmpty()
  @IsEnum(UserRole)
  role: UserRole;

  @IsOptional()
  @IsBoolean()
  isAllowedNotify?: boolean;
}
