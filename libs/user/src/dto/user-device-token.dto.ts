import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Length,
} from 'class-validator';

export class CreateUserDeviceTokenDto {
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  deviceToken: string;

  @ApiProperty({
    example: 'DEV-001',
    description: 'Unique device code per user',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  deviceCode: string;

  @ApiProperty({ example: 'iPhone 14 Pro' })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  deviceName?: string;

  @ApiProperty({ example: 'iOS' })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  deviceType?: string;

  @ApiProperty({ example: 'iOS' })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  deviceOs?: string;

  @ApiProperty({ example: '17.0.3' })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  osVersion?: string;

  @ApiProperty({ example: '1.0.0' })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  appVersion?: string;
}

export class UpdateUserDeviceTokenDto {
  @IsString()
  @IsOptional()
  deviceToken?: string;
}

export class FindUserDeviceTokenDto {
  @IsUUID()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  deviceToken?: string;
}
