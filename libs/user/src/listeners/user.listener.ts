import { MailService } from '@app/mail/services/mail.service';
import { USER_EVENT_TYPE } from '@app/user/constants';
import {
  UserCreatedEvent,
  UserDeletedEvent,
} from '@app/user/events/user.event';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { AppLogger } from '@app/shared/logger/app.logger';
import { CatchEventError } from '@app/shared/decorators/catch-event-error.decorator';
import { USER_LANGUAGE } from '@app/shared/database/entities/user.entity';
import {
  getMailTemplate,
  mapDataToTemplate,
} from '@app/shared/helpers/email.helper';
import { UserService } from '../services/user.service';
import { MAIL_TEMPLATE_TYPE } from '@app/mail/constants';

@Injectable()
export class UserListener {
  constructor(
    private readonly logger: AppLogger,
    private readonly mailService: MailService,
    private readonly userService: UserService,
  ) {}

  @OnEvent(USER_EVENT_TYPE.CREATED, { async: true })
  @CatchEventError()
  async handleUserCreatedEvent(event: UserCreatedEvent) {
    this.logger.debug('[handleUserCreatedEvent] ' + JSON.stringify(event));

    const { user } = event;

    await this.userService.sendMailVerifyEmail(user);
  }

  @OnEvent(USER_EVENT_TYPE.DELETED, { async: true })
  @CatchEventError()
  async handleUserDeletedEvent(event: UserDeletedEvent) {
    this.logger.debug('[handleUserDeletedEvent] ' + JSON.stringify(event));
    const user = event.user;
    const language: USER_LANGUAGE = user.language ?? USER_LANGUAGE.EN;

    const template = getMailTemplate(
      MAIL_TEMPLATE_TYPE.DELETE_USER_SUCCESS,
      language,
    );
    const content = await mapDataToTemplate(template, {
      name: user.name,
    });

    await this.mailService.send(user.email, template.subject, content);
  }
}
