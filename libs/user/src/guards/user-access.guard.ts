import {
  Injectable,
  CanActivate,
  Logger,
  Execution<PERSON>ontext,
  ParseU<PERSON><PERSON>ipe,
  NotFoundException,
  ForbiddenException,
  Inject,
} from '@nestjs/common';
import { UserRepositoryInterface } from '@app/shared/database/repositories';
import { DB_SERVICE_NAME } from '@app/shared/database/constants';
import { DatabaseService } from '@app/shared/database/services/database.service';
import { RequestWithUser } from '@app/shared/types/requests.type';
import { ErrorResponse } from '@app/shared/types';
import { UserRole } from '@app/shared/database/entities';

@Injectable()
export class UserAccessGuard implements CanActivate {
  logger: Logger = new Logger(UserAccessGuard.name);

  constructor(
    @Inject('UserRepositoryInterface')
    private userRepository: UserRepositoryInterface,
    @Inject(DB_SERVICE_NAME.BACKEND)
    private readonly databaseService: DatabaseService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    this.logger.debug('[UserAccessGuard]');
    const request = context.switchToHttp().getRequest<RequestWithUser>();
    const id: string = request.params.id ?? request.params.userId;
    const user = request.user;

    // Validate UUID format
    await new ParseUUIDPipe().transform(id, {
      type: 'param',
    });

    // Check if user exists
    await this.databaseService.createQueryRunner('slave');
    try {
      const userExists: boolean = await this.userRepository.exists({ id });
      if (!userExists) {
        throw new NotFoundException({
          code: 'USER_NOT_FOUND',
        } as ErrorResponse);
      }

      if (user.role === UserRole.ADMIN) {
        return true;
      }

      // Check if requesting user is the owner
      const isOwner: boolean = id === user.id;
      if (!isOwner) {
        throw new ForbiddenException({
          code: 'USER_NOT_OWNER',
        } as ErrorResponse);
      }

      return true;
    } catch (error) {
      throw error;
    } finally {
      await this.databaseService.release();
    }
  }
}
