import { User } from '@app/shared/database/entities';
import moment from 'moment';
import { UpdateUserDto } from '../dto/update-user.dto';

export class UserCreatedEvent {
  constructor(
    public readonly user: User,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class UserUpdatedEvent {
  constructor(
    public readonly user: User,
    public readonly payload: UpdateUserDto,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}

export class UserDeletedEvent {
  constructor(
    public readonly user: User,
    public readonly time?: number,
  ) {
    this.time = moment().unix();
  }
}
