import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { In } from 'typeorm';
import { FaqQuestion } from '@app/shared/database/entities';
import { FaqQuestionRepositoryInterface } from '@app/shared/database/repositories';
import { CreateFaqDto, UpdateFaqDto, SearchFaqDto } from '../dto/faq';
import { v4 as uuidv4 } from 'uuid';
import { first } from 'lodash';

@Injectable()
export class FaqService {
  constructor(
    @Inject('FaqQuestionRepositoryInterface')
    private readonly faqQuestionRepository: FaqQuestionRepositoryInterface,
  ) {}

  async list(filters: SearchFaqDto): Promise<FaqQuestion[]> {
    const { language, search, type, is_active, is_expanded } = filters;

    const queryBuilder = this.faqQuestionRepository
      .createQueryBuilder('master')
      .where('master.parent_id IS NULL');

    // Apply master filters
    if (type) {
      queryBuilder.andWhere('master.type = :type', { type });
    }

    if (is_active !== undefined) {
      queryBuilder.andWhere('master.is_active = :isActive', {
        isActive: is_active,
      });
    }

    if (is_expanded !== undefined) {
      queryBuilder.andWhere('master.is_expanded = :isExpanded', {
        isExpanded: is_expanded,
      });
    }

    // Apply children filters and join when needed
    if (language || search) {
      queryBuilder.leftJoin(
        'faq_questions',
        'children',
        'children.parent_id = master.id',
      );

      if (language) {
        queryBuilder.andWhere('children.language = :language', {
          language,
        });
      }

      if (search && search.trim()) {
        const keywords = search
          .trim()
          .split(/\s+/)
          .filter((k) => k.length > 0);

        if (keywords.length > 0) {
          const keywordConditions = keywords.map((_, index) => {
            const param = `kw${index}`;
            return `(
              children.title ILIKE :${param} OR 
              children.content ILIKE :${param}
            )`;
          });

          const params = keywords.reduce(
            (acc, kw, index) => {
              acc[`kw${index}`] = `%${kw}%`;
              return acc;
            },
            {} as Record<string, string>,
          );

          queryBuilder.andWhere(keywordConditions.join(' AND '), params);
        }
      }

      // Add distinct to avoid duplicate master records
      queryBuilder.distinctOn(['master.id']);
    }

    return queryBuilder
      .orderBy('master.display_order', 'ASC')
      .addOrderBy('master.created_at', 'DESC')
      .getMany();
  }

  async create(dto: CreateFaqDto): Promise<FaqQuestion> {
    const { items, ...masterData } = dto;

    // Generate ID for master FAQ record
    const masterId = uuidv4();

    // Create master FAQ record with pre-generated ID
    const masterFaq = this.faqQuestionRepository.create({
      id: masterId,
      ...masterData,
      parent_id: null, // Master record has no parent
      is_active: masterData?.is_active || false,
    });

    // Create child FAQ records for each language with master ID
    const childFaqs = items.map((item) =>
      this.faqQuestionRepository.create({
        parent_id: masterId,
        language: item.language,
        title: item.title,
        content: item.content,
      }),
    );

    // Save both master and children in a single transaction
    await this.faqQuestionRepository.save([masterFaq, ...childFaqs]);

    return masterFaq;
  }

  async processFaq(masterFaq: FaqQuestion): Promise<FaqQuestion> {
    return first(await this.processFaqs([masterFaq]));
  }

  async processFaqs(masterFaqs: FaqQuestion[]): Promise<FaqQuestion[]> {
    if (masterFaqs.length === 0) {
      return [];
    }

    const masterIds = masterFaqs.map((faq) => faq.id);

    // Get all children FAQs for all master FAQs in one query
    const allChildren = await this.faqQuestionRepository.find({
      where: { parent_id: In(masterIds) },
      order: { language: 'ASC' },
    });

    // Map master FAQs with their children
    return masterFaqs.map((masterFaq) => ({
      ...masterFaq,
      children: allChildren.filter((child) => child.parent_id === masterFaq.id),
    }));
  }

  async update(dto: UpdateFaqDto): Promise<FaqQuestion> {
    const { id, items, ...masterData } = dto;

    // Check if FAQ exists
    const existingFaq = await this.faqQuestionRepository.findOneBy({
      id,
      parent_id: null,
    });

    if (!existingFaq) {
      throw new NotFoundException({
        code: 'FAQ_NOT_FOUND',
        message: 'FAQ not found',
      });
    }

    // Update master FAQ record
    await this.faqQuestionRepository.update(id, masterData);

    // Delete all existing child records
    await this.faqQuestionRepository.deleteBy({ parent_id: id });

    // Create new child FAQ records
    const childFaqs = items.map((item) =>
      this.faqQuestionRepository.create({
        parent_id: id,
        language: item.language,
        title: item.title,
        content: item.content,
      }),
    );

    await this.faqQuestionRepository.save(childFaqs);

    return {
      ...existingFaq,
      ...masterData,
    };
  }

  async delete(id: string): Promise<boolean> {
    // Check if FAQ exists
    const existingFaq = await this.faqQuestionRepository.findOneBy({
      id,
      parent_id: null,
    });

    if (!existingFaq) {
      throw new NotFoundException({
        code: 'FAQ_NOT_FOUND',
        message: 'FAQ not found',
      });
    }

    // Soft delete both master FAQ and all child FAQs
    await Promise.all([
      this.faqQuestionRepository.softDelete(id),
      this.faqQuestionRepository.softDelete({ parent_id: id }),
    ]);
    return true;
  }
}
