export enum SYSTEM_SETTING_KEY {
  BONUS_POINT_RATIO = 'payment.bonus_point_ratio',
  EXCHANGE_RATE_VND = 'payment.exchange_rate.vnd',

  // Company settings
  COMPANY_NAME = 'company.name',
  COMPANY_EMAIL = 'company.email',
  COMPANY_PHONE = 'company.phone',
  TECHNICAL_PHONE = 'company.technical_phone',
  COMPANY_ADDRESS = 'company.address',
  WORKING_HOURS = 'company.working_hours',
  COMPANY_LOGO = 'company.logo',

  // Payment settings
  INITIAL_POINT = 'payment.initial_point',
}

export const SYSTEM_SETTING_KEY_SCHEMA = {
  [SYSTEM_SETTING_KEY.BONUS_POINT_RATIO]: {
    type: 'number',
    minimum: 0,
    maximum: 1,
  },
  [SYSTEM_SETTING_KEY.EXCHANGE_RATE_VND]: {
    type: 'number',
    minimum: 0,
    maximum: 1000000,
  },

  // Company settings schemas
  [SYSTEM_SETTING_KEY.COMPANY_NAME]: {
    type: 'string',
    minLength: 1,
    maxLength: 255,
  },
  [SYSTEM_SETTING_KEY.COMPANY_EMAIL]: {
    type: 'string',
    format: 'email',
    maxLength: 255,
  },
  [SYSTEM_SETTING_KEY.COMPANY_PHONE]: {
    type: 'string',
    format: 'phone',
    pattern: '^[0-9+\\-\\s()]+$',
    maxLength: 20,
  },
  [SYSTEM_SETTING_KEY.TECHNICAL_PHONE]: {
    type: 'string',
    format: 'phone',
    pattern: '^[0-9+\\-\\s()]+$',
    maxLength: 20,
  },
  [SYSTEM_SETTING_KEY.COMPANY_ADDRESS]: {
    type: 'string',
    maxLength: 500,
  },
  [SYSTEM_SETTING_KEY.WORKING_HOURS]: {
    type: 'string',
    format: 'TimeRange',
    maxLength: 100,
  },
  [SYSTEM_SETTING_KEY.COMPANY_LOGO]: {
    type: 'string',
    format: 'url',
    maxLength: 500,
  },
};
