import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsString,
  IsNotEmpty,
  IsOptional,
  ValidateIf,
  isEmpty,
} from 'class-validator';
import { plainToInstance, Transform, Type } from 'class-transformer';
import { isArray } from 'lodash';

export class SystemSettingItemDto {
  @ApiProperty({
    description: 'Setting key',
    example: 'payment.bonus_point_ratio',
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'Setting value',
    example: 0.02,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (isEmpty(value)) return '';
    return value;
  })
  value: any;

  @ApiProperty({
    description: 'Setting type',
    example: 'number',
  })
  @IsString()
  @IsNotEmpty()
  type: any;

  @ApiProperty({
    description: 'Setting category (optional, defaults to "other")',
    example: 'payment',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({
    description: 'Setting description (optional)',
    example: 'Bonus point ratio for payment transactions',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateSystemSettingsDto {
  @ApiProperty({
    description: 'Array of settings to update',
    type: [SystemSettingItemDto],
    example: [
      {
        key: 'payment.bonus_point_ratio',
        value: 0.02,
        type: 'number',
        category: 'payment',
        description: 'Bonus point ratio for payment transactions',
      },
      {
        key: 'app.name',
        value: 'New App Name',
        type: 'string',
        category: 'app',
        description: 'Application name',
      },
    ],
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (isEmpty(value)) return [];
    if (isArray(value)) return value;
    return plainToInstance(SystemSettingItemDto, JSON.parse(value));
  })
  @ValidateNested({ each: true })
  @Type(() => SystemSettingItemDto)
  settings: SystemSettingItemDto[];

  @ApiProperty({ type: 'string', format: 'binary' })
  logo?: Express.Multer.File;
}
