import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsArray } from 'class-validator';
import { Transform, Expose } from 'class-transformer';

export class ListSystemSettingsDto {
  @ApiProperty({
    description: 'Filter by setting keys (use keys[]=value format)',
    example: 'payment.bonus_point_ratio',
    isArray: true,
  })
  @Expose({ name: 'keys[]' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (Array.isArray(value)) return value;
    return [value];
  })
  keys?: string[];

  @ApiProperty({
    description: 'Filter by active status',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active?: boolean;

  @ApiProperty({
    description: 'Filter by category',
    example: 'payment',
  })
  @IsOptional()
  @IsString()
  category?: string;
}
