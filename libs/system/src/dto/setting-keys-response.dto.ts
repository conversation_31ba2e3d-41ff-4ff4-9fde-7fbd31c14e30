import { ApiProperty } from '@nestjs/swagger';
import {
  SYSTEM_SETTING_KEY,
  SYSTEM_SETTING_KEY_SCHEMA,
} from '../const/system.constant';

export class SettingKeysResponseDto {
  @ApiProperty({
    description: 'Available setting keys',
    example: {
      BONUS_POINT_RATIO: 'payment.bonus_point_ratio',
      EXCHANGE_RATE_VND: 'payment.exchange_rate.vnd',
    },
  })
  keys: typeof SYSTEM_SETTING_KEY;

  @ApiProperty({
    description: 'Validation schemas for each key',
    example: {
      'payment.bonus_point_ratio': {
        type: 'number',
        minimum: 0,
        maximum: 1,
      },
      'payment.exchange_rate.vnd': {
        type: 'number',
        minimum: 0,
        maximum: 1000000,
      },
    },
  })
  schemas: typeof SYSTEM_SETTING_KEY_SCHEMA;
}
