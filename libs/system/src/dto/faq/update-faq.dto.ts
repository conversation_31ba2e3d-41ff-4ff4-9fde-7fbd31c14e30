import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsInt,
  Min,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsIn,
  IsNotEmpty,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateFaqItemDto {
  @ApiProperty({
    description: 'Language code',
    example: 'vi',
    enum: ['vi', 'ja', 'en'],
  })
  @IsString()
  @IsIn(['vi', 'ja', 'en'])
  language: string;

  @ApiProperty({
    description: 'Question title',
    example: 'Faq question title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Question content/answer',
    example: 'Faq question content',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}

export class UpdateFaqDto {
  @ApiProperty({
    description: 'FAQ ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiPropertyOptional({
    description: 'Question type',
    example: 'faq',
    enum: ['faq', 'help', 'tutorial'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['faq', 'help', 'tutorial'])
  type?: string;

  @ApiPropertyOptional({
    description: 'URL-friendly identifier',
    example: 'faq-1',
  })
  @IsString()
  @IsOptional()
  slug?: string;

  @ApiProperty({
    description: 'Display order for sorting',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  display_order: number;

  @ApiPropertyOptional({
    description: 'Question active status',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: 'Default expanded state',
    example: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_expanded?: boolean;

  @ApiPropertyOptional({
    description: 'Additional question metadata',
    example: { category: 'general' },
  })
  @IsOptional()
  metadata?: any;

  @ApiProperty({
    description: 'FAQ items for different languages',
    type: [UpdateFaqItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateFaqItemDto)
  items: UpdateFaqItemDto[];
}
