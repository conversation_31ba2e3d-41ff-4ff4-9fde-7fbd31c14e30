import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsInt,
  Min,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsIn,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateFaqItemDto {
  @ApiProperty({
    description: 'Language code',
    example: 'vi',
    enum: ['vi', 'ja', 'en'],
  })
  @IsString()
  @IsIn(['vi', 'ja', 'en'])
  language: string;

  @ApiProperty({
    description: 'Question title',
    example: 'Faq question title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Question content/answer',
    example: 'Faq question content',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}

export class CreateFaqDto {
  @ApiPropertyOptional({
    description: 'Question type',
    example: 'faq',
    enum: ['faq', 'help', 'tutorial'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['faq', 'help', 'tutorial'])
  type?: string;

  @ApiPropertyOptional({
    description: 'URL-friendly identifier',
    example: 'faq-1',
  })
  @IsString()
  @IsOptional()
  slug?: string;

  @ApiProperty({
    description: 'Display order for sorting',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  display_order: number;

  @ApiPropertyOptional({
    description: 'Question active status',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: 'Default expanded state',
    example: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_expanded?: boolean;

  @ApiPropertyOptional({
    description: 'Additional question metadata',
    example: { category: 'general' },
  })
  @IsOptional()
  metadata?: any;

  @ApiProperty({
    description: 'FAQ items for different languages',
    type: [CreateFaqItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateFaqItemDto)
  items: CreateFaqItemDto[];
}
