import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class FaqItemResponseDto {
  @ApiProperty({
    description: 'Language code',
    example: 'vi',
  })
  language: string;

  @ApiProperty({
    description: 'Question title',
    example: 'Faq question title',
  })
  title: string;

  @ApiProperty({
    description: 'Question content/answer',
    example: 'Faq question content',
  })
  content: string;
}

export class FaqResponseDto {
  @ApiProperty({
    description: 'FAQ ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Question type',
    example: 'faq',
  })
  type: string;

  @ApiPropertyOptional({
    description: 'URL-friendly identifier',
    example: 'faq-1',
  })
  slug: string;

  @ApiProperty({
    description: 'Display order for sorting',
    example: 1,
  })
  display_order: number;

  @ApiProperty({
    description: 'Question active status',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'Default expanded state',
    example: false,
  })
  is_expanded: boolean;

  @ApiPropertyOptional({
    description: 'Additional question metadata',
    example: { category: 'general' },
  })
  metadata: any;

  @ApiProperty({
    description: 'Question creation time',
    example: '2024-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Question update time',
    example: '2024-01-01T00:00:00.000Z',
  })
  updated_at: Date;

  @ApiProperty({
    description: 'FAQ items for different languages',
    type: [FaqItemResponseDto],
  })
  items: FaqItemResponseDto[];
}
