import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsIn } from 'class-validator';

export class SearchFaqDto {
  @ApiPropertyOptional({
    description: 'Language code for filtering',
    example: 'vi',
    enum: ['vi', 'ja', 'en'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['vi', 'ja', 'en'])
  language?: string;

  @ApiPropertyOptional({
    description: 'Search keyword in title and content',
    example: 'faq question',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Question type filter',
    example: 'faq',
    enum: ['faq', 'help', 'tutorial'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['faq', 'help', 'tutorial'])
  type?: string;

  @ApiPropertyOptional({
    description: 'Filter by active status',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value == '1' || value == 'true' || value == true) return true;
    if (value == '0' || value == 'false' || value == false) return false;
    return undefined;
  })
  is_active?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by expanded state',
    example: false,
  })
  @Transform(({ value }) => {
    if (value == '1' || value == 'true' || value == true) return true;
    if (value == '0' || value == 'false' || value == false) return false;
    return undefined;
  })
  @IsOptional()
  is_expanded?: boolean;
}
