import { ApiProperty } from '@nestjs/swagger';

export class SystemSettingResponseDto {
  @ApiProperty({
    description: 'Setting ID',
    example: '1',
  })
  id: string;

  @ApiProperty({
    description: 'Setting key',
    example: 'payment.bonus_point_ratio',
  })
  key: string;

  @ApiProperty({
    description: 'Setting value',
    example: '2',
  })
  value: string;

  @ApiProperty({
    description: 'Value type',
    example: 'number',
  })
  type: string;

  @ApiProperty({
    description: 'Setting category',
    example: 'payment',
  })
  category: string;

  @ApiProperty({
    description: 'Setting description',
    example: 'Point earning rate percentage',
  })
  description: string;

  @ApiProperty({
    description: 'Active status',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  updated_at: Date;
}
