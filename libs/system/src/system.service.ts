import { Injectable, Inject } from '@nestjs/common';
import { In } from 'typeorm';
import { SystemConfiguration } from '@app/shared/database/entities';
import { SystemConfigurationRepositoryInterface } from '@app/shared/database/repositories';
import { SYSTEM_SETTING_KEY } from './const/system.constant';
import { ListSystemSettingsDto } from './dto/list-system-settings.dto';
import { UpdateSystemSettingsDto } from './dto/update-system-settings.dto';
import { S3Service } from '@app/aws';

@Injectable()
export class SystemService {
  constructor(
    @Inject('SystemConfigurationRepositoryInterface')
    private readonly systemConfigurationRepository: SystemConfigurationRepositoryInterface,
    private readonly s3Service: S3Service,
  ) {}

  async list(filters: ListSystemSettingsDto): Promise<SystemConfiguration[]> {
    const { keys, is_active, category } = filters;

    const where: any = {};

    if (keys?.length) {
      where.key = In(keys);
    }

    if (is_active !== undefined) {
      where.is_active = is_active;
    }

    if (category) {
      where.category = category;
    }

    return this.systemConfigurationRepository.find({
      where,
      order: { key: 'ASC' },
    });
  }

  async updateSettings(
    dto: UpdateSystemSettingsDto,
  ): Promise<SystemConfiguration[]> {
    const { settings } = dto;
    const keys = settings.map((setting) => setting.key);

    // Delete all existing settings with these keys
    await this.systemConfigurationRepository.deleteBy({ key: In(keys) });

    // Create and save all new settings
    const newConfigs = settings.map((setting) => {
      const { key, value, type, category, description } = setting;

      return this.systemConfigurationRepository.create({
        key,
        value: value ?? '',
        type,
        category,
        description,
        is_active: true,
      });
    });

    return this.systemConfigurationRepository.save(newConfigs);
  }

  async processSettings(
    settings: SystemConfiguration[],
  ): Promise<SystemConfiguration[]> {
    if (settings.length === 0) {
      return [];
    }

    return await Promise.all(
      settings.map(async (setting) => {
        if (setting.type === 'url' && setting.value) {
          const tempUrl = await this.s3Service.getTempUrl(setting.value);
          return { ...setting, value: tempUrl } as SystemConfiguration;
        }
        return { ...setting } as SystemConfiguration;
      }),
    );
  }
}
