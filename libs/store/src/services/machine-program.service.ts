import { Injectable, Inject } from '@nestjs/common';
import { ProductRepositoryInterface } from '@app/shared/database/repositories';
import { MachineProgramDto, ProductDto } from '@app/store/dto';
import { MachineProgram } from '@app/shared/database/entities';
import { In } from 'typeorm';
import { map, uniq } from 'lodash';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class MachineProgramService {
  constructor(
    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,
  ) {}

  async processMachinePrograms(
    machinePrograms: MachineProgram[],
  ): Promise<MachineProgramDto[]> {
    if (machinePrograms.length === 0) {
      return [];
    }

    const products = await this.productRepository.find({
      where: {
        id: In(uniq(map(machinePrograms, 'productId'))),
      },
    });

    return machinePrograms.map((program) => {
      const product = products.find((p) => p.id === program.productId);
      const machineProgramDto = plainToInstance(MachineProgramDto, program);
      machineProgramDto.product = product
        ? plainToInstance(ProductDto, product)
        : null;
      return machineProgramDto;
    });
  }
}
