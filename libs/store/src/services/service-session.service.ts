import { Injectable, Inject } from '@nestjs/common';
import {
  MachineProgramRepositoryInterface,
  ServiceSessionRepositoryInterface,
} from '@app/shared/database/repositories';
import {
  Order,
  OrderItem,
  ServiceSession,
  ServiceSessionStatus,
} from '@app/shared/database/entities';
import { In } from 'typeorm';
import { SERVICE_SESSION_EVENTS } from '../constants';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { map, uniq } from 'lodash';
import {
  ServiceSessionCompletedEvent,
  ServiceSessionDoneEvent,
  ServiceSessionStartedEvent,
} from '../events/service-session.events';
import moment from 'moment';
import appConfig from '@app/shared/config/app.config';

@Injectable()
export class ServiceSessionService {
  constructor(
    @Inject('ServiceSessionRepositoryInterface')
    private readonly serviceSessionRepository: ServiceSessionRepositoryInterface,
    @Inject('MachineProgramRepositoryInterface')
    private readonly machineProgramRepository: MachineProgramRepositoryInterface,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async getLatestServiceSessionsByMachineIds(
    machineIds: string[],
  ): Promise<ServiceSession[]> {
    if (machineIds.length === 0) {
      return [];
    }

    return await this.serviceSessionRepository
      .createQueryBuilder('ss')
      .distinctOn(['ss.machineId'])
      .where('ss.machineId IN (:...machineIds)', { machineIds })
      .orderBy('ss.machineId', 'ASC')
      .addOrderBy('ss.createdAt', 'DESC')
      .getMany();
  }

  async getLatestServiceSessionsByUserId(
    userIds: string[],
  ): Promise<ServiceSession[]> {
    if (userIds.length === 0) {
      return [];
    }

    return await this.serviceSessionRepository
      .createQueryBuilder('ss')
      .distinctOn(['ss.userId'])
      .where('ss.userId IN (:...userIds)', { userIds })
      .orderBy('ss.userId', 'ASC')
      .addOrderBy('ss.createdAt', 'DESC')
      .getMany();
  }

  async getServiceSessionsEndingIn(options: {
    minutesBefore: number;
    limit?: number;
    skip?: number;
  }): Promise<ServiceSession[]> {
    const bufferSeconds = appConfig().CRONJOB.ENDING_SESSION_BUFFER_SECONDS;
    let { minutesBefore, limit, skip } = options;
    minutesBefore = Number(minutesBefore);
    const lowerBound = moment()
      .add(minutesBefore, 'minutes')
      .subtract(bufferSeconds, 'seconds')
      .toDate();
    const upperBound = moment()
      .add(minutesBefore, 'minutes')
      .add(bufferSeconds, 'seconds')
      .toDate();
    const remindBefore = moment().subtract(minutesBefore, 'minutes').toDate();

    const query = await this.serviceSessionRepository
      .createQueryBuilder('ss')
      .select(['ss'])
      .innerJoin('ss.user', 'u')
      .where('ss.status = :status', { status: ServiceSessionStatus.IN_USE })
      .andWhere('ss.estimatedEndTime IS NOT NULL')
      .andWhere('(ss.remindedAt IS NULL OR ss.remindedAt <= :remindBefore)', {
        remindBefore,
      })
      .andWhere('u.isAllowedNotify = :isAllowed', { isAllowed: true })
      .andWhere('ss.estimatedEndTime BETWEEN :lowerBound AND :upperBound', {
        lowerBound,
        upperBound,
      })
      .orderBy('ss.estimatedEndTime', 'ASC');

    if (limit) {
      query.take(limit);
    }

    if (skip) {
      query.skip(skip);
    }
    return query.getMany();
  }

  async getServiceSessionsCompleted(options?: {
    limit?: number;
    skip?: number;
    completedBefore?: Date;
    completedAfter?: Date;
  }): Promise<ServiceSession[]> {
    const query = await this.serviceSessionRepository
      .createQueryBuilder('ss')
      .where('ss.status = :status', { status: ServiceSessionStatus.IN_USE })
      .orderBy('ss.estimatedEndTime', 'ASC');
    if (options?.completedBefore) {
      query.andWhere('ss.estimatedEndTime <= :completedBefore', {
        completedBefore: options.completedBefore,
      });
    }
    if (options?.completedAfter) {
      query.andWhere('ss.estimatedEndTime >= :completedAfter', {
        completedAfter: options.completedAfter,
      });
    }

    if (options?.limit) {
      query.take(options.limit);
    }

    if (options?.skip) {
      query.skip(options.skip);
    }
    return query.getMany();
  }

  async markAsCompleted(sessions: ServiceSession[]): Promise<void> {
    const sessionIds = map(sessions, 'id');
    const completedAt = new Date();
    await this.serviceSessionRepository.updateBy(
      { id: In(sessionIds) },
      { status: ServiceSessionStatus.COMPLETED, completedAt },
    );

    this.eventEmitter.emit(
      SERVICE_SESSION_EVENTS.COMPLETED,
      new ServiceSessionCompletedEvent(sessions, completedAt),
    );
  }

  async markAsDone(sessions: ServiceSession[]): Promise<void> {
    const sessionIds = map(sessions, 'id');
    await this.serviceSessionRepository.updateBy(
      { id: In(sessionIds) },
      { status: ServiceSessionStatus.DONE },
    );

    this.eventEmitter.emit(
      SERVICE_SESSION_EVENTS.DONE,
      new ServiceSessionDoneEvent(sessions, new Date()),
    );
  }

  async markAsDoneByMachines(machineIds: string[]): Promise<void> {
    await this.serviceSessionRepository.updateBy(
      { machineId: In(machineIds), status: ServiceSessionStatus.COMPLETED },
      { status: ServiceSessionStatus.DONE },
    );
  }

  async startServiceSession(order: Order): Promise<void> {
    if (order.items.length === 0) {
      return;
    }

    const programs = await this.machineProgramRepository.findBy({
      productId: In(uniq(map(order.items, 'productId'))),
    });

    const serviceSessions = await this.serviceSessionRepository.save(
      order.items.map((item) => {
        const program = programs.find((p) => p.productId === item.productId);

        const estimatedEndTime = new Date(
          new Date().getTime() + program.durationMinutes * 60 * 1000,
        );

        return this.serviceSessionRepository.create({
          userId: order.userId,
          orderId: order.id,
          orderItemId: item.id,
          machineId: program.machineId,
          machineProgramId: program.id,
          status: ServiceSessionStatus.IN_USE,
          startedAt: new Date(),
          estimatedEndTime,
          completedAt: null,
          remindedAt: null,
          createdAt: new Date(),
        });
      }),
    );

    this.eventEmitter.emit(
      SERVICE_SESSION_EVENTS.STARTED,
      new ServiceSessionStartedEvent(serviceSessions),
    );
  }

  async getOutdatedCompletedSessions(options?: {
    completedBefore: Date;
    completedAfter?: Date;
    limit?: number;
    skip?: number;
  }): Promise<ServiceSession[]> {
    const { completedBefore, completedAfter, limit, skip } = options;
    const query = await this.serviceSessionRepository
      .createQueryBuilder('ss')
      .where('ss.status = :status', { status: ServiceSessionStatus.COMPLETED })
      .andWhere('ss.estimatedEndTime < :completedBefore', { completedBefore });

    if (completedAfter) {
      query.andWhere('ss.estimatedEndTime > :completedAfter', {
        completedAfter,
      });
    }

    if (limit) {
      query.take(limit);
    }

    if (skip) {
      query.skip(skip);
    }

    return query.getMany();
  }
}
