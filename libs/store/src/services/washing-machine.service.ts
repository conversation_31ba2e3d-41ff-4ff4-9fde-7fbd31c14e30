import {
  Injectable,
  Inject,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  WashingMachineRepositoryInterface,
  ProductRepositoryInterface,
  MachineProgramRepositoryInterface,
  ServiceSessionRepositoryInterface,
  StoreRepositoryInterface,
  OrderItemRepositoryInterface,
} from '@app/shared/database/repositories';
import { WASHING_MACHINE_EVENTS } from '../constants';
import { WashingMachineDeletedEvent } from '../events/washing-machine.events';
import {
  MachineProgramDto,
  ServiceSessionDto,
  StoreDto,
  WashingMachineCreateDto,
  WashingMachineUpdateDto,
  WashingMachineDto,
  MachineProgramCreateDto,
} from '@app/store/dto';
import {
  WashingMachine,
  MachineType,
  MachineStatus,
  ProductType,
  ProductCategory,
  ProductStatus,
  MachineProgram,
  Product,
  ServiceSession,
  MachineStatusPriority,
  Store,
  OrderItem,
  Order,
  OrderStatus,
} from '@app/shared/database/entities';
import { v4, validate as uuidValidate } from 'uuid';
import { Brackets, In, SelectQueryBuilder, Not } from 'typeorm';
import { first, map, uniq, keys, pickBy, omit, isEmpty, some } from 'lodash';
import { plainToInstance } from 'class-transformer';
import { MachineProgramService } from './machine-program.service';
import { ServiceSessionService } from '@app/store/services/service-session.service';
import { SearchWashingMachinesQueryDto } from '@app/store/dto/search-washing-machine.dto';
import { PaginationResponse } from '@app/shared/types/common.type';
import { ServiceSessionStatus } from '@app/shared/database/entities/service-session.entity';
import { DatabaseService } from '@app/shared/database/services/database.service';
import { DB_SERVICE_NAME } from '@app/shared/database/constants';
import { MachineProgramUpdateDto } from '../dto/machine-program-update.dto';

@Injectable()
export class WashingMachineService {
  constructor(
    @Inject('WashingMachineRepositoryInterface')
    private readonly washingMachineRepository: WashingMachineRepositoryInterface,
    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,
    @Inject('MachineProgramRepositoryInterface')
    private readonly machineProgramRepository: MachineProgramRepositoryInterface,
    @Inject('ServiceSessionRepositoryInterface')
    private readonly serviceSessionRepository: ServiceSessionRepositoryInterface,
    @Inject('StoreRepositoryInterface')
    private readonly storeRepository: StoreRepositoryInterface,
    @Inject('OrderItemRepositoryInterface')
    private readonly orderItemRepository: OrderItemRepositoryInterface,
    private readonly machineProgramService: MachineProgramService,
    private readonly serviceSessionService: ServiceSessionService,
    @Inject(DB_SERVICE_NAME.BACKEND)
    private readonly databaseService: DatabaseService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(payload: WashingMachineCreateDto, storeId: string) {
    // Check if machine code already exists
    const existingMachine = await this.washingMachineRepository.findOneBy({
      code: payload.code,
    });
    if (existingMachine) {
      throw new ConflictException({
        code: 'MACHINE_CODE_EXISTS',
        message: 'Washing machine code already exists.',
      });
    }

    const washingMachine = await this.washingMachineRepository.save(
      this.washingMachineRepository.create({
        storeId,
        code: payload.code,
        name: payload.name,
        model: payload.model,
        type: payload.type ?? MachineType.COMBO,
        status: MachineStatus.AVAILABLE,
        capacityKg: payload.capacityKg,
        features: {
          name: payload.name,
          model: payload.model,
          capacity: payload.capacityKg,
        },
        options: payload.machinePrograms,
      }),
    );

    await this.createMachineProgramAndProduct(
      washingMachine,
      payload.machinePrograms,
    );

    return {
      id: washingMachine.id,
      code: washingMachine.code,
      name: washingMachine.name,
      model: washingMachine.model,
      storeId: washingMachine.storeId,
      capacityKg: washingMachine.capacityKg,
      status: washingMachine.status,
      createdAt: washingMachine.createdAt,
    };
  }

  async findByIdOrCode(identifier: string): Promise<WashingMachine | null> {
    return await this.washingMachineRepository.findOne({
      where: {
        ...(uuidValidate(identifier)
          ? { id: identifier }
          : { code: identifier }),
      },
    });
  }

  async processWashingMachine(
    machine: WashingMachine,
    options?: {
      userId?: string;
      includePrograms?: boolean;
      includeStore?: boolean;
      includeLatestServiceSession?: boolean;
    },
  ): Promise<WashingMachineDto> {
    return first(await this.processWashingMachines([machine], options));
  }

  async processWashingMachines(
    machines: WashingMachine[],
    options?: {
      userId?: string;
      includePrograms?: boolean;
      includeStore?: boolean;
      includeLatestServiceSession?: boolean;
      withDeleted?: boolean;
    },
  ): Promise<WashingMachineDto[]> {
    if (machines.length === 0) {
      return [];
    }

    const {
      userId,
      includePrograms = true,
      includeStore = true,
      includeLatestServiceSession = true,
      withDeleted = false,
    } = options ?? {};
    const machineIds = uniq(map(machines, 'id'));

    // Initialize variables to avoid null checks
    let usedMachineIds: string[] = [];
    let stores: Store[] = [];
    let machineProgramsDtos: MachineProgramDto[] = [];
    let lastSessions: ServiceSession[] = [];

    if (includeStore) {
      stores = await this.storeRepository.find({
        where: {
          id: In(uniq(map(machines, 'storeId'))),
        },
        ...(withDeleted ? { withDeleted: true } : {}),
      });
    }

    if (includePrograms) {
      const machinePrograms = await this.machineProgramRepository.find({
        where: {
          machineId: In(machineIds),
        },
        ...(withDeleted ? { withDeleted: true } : {}),
      });

      machineProgramsDtos =
        await this.machineProgramService.processMachinePrograms(
          machinePrograms,
        );
    }

    if (includeLatestServiceSession) {
      lastSessions =
        await this.serviceSessionService.getLatestServiceSessionsByMachineIds(
          machineIds,
        );
    }

    // Get used machine ids
    if (userId) {
      const usedServiceSessions = await this.serviceSessionRepository.find({
        where: {
          userId,
          status: In([
            ServiceSessionStatus.DONE,
            ServiceSessionStatus.COMPLETED,
          ]),
          machineId: In(machineIds),
        },
        order: {
          createdAt: 'DESC',
        },
      });

      usedMachineIds = uniq(map(usedServiceSessions, 'machineId'));
    }

    return machines.map((machine) => {
      const machineDto = plainToInstance(WashingMachineDto, machine);
      machineDto.programs = machineProgramsDtos.filter(
        (program) => program.machineId === machine.id,
      );

      const latestSession = lastSessions.find(
        (session) => session.machineId === machine.id,
      );

      const store = stores.find((s) => s.id === machine.storeId);

      machineDto.latestServiceSession = latestSession
        ? plainToInstance(ServiceSessionDto, latestSession)
        : null;

      machineDto.store = store ? plainToInstance(StoreDto, store) : null;
      machineDto.hasUsed = usedMachineIds.includes(machine.id);
      return machineDto;
    });
  }

  async list(
    query: SearchWashingMachinesQueryDto,
    currentUserId?: string,
  ): Promise<PaginationResponse<WashingMachine>> {
    const {
      storeId,
      status,
      keyword,
      isUsingByMe,
      skip = 0,
      limit = 20,
    } = query;

    const qb = this.washingMachineRepository
      .createQueryBuilder('wm')
      .leftJoin(
        ServiceSession,
        'ss_latest',
        'ss_latest.machineId = wm.id AND ss_latest.status IN (:...ongoingStatuses)',
        {
          ongoingStatuses: [
            ServiceSessionStatus.IN_USE,
            ServiceSessionStatus.COMPLETED,
          ],
        },
      );

    if (storeId) {
      qb.andWhere('wm.storeId = :storeId', { storeId });
    }

    if (status?.length) {
      qb.andWhere('wm.status IN (:...status)', { status });
    }

    if (keyword) {
      const keys = keyword
        .split(' ')
        .map((t) => t.trim())
        .filter((t) => t);

      // search by name, split by space and search by each key with AND ILIKE
      if (keys.length) {
        qb.andWhere(
          new Brackets((sqb) => {
            keys.forEach((key, index) => {
              const param = `token_${index}`;
              sqb.andWhere(
                `(wm.name ILIKE :${param} OR wm.code ILIKE :${param})`,
                {
                  [param]: `%${key}%`,
                },
              );
            });
          }),
        );
      }
    }

    if (isUsingByMe) {
      // only machines whose latest session is being used by current user
      qb.andWhere(
        'ss_latest.userId = :currentUserId AND ss_latest.status IN (:...ongoing)',
        {
          currentUserId,
          ongoing: [
            ServiceSessionStatus.WAITING,
            ServiceSessionStatus.IN_USE,
            ServiceSessionStatus.COMPLETED,
          ],
        },
      );
    }

    const countQb = qb.clone();

    // Add computed selections for ordering to ensure aliases exist in pagination/count subqueries
    const statusOrderExpr = `array_position(ARRAY[${Object.values(
      MachineStatusPriority,
    )
      .map((v) => `'${v}'`)
      .join(',')}], wm.status)`;

    // Use select aliases for ORDER BY so getManyAndCount doesn't reference table aliases in count subquery
    qb.addSelect(statusOrderExpr, 'status_order');
    qb.addSelect('ss_latest.estimatedEndTime', 'estimated_end_time');

    qb.distinctOn(['status_order', 'estimated_end_time', 'wm.id'])
      .orderBy('status_order', 'ASC')
      .addOrderBy('estimated_end_time', 'ASC', 'NULLS FIRST');
    if (currentUserId) {
      qb.leftJoin(
        ServiceSession,
        'ss_latest_by_user',
        'ss_latest_by_user.machineId = wm.id AND ss_latest_by_user.userId = :currentUserId AND ss_latest_by_user.status IN (:...latestStatuses)',
        {
          currentUserId,
          latestStatuses: [ServiceSessionStatus.DONE],
        },
      )
        .addSelect('ss_latest_by_user.createdAt', 'latest_by_user_created_at')
        .distinctOn([
          'status_order',
          'estimated_end_time',
          'latest_by_user_created_at',
          'wm.id',
        ])
        .addOrderBy('latest_by_user_created_at', 'DESC', 'NULLS LAST');
    }
    qb.addOrderBy('wm.id', 'ASC')
      .addOrderBy('wm.createdAt', 'DESC')
      .skip(skip)
      .take(limit);
    const [items, count] = await Promise.all([
      qb.getMany(),
      countQb.select('COUNT(DISTINCT wm.id) AS count').getRawOne(),
    ]);

    return {
      items,
      count: parseInt(count.count ?? '0', 10),
    };
  }

  async update(
    idOrCode: string,
    payload: WashingMachineUpdateDto,
  ): Promise<WashingMachine> {
    // Find the washing machine
    const washingMachine = await this.findByIdOrCode(idOrCode);
    if (!washingMachine) {
      throw new NotFoundException({
        code: 'WASHING_MACHINE_NOT_FOUND',
        message: 'Washing machine not found',
      });
    }

    // Update basic information
    const updateData: Partial<WashingMachine> = {};

    // Check for duplicate code if changed
    if (payload.code && payload.code !== washingMachine.code) {
      const codeExists = await this.washingMachineRepository.existsBy({
        code: payload.code,
        id: Not(washingMachine.id),
      });
      if (codeExists) {
        throw new ConflictException({
          code: 'MACHINE_CODE_EXISTS',
          message: 'Washing machine code already exists.',
        });
      }
    }

    // Check if machine is in use and prevent update
    if (
      (await this.isMachineHasPendingOrder(washingMachine.id)) ||
      washingMachine.status === MachineStatus.IN_USE
    ) {
      throw new ConflictException({
        code: 'MACHINE_IN_USE',
        message: 'Cannot update washing machine while it is in use.',
      });
    }

    // Update fields that have values (exclude machinePrograms)
    const updateFields = omit(payload, 'machinePrograms');
    const validUpdateFields = pickBy(
      updateFields,
      (value) => value !== undefined && value !== null,
    );
    Object.assign(updateData, validUpdateFields);

    // Update features if basic info changed
    const basicInfoFields = ['name', 'model', 'capacityKg'];
    const hasBasicInfoChanges = some(
      basicInfoFields,
      (field) => payload[field] !== undefined,
    );
    if (hasBasicInfoChanges) {
      updateData.features = {
        name: payload.name ?? washingMachine.name,
        model: payload.model ?? washingMachine.model,
        capacity: payload.capacityKg ?? washingMachine.capacityKg,
      };
    }

    // Update machine programs if provided
    if (payload.machinePrograms && payload.machinePrograms.length > 0) {
      // Get machine programs for this washing machine to rebuild options
      const machinePrograms = await this.machineProgramRepository.find({
        where: { machineId: washingMachine.id },
        relations: ['product'],
      });

      for (const programUpdate of payload.machinePrograms) {
        // Find existing machine program from machinePrograms
        const existingProgram = machinePrograms.find(
          (program) => program.id === programUpdate.id,
        );

        if (!existingProgram) {
          continue;
        }

        // Update associated product price
        await this.productRepository.update(existingProgram.productId, {
          pricePoints: programUpdate.price,
        });
      }

      const machineProgramsToDelete = machinePrograms.filter(
        (program) =>
          !payload.machinePrograms.some(
            (programUpdate) => programUpdate.id === program.id,
          ),
      );

      if (machineProgramsToDelete.length > 0) {
        await this.machineProgramRepository.softDelete({
          id: In(map(machineProgramsToDelete, 'id')),
        });

        await this.productRepository.softDelete({
          id: In(map(machineProgramsToDelete, 'productId')),
        });
      }

      // Create new machine programs and products
      await this.createMachineProgramAndProduct(
        washingMachine,
        payload.machinePrograms,
      );

      // Fetch updated machine programs with fresh product data
      const updatedMachinePrograms = await this.machineProgramRepository.find({
        where: { machineId: washingMachine.id },
        relations: ['product'],
      });

      // Rebuild options array with updated prices (like when creating)
      const updatedOptions = updatedMachinePrograms.map((program) => ({
        name: program.name,
        code: program.code,
        price: program.product.pricePoints, // Use updated price from Product
        settings: program.settings,
        features: program.features,
        displayOrder: program.displayOrder,
      }));

      // Add options to updateData for single update
      updateData.options = updatedOptions;
    }

    if (isEmpty(updateData)) {
      return washingMachine;
    }

    // Update the washing machine once with all changes
    const result = await this.washingMachineRepository.update(
      washingMachine.id,
      updateData,
    );
    if (result.affected > 0 && updateData.status) {
      await this.serviceSessionService.markAsDoneByMachines([
        washingMachine.id,
      ]);
    }

    // Return updated washing machine
    return {
      ...washingMachine,
      ...(updateData as Partial<WashingMachine>),
    } as WashingMachine;
  }

  /**
   * Delete washing machine (soft delete)
   * @param idOrCode - Washing machine ID or code
   * @returns {Promise<boolean>}
   */
  async delete(idOrCode: string): Promise<boolean> {
    // Find the washing machine
    const washingMachine = await this.findByIdOrCode(idOrCode);
    if (!washingMachine) {
      throw new NotFoundException({
        code: 'WASHING_MACHINE_NOT_FOUND',
        message: 'Washing machine not found',
      });
    }

    // Check if machine is currently in use
    const hasActiveSessions = await this.serviceSessionRepository.existsBy({
      machineId: washingMachine.id,
      status: In([ServiceSessionStatus.WAITING, ServiceSessionStatus.IN_USE]),
    });

    if (washingMachine.status === MachineStatus.IN_USE || hasActiveSessions) {
      throw new ConflictException({
        code: 'MACHINE_IN_USE',
        message: 'Cannot delete washing machine that is currently in use',
      });
    }

    const hasOrder = await this.machineProgramRepository
      .createQueryBuilder('mp')
      .withDeleted()
      .innerJoin(OrderItem, 'oi', 'oi.productId = mp.productId')
      .where('mp.machineId = :machineId', { machineId: washingMachine.id })
      .getExists();

    if ((await this.isMachineHasPendingOrder(washingMachine.id)) || hasOrder) {
      throw new ConflictException({
        code: 'MACHINE_HAS_ORDER',
        message: 'Cannot delete washing machine that has order',
      });
    }

    // Soft delete the washing machine
    await this.washingMachineRepository.softDelete(washingMachine.id);

    // Fire event to handle related data deletion
    this.eventEmitter.emit(
      WASHING_MACHINE_EVENTS.DELETED,
      new WashingMachineDeletedEvent(
        washingMachine.id,
        washingMachine.code,
        washingMachine.storeId,
      ),
    );

    return true;
  }

  async bulkStatus(payload: {
    machineIds: string[];
    status: MachineStatus;
  }): Promise<boolean> {
    const { machineIds, status } = payload;

    if (machineIds.length === 0) {
      throw new ConflictException({
        code: 'EMPTY_MACHINE_LIST',
        message: 'Washing machine list cannot be empty.',
      });
    }

    const machines = await this.washingMachineRepository.find({
      where: { id: In(machineIds) },
    });

    if (machines.length !== machineIds.length) {
      throw new NotFoundException({
        code: 'WASHING_MACHINE_NOT_FOUND',
        message: 'Some washing machines not found',
      });
    }

    const latestSessions =
      await this.serviceSessionService.getLatestServiceSessionsByMachineIds(
        machines.map((machine) => machine.id),
      );

    const latestSessionsMap = new Map(
      latestSessions.map((session) => [session.machineId, session]),
    );

    const hasInvalidMachines = machines.some((machine) => {
      const latestSession = latestSessionsMap.get(machine.id);
      const hasActiveSessions =
        latestSession &&
        (latestSession.status === ServiceSessionStatus.WAITING ||
          latestSession.status === ServiceSessionStatus.IN_USE);

      return machine.status === MachineStatus.IN_USE || hasActiveSessions;
    });

    if (hasInvalidMachines) {
      throw new ConflictException({
        code: 'MACHINE_IN_USE',
        message: 'Some washing machines are in use, cannot update status',
      });
    }

    const updateData: Partial<WashingMachine> = {
      status,
    };

    if (status === MachineStatus.MAINTENANCE) {
      updateData.lastMaintenanceAt = new Date();
    }

    const result = await this.washingMachineRepository.updateBy(
      { id: In(machineIds) },
      updateData,
    );

    if (result.affected > 0) {
      await this.serviceSessionService.markAsDoneByMachines(machineIds);
    }

    return true;
  }

  async createMachineProgramAndProduct(
    washingMachine: WashingMachine,
    payload: (MachineProgramCreateDto | MachineProgramUpdateDto)[],
  ) {
    const machineProgramsToCreate: MachineProgram[] = [];
    const productsToCreate: Product[] = [];

    payload.map((program, index) => {
      if ((program as MachineProgramUpdateDto)?.id) {
        return null;
      }

      const productId = v4();
      productsToCreate.push(
        this.productRepository.create({
          id: productId,
          name: program.name,
          slug: productId,
          type: ProductType.SERVICE,
          category: ProductCategory.LAUNDRY_SERVICE,
          pricePoints: program.price,
          status: ProductStatus.ACTIVE,
          storeId: washingMachine.storeId,
        }),
      );
      machineProgramsToCreate.push(
        this.machineProgramRepository.create({
          machineId: washingMachine.id,
          productId,
          name: program.name,
          code: program.code,
          durationMinutes: program.settings.duration,
          settings: program.settings,
          features: program.features ?? {},
          isActive: true,
          displayOrder: program.displayOrder ?? index + 1,
        }),
      );
    });

    await this.productRepository.save(productsToCreate);

    await this.machineProgramRepository.save(machineProgramsToCreate);
  }

  async isMachineHasPendingOrder(machineId: string): Promise<boolean> {
    return await this.orderItemRepository
      .createQueryBuilder('oi')
      .innerJoin(Order, 'o', 'o.id = oi.orderId AND o.status = :status', {
        status: OrderStatus.PENDING,
      })
      .innerJoin(
        MachineProgram,
        'mp',
        'mp.productId = oi.productId AND mp.machineId = :machineId',
        { machineId },
      )
      .getExists();
  }
}
