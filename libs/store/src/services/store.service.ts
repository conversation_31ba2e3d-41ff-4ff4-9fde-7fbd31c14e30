import { Injectable, Inject } from '@nestjs/common';
import { Store } from '@app/shared/database/entities/store.entity';
import { StoreRepositoryInterface } from '@app/shared/database/repositories';
import { v4 as uuidv4 } from 'uuid';
import { StoreStatus } from '@app/shared/database/entities/store.entity';

@Injectable()
export class StoreService {
  constructor(
    @Inject('StoreRepositoryInterface')
    private readonly storeRepository: StoreRepositoryInterface,
  ) {}

  async getOrCreateAnyStore(storeId?: string): Promise<Store> {
    if (storeId) {
      const store = await this.storeRepository.findOneById(storeId);
      if (store) return store;
    }
    // Get first store
    const stores = await this.storeRepository.find({ take: 1 });
    if (stores.length > 0) return stores[0];
    // If no store, create sample store
    const sampleStore = this.storeRepository.create({
      name: 'Sample Store',
      code: `SAMPLE-${uuidv4().slice(0, 8)}`,
      address: '123 Sample St, City',
      phone: '0123456789',
      manager_name: 'Sample Manager',
      operating_hours: {
        monday: '06:00-22:00',
        tuesday: '06:00-22:00',
        wednesday: '06:00-22:00',
        thursday: '06:00-22:00',
        friday: '06:00-22:00',
        saturday: '07:00-23:00',
        sunday: '08:00-20:00',
      },
      facilities: ['free_wifi', 'parking'],
      status: StoreStatus.ACTIVE,
      latitude: 21.0285,
      longitude: 105.8542,
    });
    return await this.storeRepository.save(sampleStore);
  }
}
