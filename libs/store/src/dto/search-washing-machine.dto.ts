import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  <PERSON>,
} from 'class-validator';
import { MachineStatus } from '@app/shared/database/entities';

export class SearchWashingMachinesQueryDto {
  @ApiPropertyOptional({ description: 'Store ID to filter', format: 'uuid' })
  @IsUUID()
  @IsOptional()
  storeId?: string;

  @ApiPropertyOptional({
    description: 'Filter by machine statuses.',
    enum: MachineStatus,
    isArray: true,
  })
  @Expose({
    name: 'status[]',
  })
  @IsEnum(MachineStatus, { each: true })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (Array.isArray(value)) return value;
    return [value];
  })
  status?: MachineStatus[];

  @ApiPropertyOptional({ description: 'Keyword to search by machine name' })
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiPropertyOptional({
    description:
      'If true, only return machines currently in use by the current user',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') return value === 'true';
    return false;
  })
  isUsingByMe?: boolean;

  @ApiPropertyOptional({ description: 'Number of records to skip', default: 0 })
  @IsInt()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value ?? 0, 10))
  skip?: number = 0;

  @ApiPropertyOptional({ description: 'Page size', default: 20, maximum: 100 })
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value ?? 20, 10))
  limit?: number = 20;
}
