import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  Length,
  Matches,
  IsUUID,
  IsNumber,
  Min,
  ValidateNested,
  IsArray,
  IsOptional,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MachineType, MachineStatus } from '@app/shared/database/entities';
import { MachineProgramUpdateDto } from './machine-program-update.dto';

export class WashingMachineUpdateDto {
  @ApiProperty({
    example: 'A01',
    description: 'Machine code',
    required: false,
  })
  @IsString()
  @Length(1, 20)
  @Matches(/^[A-Za-z0-9]+$/)
  @IsOptional()
  code?: string;

  @ApiProperty({
    example: 'Washing machine Samsung',
    description: 'Machine name',
    required: false,
  })
  @IsString()
  @Length(1, 255)
  @IsOptional()
  name?: string;

  @ApiProperty({
    example: 'store-uuid',
    description: 'Store ID',
    required: false,
  })
  @IsString()
  @IsUUID()
  @IsOptional()
  storeId?: string;

  @ApiProperty({
    example: 'WD172CS',
    description: 'Machine model',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  model?: string;

  @ApiProperty({
    example: 'combo',
    description: 'Machine type',
    required: false,
  })
  @IsString()
  @IsOptional()
  type?: MachineType;

  @ApiProperty({
    example: 11,
    description: 'Capacity (kg)',
    required: false,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  capacityKg?: number;

  @ApiProperty({
    enum: MachineStatus,
    example: MachineStatus.AVAILABLE,
    description: 'Machine status (available, maintenance, out_of_service)',
    required: false,
  })
  @IsEnum(MachineStatus)
  @IsOptional()
  status?: MachineStatus;

  @ApiProperty({
    type: [MachineProgramUpdateDto],
    description: 'List of machine programs to update',
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MachineProgramUpdateDto)
  @IsOptional()
  machinePrograms?: MachineProgramUpdateDto[];
}
