import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';

export class MachineProgramSettingsDto {
  @ApiProperty({
    example: 40,
    description: 'Temperature (°C)',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  temperature?: number;

  @ApiProperty({
    example: 1000,
    description: 'Spin speed (rpm)',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  spinSpeed?: number;

  @ApiProperty({
    example: 'medium',
    description: 'Water level (low, medium, high, auto)',
    required: false,
  })
  @IsString()
  @IsOptional()
  waterLevel?: string;

  @ApiProperty({
    example: 'normal',
    description: 'Wash intensity (gentle, normal, heavy)',
    required: false,
  })
  @IsString()
  @IsOptional()
  washIntensity?: string;

  @ApiProperty({ example: false, description: 'Pre-wash', required: false })
  @IsOptional()
  preWash?: boolean;

  @ApiProperty({ example: 1, description: 'Extra rinse', required: false })
  @IsNumber()
  @IsOptional()
  extraRinse?: number;

  @ApiProperty({ example: 11, description: 'Weight (kg)', required: false })
  @IsNumber()
  @IsOptional()
  weight?: number;

  @ApiProperty({
    example: 60,
    description: 'Duration (minutes)',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  duration?: number;
}
