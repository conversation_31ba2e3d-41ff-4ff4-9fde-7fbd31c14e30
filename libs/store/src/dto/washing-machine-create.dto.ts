import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  Length,
  Matches,
  IsUUID,
  IsIn,
  IsNumber,
  Min,
  ValidateNested,
  ArrayMinSize,
  IsArray,
  IsOptional,
  isNotEmpty,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MachineProgramCreateDto } from './machine-program-create.dto';
import { MachineType } from '@app/shared/database/entities';

export class WashingMachineCreateDto {
  @ApiProperty({ example: 'A01', description: 'Machine code' })
  @IsString()
  @IsNotEmpty()
  @Length(1, 20)
  @Matches(/^[A-Za-z0-9]+$/)
  code: string;

  @ApiProperty({
    example: 'Washing machine Samsung',
    description: 'Machine name',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  @ValidateIf((object, value) => isNotEmpty(value))
  @ApiProperty({ example: 'store-uuid', description: 'Store ID' })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  storeId?: string;

  @ApiProperty({
    example: 'WD172CS',
    description: 'Machine model',
  })
  @IsString()
  @IsNotEmpty()
  model: string;

  @ApiProperty({
    example: 'combo',
    description: 'Machine type',
  })
  @IsString()
  @IsOptional()
  type?: MachineType;

  @ApiProperty({ example: 11, description: 'Capacity (kg)' })
  @IsNumber()
  @Min(1)
  capacityKg: number;

  @ApiProperty({
    type: [MachineProgramCreateDto],
    description: 'List of washing programs and prices',
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => MachineProgramCreateDto)
  machinePrograms: MachineProgramCreateDto[];
}
