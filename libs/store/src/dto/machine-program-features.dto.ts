import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class MachineProgramFeaturesDto {
  @ApiProperty({
    example: false,
    description: 'Steam refresh',
    required: false,
  })
  @IsOptional()
  steamRefresh?: boolean;

  @ApiProperty({
    example: false,
    description: 'Stain removal',
    required: false,
  })
  @IsOptional()
  stainRemoval?: boolean;

  @ApiProperty({
    example: true,
    description: 'Fabric softener',
    required: false,
  })
  @IsOptional()
  fabricSoftener?: boolean;

  @ApiProperty({ example: false, description: 'Anti-wrinkle', required: false })
  @IsOptional()
  antiWrinkle?: boolean;

  @ApiProperty({
    example: false,
    description: 'Eco mode',
    required: false,
  })
  @IsOptional()
  ecoMode?: boolean;

  @ApiProperty({ example: false, description: 'Sanitize', required: false })
  @IsOptional()
  sanitize?: boolean;
}
