import { ApiProperty } from '@nestjs/swagger';
import { ProductDto } from './product.dto';
import { Expose } from 'class-transformer';

export class MachineProgramDto {
  @ApiProperty({ example: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  machineId: string;

  @ApiProperty({ example: 'Standard' })
  @Expose()
  name: string;

  @ApiProperty({ example: 'STANDARD' })
  @Expose()
  code: string;

  @ApiProperty({ example: 60 })
  @Expose()
  durationMinutes: number;

  @ApiProperty({ type: () => Object, nullable: true })
  @Expose()
  settings: Record<string, any> | null;

  @ApiProperty({ type: () => Object, nullable: true })
  @Expose()
  features: Record<string, any> | null;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  productId: string;

  @ApiProperty({ type: () => ProductDto, nullable: true })
  @Expose()
  product: ProductDto | null;

  @ApiProperty({ example: 1, nullable: true })
  @Expose()
  displayOrder: number | null;
}
