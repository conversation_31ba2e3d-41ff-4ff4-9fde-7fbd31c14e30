import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  Min,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MachineProgramSettingsDto } from './machine-program-settings.dto';
import { MachineProgramFeaturesDto } from './machine-program-features.dto';

export class MachineProgramCreateDto {
  @ApiProperty({
    example: 'Wash and dry standard',
    description: 'Program name',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'WASH_AND_DRY_STANDARD',
    description: 'Program code',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({ example: 10000, description: 'Price' })
  @IsNumber()
  @Min(1)
  price: number;

  @ApiProperty({
    type: MachineProgramSettingsDto,
    description: 'Technical settings of the program',
    required: false,
  })
  @ValidateNested()
  @Type(() => MachineProgramSettingsDto)
  @IsOptional()
  settings?: MachineProgramSettingsDto;

  @ApiProperty({
    type: MachineProgramFeaturesDto,
    description: 'Special features of the program',
    required: false,
  })
  @ValidateNested()
  @Type(() => MachineProgramFeaturesDto)
  @IsOptional()
  features?: MachineProgramFeaturesDto;

  @ApiProperty({
    type: Number,
    description: 'Display order',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  displayOrder?: number;
}
