import { ApiProperty } from '@nestjs/swagger';
import { StoreStatus } from '@app/shared/database/entities/store.entity';
import { Expose } from 'class-transformer';

export class StoreDto {
  @ApiProperty({ example: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ example: 'STORE-01' })
  @Expose()
  code: string;

  @ApiProperty({ example: 'Sample Store' })
  @Expose()
  name: string;

  @ApiProperty({ example: '123 Sample St' })
  @Expose()
  address: string;

  @ApiProperty({ example: '0123456789' })
  @Expose()
  phone: string;

  @ApiProperty({ enum: StoreStatus, example: StoreStatus.ACTIVE })
  @Expose()
  status: StoreStatus;

  @ApiProperty({ example: 21.0285, nullable: true })
  @Expose()
  latitude: number | null;

  @ApiProperty({ example: 105.8542, nullable: true })
  @Expose()
  longitude: number | null;
}
