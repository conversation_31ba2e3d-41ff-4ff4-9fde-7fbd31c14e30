import { ServiceSessionStatus } from '@app/shared/database/entities';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class ServiceSessionDto {
  @ApiProperty({ example: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  machineId: string;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  machineProgramId: string;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  userId: string;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  orderId: string;

  @ApiProperty({ example: 'uuid' })
  @Expose()
  orderItemId: string;

  @ApiProperty({
    enum: ServiceSessionStatus,
    example: ServiceSessionStatus.IN_USE,
  })
  @Expose()
  status: ServiceSessionStatus;

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  @Expose()
  startedAt: Date | null;

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  @Expose()
  estimatedEndTime: Date | null;

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  @Expose()
  completedAt: Date | null;

  @ApiProperty({ type: String, format: 'date-time' })
  @Expose()
  createdAt: Date;
}
