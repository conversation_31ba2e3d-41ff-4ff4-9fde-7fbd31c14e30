import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { S3Service } from './services/s3.service';
import { SESService } from './services/ses.service';
import { ChimeService } from './services/chime.service';

@Module({
  imports: [ConfigModule],
  providers: [S3Service, SESService, ChimeService],
  exports: [S3Service, SESService, ChimeService],
})
export class AwsModule {}
