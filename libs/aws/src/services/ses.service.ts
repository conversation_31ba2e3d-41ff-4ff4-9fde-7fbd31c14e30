import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SendEmailCommand, SESClient } from '@aws-sdk/client-ses';

@Injectable()
export class SESService {
  private readonly logger = new Logger(SESService.name);
  private readonly ses: SESClient;
  private readonly noReplyEmail: string;

  constructor(private readonly configService: ConfigService) {
    const accessKeyId = this.configService.get('AWS.ACCESS_KEY');
    const secretAccessKey = this.configService.get('AWS.SECRET_ACCESS_KEY');
    this.ses = new SESClient({
      region: configService.get('AWS.DEFAULT_REGION'),
      ...(accessKeyId
        ? {
            credentials: {
              accessKeyId,
              secretAccessKey,
            },
          }
        : {}),
    });
    this.noReplyEmail = configService.get('MAIL_SENDER');
  }

  /**
   * Send email
   *
   * @param receiverEmail
   * @param subject
   * @param body
   * @param senderEmail
   * @returns Promise<boolean>
   */
  async sendEmail(
    receiverEmail: string,
    subject: string,
    body: string,
    senderEmail?: string,
  ): Promise<boolean> {
    senderEmail = senderEmail || this.noReplyEmail;

    const params = {
      Source: senderEmail,
      Destination: {
        ToAddresses: [receiverEmail],
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'utf-8',
        },
        Body: {
          Html: {
            Data: body,
            Charset: 'utf-8',
          },
        },
      },
    };
    try {
      this.logger.log(
        `[Send mail] Receiver email: ${receiverEmail}` +
          JSON.stringify({
            subject,
            senderEmail,
          }),
      );
      const command = new SendEmailCommand(params);
      await this.ses.send(command);

      return true;
    } catch (error) {
      this.logger.error('[Send mail] Error:', error);
      return false;
    }
  }
}
