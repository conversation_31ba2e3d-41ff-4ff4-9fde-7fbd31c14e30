import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  DeleteObjectsCommand,
  GetObjectCommand,
  NoSuchKey,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import { v4 } from 'uuid';
import { last, slice } from 'lodash';
import moment from 'moment';
import sharp from 'sharp';
import { Readable } from 'stream';
import { fileTypeFromBuffer } from 'file-type';
import { CHUNK_SIZE } from '@app/shared/constants';
import { ErrorResponse } from '@app/shared/types';

export interface S3Image {
  url: string;
  key: string;
}

@Injectable()
export class S3Service {
  private _s3Client: S3Client;

  constructor(private readonly configService: ConfigService) {
    const accessKeyId = this.configService.get('AWS.ACCESS_KEY');
    const secretAccessKey = this.configService.get('AWS.SECRET_ACCESS_KEY');
    this._s3Client = new S3Client({
      region: this.configService.get('AWS.DEFAULT_REGION'),
      useAccelerateEndpoint: this.configService.get(
        'AWS.USE_ACCELERATE_ENDPOINT',
      ),
      ...(accessKeyId
        ? {
            credentials: {
              accessKeyId,
              secretAccessKey,
            },
          }
        : {}),
    });
  }

  async getTempUrl(
    key: string,
    expiresInSeconds?: number,
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: this.processPathnameToS3Key(key),
      });
      return getSignedUrl(this.client, command, {
        expiresIn:
          expiresInSeconds ??
          this.configService.get('AWS.PRESIGNED_URL_EXPIRE_MINUTES') * 60,
      });
    } catch (error) {
      throw error;
    }
  }

  get client(): S3Client {
    return this._s3Client;
  }

  async saveImage(
    file: Express.Multer.File,
    uriPath: string,
    fileName?: string,
  ): Promise<S3Image> {
    if (!file) {
      throw new NotFoundException({ code: 'FILE_NOT_FOUND' } as ErrorResponse);
    }
    if (!fileName) {
      fileName = this.makeFileName(file);
    }
    const s3FilePath =
      `${uriPath}/` + moment().format('YYYYMMDD') + '/' + v4() + '_' + fileName;
    const imgBuf = file.buffer;
    const imgType = file.mimetype;

    try {
      const s3File = await new Upload({
        client: this.client,
        params: {
          Bucket: this.configService.get('AWS.BUCKET_GALLERY'),
          Key: s3FilePath,
          Body: imgBuf,
          ContentType: imgType,
        },
      }).done();

      return {
        key: s3FilePath,
        url: s3File.Location,
      };
    } catch (error) {
      throw error;
    }
  }

  async getPresignedUrl(key: string): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.configService.get('AWS.BUCKET_GALLERY'),
        Key: this.processPathnameToS3Key(key),
      });
      return getSignedUrl(this.client, command, {
        expiresIn:
          this.configService.get('AWS.PRESIGNED_URL_EXPIRE_MINUTES') * 60,
      });
    } catch (error) {
      throw error;
    }
  }

  async getContentStreamObject(
    key: string,
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ): Promise<any> {
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: this.processPathnameToS3Key(key),
    });
    try {
      const response = await this._s3Client.send(command);

      return response.Body;
    } catch (error) {
      if (error?.name === NoSuchKey.name) {
        return null;
      }
      throw error;
    }
  }

  async getImageDimensions(
    key: string,
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ): Promise<{ width: number; height: number }> {
    const metadata = await this.getMetadata(key, bucket);

    return {
      width: metadata.width,
      height: metadata.height,
    };
  }

  async getMetadata(
    key: string,
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ): Promise<any> {
    const fileStream = await this.getContentStreamObject(key, bucket);
    if (!fileStream) {
      return fileStream;
    }
    const writableStream = sharp();
    fileStream.pipe(writableStream);
    return await writableStream.metadata();
  }

  async processFile(
    key: string,
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ) {
    const fileStream = await this.getContentStreamObject(key, bucket);

    const buffer: Buffer = await this.streamToBuffer(fileStream);
    const fileType = await fileTypeFromBuffer(buffer);

    return {
      fileBuffer: await this.streamToBuffer(Readable.from(buffer)),
      fileType,
    };
  }

  async deleteObject(
    key: string,
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ) {
    await this.deleteObjects([key], bucket);
  }

  async deleteObjects(
    keys: string[],
    bucket = this.configService.get('AWS.BUCKET_GALLERY'),
  ) {
    try {
      let skip = 0;
      const limit = CHUNK_SIZE;
      for (;;) {
        const keysChunk = slice(keys, skip, skip + limit);
        if (!keysChunk.length) {
          break;
        }
        const command = new DeleteObjectsCommand({
          Bucket: bucket,
          Delete: {
            Objects: keysChunk.map((key) => ({
              Key: this.processPathnameToS3Key(key),
            })),
          },
        });
        await this._s3Client.send(command);
        skip += limit;
      }
    } catch (error) {
      throw error;
    }
  }

  makeFileName(file: Express.Multer.File): string {
    const fileExtension = file.mimetype;
    return (
      (Math.random() + 1).toString(36).substring(12) +
      new Date().getTime() +
      '.' +
      (fileExtension ? last(fileExtension.split('/')) : 'png')
    );
  }

  private async streamToBuffer(stream: Readable): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: any[] = [];
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
      stream.on('error', reject);
    });
  }

  private processPathnameToS3Key(pathname: string): string {
    return pathname.replace(/^\//, '');
  }
}
