import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ChimeSDKMeetingsClient,
  CreateAttendeeCommand,
  CreateMeetingCommand,
  DeleteMeetingCommand,
  GetMeetingCommand,
} from '@aws-sdk/client-chime-sdk-meetings';
import { v4 } from 'uuid';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class ChimeService {
  private readonly logger: Logger = new Logger(ChimeService.name);
  private readonly chimeClient: ChimeSDKMeetingsClient;
  private readonly region: string;

  constructor(private readonly configService: ConfigService) {
    const accessKeyId = this.configService.get('AWS.ACCESS_KEY');
    const secretAccessKey = this.configService.get('AWS.SECRET_ACCESS_KEY');
    this.region = configService.get('AWS_DEFAULT_REGION');
    this.chimeClient = new ChimeSDKMeetingsClient({
      region: this.region,
      ...(accessKeyId
        ? {
            credentials: {
              accessKeyId,
              secretAccessKey,
            },
          }
        : {}),
    });
  }

  /**
   * Join a meeting.
   *
   * @param externalMeetingId Conversation ID
   * @param externalUserId User ID
   * @param meetingId Conversation code
   */
  async joinMeeting(
    externalMeetingId: string,
    externalUserId: string,
    meetingId?: string,
  ) {
    let meeting;
    if (meetingId) {
      meeting = await this.getMeeting(meetingId);
    }

    if (meeting) {
      meetingId = meeting.Meeting.MeetingId;
    } else {
      meeting = await this.createMeeting(v4(), externalMeetingId);
      meetingId = meeting.Meeting.MeetingId;
    }

    const attendee = await this.createAttendee(meetingId, externalUserId);

    return {
      meeting: meeting.Meeting,
      attendee: attendee.Attendee,
    };
  }

  /**
   * Find a meeting by meeting ID.
   *
   * @param meetingId Conversation code
   */
  async getMeeting(meetingId: string) {
    try {
      const command: GetMeetingCommand = new GetMeetingCommand({
        MeetingId: meetingId,
      });
      return await this.chimeClient.send(command);
    } catch (error) {
      this.logger.error(`[Find meeting] Error:`, error.message);
      return null;
    }
  }

  /**
   * Delete a meeting.
   *
   * @param meetingId Conversation code
   */
  async deleteMeeting(meetingId: string): Promise<boolean> {
    try {
      const command: DeleteMeetingCommand = new DeleteMeetingCommand({
        MeetingId: meetingId,
      });

      await this.chimeClient.send(command);
      return true;
    } catch (error) {
      this.logger.error(`[Delete meeting] Error:`, error.message);
      return false;
    }
  }

  /**
   * Create a meeting.
   *
   * @param clientRequestId The unique identifier for the client request
   * @param externalMeetingId Conversation ID
   */
  private async createMeeting(
    clientRequestId: string,
    externalMeetingId: string,
  ) {
    try {
      const command: CreateMeetingCommand = new CreateMeetingCommand({
        ClientRequestToken: clientRequestId,
        MediaRegion: this.region,
        ExternalMeetingId: externalMeetingId,
      });

      return await this.chimeClient.send(command);
    } catch (error) {
      this.logger.error(`[Create meeting] Error:`, error.message);
      throw new InternalServerErrorException({
        code: 'CHIME_MEETING_CREATION_FAILED',
      } as ErrorResponse);
    }
  }

  /**
   * Create an attendee.
   *
   * @param meetingId Conversation code
   * @param externalUserId User ID
   */
  private async createAttendee(meetingId: string, externalUserId: string) {
    try {
      const command: CreateAttendeeCommand = new CreateAttendeeCommand({
        MeetingId: meetingId,
        ExternalUserId: externalUserId,
      });

      return await this.chimeClient.send(command);
    } catch (error) {
      this.logger.error(`[Create attendee] Error:`, error.message);
      throw new InternalServerErrorException({
        code: 'CHIME_MEETING_CREATION_FAILED',
      } as ErrorResponse);
    }
  }
}
