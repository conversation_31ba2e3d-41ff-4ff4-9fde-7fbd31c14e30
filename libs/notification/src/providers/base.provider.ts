import {
  NotificationProviderType,
  NotificationMessage,
  NotificationSendResult,
} from '../types';

export abstract class BaseNotificationProvider {
  abstract readonly name: NotificationProviderType;

  abstract isValidRecipient(recipient: string): boolean;

  abstract send(
    to: string,
    title: string,
    body: string,
    data?: Record<string, unknown>,
    image?: string,
  ): Promise<NotificationSendResult>;

  abstract sendMany(
    tokens: string[],
    title: string,
    body: string,
    data?: Record<string, unknown>,
    image?: string,
  ): Promise<NotificationSendResult>;

  abstract sendMessages(
    messages: NotificationMessage[],
  ): Promise<NotificationSendResult>;
}

export type { NotificationMessage, NotificationSendResult };
