import { Injectable, Logger } from '@nestjs/common';
import { BaseNotificationProvider } from './base.provider';
import { NotificationMessage, NotificationSendResult } from './base.provider';
import {
  Expo,
  ExpoPushMessage,
  ExpoPushTicket,
  ExpoPushErrorTicket,
} from 'expo-server-sdk';
import { ExpoService } from '../services/expo.service';

@Injectable()
export class ExpoNotificationProvider extends BaseNotificationProvider {
  // Provider for Expo push notifications.
  // Uses singleton ExpoService to build/send messages.
  // Logs ticket result once here (no duplicate logging in service).
  readonly name = 'expo' as const;
  private readonly logger = new Logger(ExpoNotificationProvider.name);
  constructor(private readonly expoService: ExpoService) {
    super();
  }

  /**
   * Validate Expo token format
   * @param recipient - The token to validate.
   * @returns True if the token is valid, false otherwise.
   */
  isValidRecipient(recipient: string): boolean {
    return Expo.isExpoPushToken(recipient);
  }

  /**
   * Send a single push
   * @param to - The token to send the notification to.
   * @param title - The title of the notification.
   * @param body - The body of the notification.
   * @param data - The data of the notification.
   * @param image - The image of the notification.
   * @returns The result of the notification send.
   */
  async send(
    to: string,
    title: string,
    body: string,
    data?: Record<string, unknown>,
    image?: string,
  ): Promise<NotificationSendResult> {
    // Build message once and delegate sending to service
    const expoMessage = this.expoService.buildMessage(
      to,
      title,
      body,
      data as Record<string, any> | undefined,
      image,
    );
    const tickets = await this.expoService.sendMessages([expoMessage]);
    const ticket = (tickets?.[0] as ExpoPushTicket) || null;
    // Log result for the only ticket if returned
    if (ticket) {
      if (ticket.status === 'ok') {
        this.logger.log(
          `[${this.name}] OK to=${to} title="${title}" ticketId=${ticket.id}`,
        );
      } else {
        this.logger.error(
          `[${this.name}] ERROR to=${to} title="${title}" message=${ticket.message} details=${JSON.stringify(
            ticket.details,
          )}`,
        );
      }
    } else {
      this.logger.error(`[${this.name}] No ticket returned for to=${to}`);
    }
    return { success: tickets.length > 0, tickets };
  }

  /**
   * Send the same content to multiple tokens
   * @param tokens - The tokens to send the notification to.
   * @param title - The title of the notification.
   * @param body - The body of the notification.
   * @param data - The data of the notification.
   * @param image - The image of the notification.
   * @returns The result of the notification send.
   */
  async sendMany(
    tokens: string[],
    title: string,
    body: string,
    data?: Record<string, unknown>,
    image?: string,
  ): Promise<NotificationSendResult> {
    // Delegate sending to service; only log per-ticket here
    const tickets = await this.expoService.sendToTokens(
      tokens,
      title,
      body,
      data as Record<string, any> | undefined,
      image,
    );
    for (let i = 0; i < tokens.length; i++) {
      const message = { to: tokens[i], title, body } as any;
      const ticket = (tickets?.[i] as ExpoPushTicket) || null;
      if (!ticket) {
        this.logger.error(
          `[${this.name}] No ticket returned for to=${message.to} title="${message.title}"`,
        );
        continue;
      }
      if (ticket.status === 'ok') {
        this.logger.log(
          `[${this.name}] OK to=${message.to} title="${message.title}" id=${ticket.id}`,
        );
      } else {
        this.logger.error(
          `[${this.name}] ERROR to=${message.to} title="${message.title}" message=${ticket.message} details=${JSON.stringify(
            ticket.details,
          )}`,
        );
      }
    }
    return { success: tickets.length > 0, tickets };
  }

  /**
   * Send personalized messages
   * @param messages - The messages to send the notification to.
   * @returns The result of the notification send.
   */
  async sendMessages(
    messages: NotificationMessage[],
  ): Promise<NotificationSendResult> {
    // Build messages per recipient and let service handle chunking
    const expoMessages = messages.map((message) =>
      this.expoService.buildMessage(
        message.to,
        message.title,
        message.body,
        message.data as Record<string, any> | undefined,
        message.image,
      ),
    );
    const tickets = await this.expoService.sendMessages(expoMessages);
    // Log each ticket result aligned with input order
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const ticket = (tickets?.[i] as ExpoPushTicket) || null;
      if (!ticket) {
        this.logger.error(
          `[${this.name}] No ticket returned for to=${message.to} title="${message.title}"`,
        );
        continue;
      }
      if (ticket.status === 'ok') {
        this.logger.log(
          `[${this.name}] OK to=${message.to} title="${message.title}" id=${ticket.id}`,
        );
      } else {
        this.logger.error(
          `[${this.name}] ERROR to=${message.to} title="${message.title}" message=${ticket.message} details=${JSON.stringify(
            ticket.details,
          )}`,
        );
      }
    }
    return { success: tickets.length > 0, tickets };
  }
}
