// Identifier of notification providers supported by the system.
// Extend this union when introducing new providers (e.g., 'fcm' | 'apns').
// Example:
// export type NotificationProviderType = 'expo' | 'fcm' | 'apns';
export type NotificationProviderType = 'expo';

// Generic message payload passed to providers.
// Note: the meaning/format of `to` depends on provider (Expo push token for 'expo').
export type NotificationMessage = {
  to: string;
  title: string;
  body: string;
  data?: Record<string, unknown>;
  image?: string;
};

// Normalized sending result across providers.
// `tickets` may contain raw provider responses (e.g., Expo push tickets).
export type NotificationSendResult = {
  success: boolean;
  tickets?: unknown[];
};

// Payload for sending a notification.
export type SendNotificationPayload = {
  title: string;
  body: string;
  data?: Record<string, any>;
  image?: string;
};
