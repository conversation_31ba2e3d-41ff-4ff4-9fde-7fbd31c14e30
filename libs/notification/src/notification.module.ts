import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NotificationService } from './services/notification.service';
import { NotificationProviderFactory } from './factories/notification-provider.factory';
import { ExpoService } from './services/expo.service';
import { ExpoNotificationProvider } from './providers/expo.provider';

@Module({
  imports: [ConfigModule],
  providers: [
    ExpoService,
    ExpoNotificationProvider,
    NotificationProviderFactory,
    NotificationService,
  ],
  exports: [NotificationService],
})
export class NotificationModule {}
