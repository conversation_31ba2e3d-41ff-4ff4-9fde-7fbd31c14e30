import { Injectable, BadRequestException } from '@nestjs/common';
import { BaseNotificationProvider } from '../providers/base.provider';
import { NotificationProviderType } from '../types';
import { DEFAULT_NOTIFICATION_PROVIDER } from '../constants';
import { ExpoNotificationProvider } from '../providers';
import { ErrorResponse } from '@app/shared/types/error.type';

@Injectable()
export class NotificationProviderFactory {
  private readonly providers: Record<
    NotificationProviderType,
    BaseNotificationProvider
  >;

  constructor(private readonly expo: ExpoNotificationProvider) {
    this.providers = {
      expo: this.expo,
    };
  }

  /**
   * Create a notification provider instance based on the given type.
   * @param type - The type of notification provider to create.
   * @returns A notification provider instance.
   */
  create(
    type: NotificationProviderType = DEFAULT_NOTIFICATION_PROVIDER,
  ): BaseNotificationProvider {
    const provider = this.providers[type];
    if (!provider) {
      throw new BadRequestException({ code: 'BAD_REQUEST' } as ErrorResponse);
    }
    return provider;
  }

  /**
   * Get a notification provider instance based on the given type.
   * @param type - The type of notification provider to get.
   * @returns A notification provider instance.
   */
  getProvider(
    type: NotificationProviderType = DEFAULT_NOTIFICATION_PROVIDER,
  ): BaseNotificationProvider {
    return this.create(type);
  }

  /**
   * Check if a notification provider is supported based on the given type.
   * @param type - The type of notification provider to check.
   * @returns True if the provider is supported, false otherwise.
   */
  isProviderSupported(type: string): type is NotificationProviderType {
    return Object.prototype.hasOwnProperty.call(this.providers, type);
  }
}
