import { Injectable, Logger } from '@nestjs/common';
import { NotificationProviderFactory } from '../factories/notification-provider.factory';
import { DEFAULT_NOTIFICATION_PROVIDER } from '../constants';
import { SendNotificationPayload } from '../types';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(private readonly factory: NotificationProviderFactory) {}

  /**
   * Send a notification to a device
   * @param token - The token to send the notification to.
   * @param payload - The payload of the notification.
   * @returns True if the notification is sent successfully, false otherwise.
   */
  async sendToDevice(
    token: string,
    payload: SendNotificationPayload,
  ): Promise<boolean> {
    const provider = this.factory.getProvider(DEFAULT_NOTIFICATION_PROVIDER);
    if (!provider.isValidRecipient(token)) {
      this.logger.warn(`[${provider.name}] Invalid recipient: ${token}`);
      return false;
    }
    const { success } = await provider.send(
      token,
      payload.title,
      payload.body,
      payload.data,
      payload.image,
    );
    return success;
  }

  /**
   * Send a notification to multiple devices
   * @param tokens - The tokens to send the notification to.
   * @param payload - The payload of the notification.
   * @returns True if the notification is sent successfully, false otherwise.
   */
  async sendToMany(
    tokens: string[],
    payload: SendNotificationPayload,
  ): Promise<boolean> {
    const provider = this.factory.getProvider(DEFAULT_NOTIFICATION_PROVIDER);
    const { success } = await provider.sendMany(
      tokens,
      payload.title,
      payload.body,
      payload.data,
      payload.image,
    );
    return success;
  }

  /**
   * Send a personalized notification to multiple devices
   * @param items - The items to send the notification to.
   * @returns True if the notification is sent successfully, false otherwise.
   */
  async sendToManyPersonalized(
    items: (SendNotificationPayload & { to: string })[],
  ): Promise<boolean> {
    const provider = this.factory.getProvider(DEFAULT_NOTIFICATION_PROVIDER);
    const { success } = await provider.sendMessages(items);
    return success;
  }
}
