import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  Expo,
  ExpoPushErrorTicket,
  ExpoPushMessage,
  ExpoPushTicket,
} from 'expo-server-sdk';

@Injectable()
export class ExpoService {
  private readonly logger = new Logger(ExpoService.name);
  private readonly expo: Expo;

  constructor(private readonly configService: ConfigService) {
    const accessToken = this.configService.get<string>('EXPO.ACCESS_TOKEN');

    this.expo = new Expo({
      useFcmV1: true,
      accessToken: accessToken || undefined,
    });
  }

  /**
   * Validate Expo token format
   * @param token - The token to validate.
   * @returns True if the token is valid, false otherwise.
   */
  isValidToken(token: string): boolean {
    return Expo.isExpoPushToken(token);
  }

  /**
   * Build a message for Expo push notifications
   * @param to - The token to send the notification to.
   * @param title - The title of the notification.
   * @param body - The body of the notification.
   * @param data - The data of the notification.
   * @param image - The image of the notification.
   * @returns The message to send the notification to.
   */
  buildMessage(
    to: string,
    title: string,
    body: string,
    data?: Record<string, any>,
    image?: string,
  ): ExpoPushMessage {
    return {
      to,
      sound: 'default',
      title,
      body,
      data,
      richContent: image ? { image } : undefined,
    } as ExpoPushMessage;
  }

  /**
   * Send a single push
   * @param to - The token to send the notification to.
   * @param title - The title of the notification.
   * @param body - The body of the notification.
   * @param data - The data of the notification.
   * @param image - The image of the notification.
   * @returns The result of the notification send.
   */
  async send(
    to: string,
    title: string,
    body: string,
    data?: Record<string, any>,
    image?: string,
  ): Promise<ExpoPushTicket[]> {
    const message = this.buildMessage(to, title, body, data, image);
    return this.sendMessages([message]);
  }

  /**
   * Send the same content to multiple tokens
   * @param tokens - The tokens to send the notification to.
   * @param title - The title of the notification.
   * @param body - The body of the notification.
   * @param data - The data of the notification.
   * @param image - The image of the notification.
   * @returns The result of the notification send.
   */
  async sendToTokens(
    tokens: string[],
    title: string,
    body: string,
    data?: Record<string, any>,
    image?: string,
  ): Promise<ExpoPushTicket[]> {
    const messages = tokens.map((to) =>
      this.buildMessage(to, title, body, data, image),
    );
    return this.sendMessages(messages);
  }

  /**
   * Send personalized messages
   * @param messages - The messages to send the notification to.
   * @returns The result of the notification send.
   */
  async sendMessages(messages: ExpoPushMessage[]): Promise<ExpoPushTicket[]> {
    try {
      if (!messages || messages.length === 0) {
        return [];
      }

      const invalidTokens = messages
        .flatMap((message) =>
          Array.isArray(message.to) ? message.to : [message.to],
        )
        .filter((token) => !Expo.isExpoPushToken(token));
      if (invalidTokens.length > 0) {
        this.logger.warn(`[Expo] Invalid tokens: ${invalidTokens.join(', ')}`);
      }

      const chunks = this.expo.chunkPushNotifications(messages);
      const tickets: ExpoPushTicket[] = [];

      for (const chunk of chunks) {
        try {
          const ticketChunk: ExpoPushTicket[] =
            await this.expo.sendPushNotificationsAsync(chunk);
          tickets.push(...ticketChunk);
        } catch (error) {
          this.logger.error('[Expo] Push send chunk failed', error);
        }
      }

      return tickets;
    } catch (error) {
      this.logger.error('[Expo] Push send failed', error);
      return [];
    }
  }
}
