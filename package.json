{"name": "laundry-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix && prettier --check . --ignore-path .prettierignore", "check-types": "tsc --noEmit --pretty", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./apps/api/test/jest-e2e.json", "test:e2e:watch": "NODE_ENV=test jest --config ./apps/api/test/jest-e2e.json --watch", "test:e2e:coverage": "NODE_ENV=test jest --config ./apps/api/test/jest-e2e.json --coverage", "prepare": "husky", "nest:generate": "nest generate", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js"}, "dependencies": {"@aws-sdk/client-chime-sdk-meetings": "^3.848.0", "@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/client-ses": "^3.848.0", "@aws-sdk/lib-storage": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@faker-js/faker": "^9.9.0", "@nestjs/axios": "^4.0.1", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.1.5", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.5", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cache-manager": "^5.7.6", "cache-manager-ioredis-yet": "^2.1.2", "cheerio": "^1.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "dotenv": "^17.2.0", "expo-server-sdk": "^3.15.0", "express-basic-auth": "^1.2.1", "express-session": "^1.18.2", "file-type": "^21.0.0", "handlebars": "^4.7.8", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "nestjs-cls": "^6.0.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-line": "^0.0.4", "passport-line-auth": "^0.2.9", "passport-local": "^1.0.0", "pg": "^8.16.3", "randomstring": "^1.3.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "^0.34.3", "typeorm": "^0.3.25", "webpack": "^5.100.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.1.5", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/express-session": "^1.18.2", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/multer": "^2.0.0", "@types/node": "^20.3.1", "@types/passport-facebook": "^3.0.3", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-no-async-foreach": "^0.1.1", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.(spec|e2e-spec)\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "transformIgnorePatterns": ["node_modules/(?!.*\\.mjs$)", "dist/"], "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/auth(|/.*)$": "<rootDir>/libs/auth/src/$1", "^@app/aws(|/.*)$": "<rootDir>/libs/aws/src/$1", "^@app/cache(|/.*)$": "<rootDir>/libs/cache/src/$1", "^@app/mail(|/.*)$": "<rootDir>/libs/mail/src/$1", "^@app/order(|/.*)$": "<rootDir>/libs/order/src/$1", "^@app/notification(|/.*)$": "<rootDir>/libs/notification/src/$1", "^@app/payment(|/.*)$": "<rootDir>/libs/payment/src/$1", "^@app/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^@app/store(|/.*)$": "<rootDir>/libs/store/src/$1", "^@app/user(|/.*)$": "<rootDir>/libs/user/src/$1", "^file-type$": "<rootDir>/apps/api/test/mocks/file-type.mock.js"}}}