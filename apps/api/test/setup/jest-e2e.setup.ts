import { SharedTestApp } from './test-app.setup';

/**
 * Jest E2E setup - runs before each test file
 * Configures global test environment and utilities
 */

// Global test utilities
global.getSharedTestApp = () => SharedTestApp.getInstance();
global.clearTestDatabase = () => SharedTestApp.clearDatabase();
global.clearThrottlingData = () => SharedTestApp.clearThrottlingData();

// Extend global types
declare global {
  var getSharedTestApp: () => Promise<any>;
  var clearTestDatabase: () => Promise<void>;
  var clearThrottlingData: () => Promise<void>;
}

// Set test timeout
jest.setTimeout(30000);
