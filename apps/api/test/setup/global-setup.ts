import { SharedTestApp } from './test-app.setup';

/**
 * Global setup - runs once before all test suites
 * Initializes shared test application for better performance
 */
export default async function globalSetup(): Promise<void> {
  console.log('🚀 Initializing shared test application...');

  try {
    await SharedTestApp.getInstance();
    console.log('✅ Shared test application initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize shared test application:', error);
    throw error;
  }
}
