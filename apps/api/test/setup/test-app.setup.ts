import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { setupApp } from '../../src/app';
import { AppModule } from '../../src/app.module';
import { TestDatabaseSetup } from '../utils/test-database.util';
import { createMockProviders } from './mock-providers.setup';
import { SESService } from '@app/aws/services/ses.service';
import { S3Service } from '@app/aws/services/s3.service';
import { ChimeService } from '@app/aws/services/chime.service';
import { MailService } from '@app/mail/services/mail.service';
import { NotificationService } from '@app/notification/services/notification.service';
import { HttpService } from '@nestjs/axios';

export class TestApp {
  private app: INestApplication;
  private dbSetup: TestDatabaseSetup;
  private moduleFixture: TestingModule;

  async init() {
    // Set NODE_ENV to test if not already set
    process.env.NODE_ENV = 'test';

    // Setup test database
    this.dbSetup = new TestDatabaseSetup();
    await this.dbSetup.init();

    // Create testing module with ONLY external service mocks
    this.moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    })
      // Only mock external services (AWS, Email, HTTP calls)
      .overrideProvider(SESService)
      .useValue(
        createMockProviders().find((p) => p.provide === 'SESService')?.useValue,
      )
      .overrideProvider(S3Service)
      .useValue(
        createMockProviders().find((p) => p.provide === 'S3Service')?.useValue,
      )
      .overrideProvider(ChimeService)
      .useValue(
        createMockProviders().find((p) => p.provide === 'ChimeService')
          ?.useValue,
      )
      .overrideProvider(MailService)
      .useValue(
        createMockProviders().find((p) => p.provide === 'MailService')
          ?.useValue,
      )
      .overrideProvider(NotificationService)
      .useValue(
        createMockProviders().find((p) => p.provide === 'NotificationService')
          ?.useValue,
      )
      .overrideProvider(HttpService)
      .useValue(
        createMockProviders().find((p) => p.provide === 'HttpService')
          ?.useValue,
      )

      .compile();

    this.app = this.moduleFixture.createNestApplication();

    // Apply the same setup as production
    await setupApp(this.app);

    await this.app.init();

    return this.app;
  }

  async cleanup() {
    if (this.app) {
      await this.app.close();
    }
    if (this.dbSetup) {
      await this.dbSetup.cleanup();
    }
    if (this.moduleFixture) {
      await this.moduleFixture.close();
    }
    // Clear throttling data
    await this.clearThrottlingData();
  }

  async clearDatabase() {
    if (this.dbSetup) {
      await this.dbSetup.clearDatabase();
    }
  }

  async clearThrottlingData() {
    try {
      const Redis = require('ioredis');
      const redis = new Redis({
        host: process.env.REDIS_HOST || 'redis',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      });

      const keys = await redis.keys('verify-code:*');
      if (keys.length > 0) {
        await redis.del(...keys);
      }

      await redis.disconnect();
    } catch (error) {
      console.warn('Failed to clear throttling data:', error.message);
    }
  }

  getApp() {
    return this.app;
  }

  getModuleFixture() {
    return this.moduleFixture;
  }
}

/**
 * Shared test application instance for better performance
 * Reused across all test suites instead of creating new instances
 */
class SharedTestApp {
  private static instance: INestApplication | null = null;
  private static testApp: TestApp | null = null;
  private static isInitializing = false;
  private static initPromise: Promise<INestApplication> | null = null;

  /**
   * Get or create shared test application instance
   */
  static async getInstance(): Promise<INestApplication> {
    if (this.instance) {
      return this.instance;
    }

    if (this.isInitializing && this.initPromise) {
      return this.initPromise;
    }

    this.isInitializing = true;
    this.initPromise = this.createInstance();

    try {
      this.instance = await this.initPromise;
      return this.instance;
    } finally {
      this.isInitializing = false;
      this.initPromise = null;
    }
  }

  /**
   * Create new test application instance
   */
  private static async createInstance(): Promise<INestApplication> {
    this.testApp = new TestApp();
    this.instance = await this.testApp.init();
    return this.instance;
  }

  /**
   * Cleanup shared test application
   */
  static async cleanup(): Promise<void> {
    if (this.testApp) {
      await this.testApp.cleanup();
      this.testApp = null;
    }
    this.instance = null;
  }

  /**
   * Get test app instance for database operations
   */
  static getTestApp(): TestApp | null {
    return this.testApp;
  }

  /**
   * Clear database between tests
   */
  static async clearDatabase(): Promise<void> {
    if (this.testApp) {
      await this.testApp.clearDatabase();
    }
  }

  /**
   * Clear throttling data between tests
   */
  static async clearThrottlingData(): Promise<void> {
    if (this.testApp) {
      await this.testApp.clearThrottlingData();
    }
  }
}

// Legacy exports for backward compatibility
let globalTestApp: TestApp;

export const getTestApp = () => globalTestApp;

export const initTestApp = async () => {
  // Use shared instance for better performance
  return await SharedTestApp.getInstance();
};

export const cleanupTestApp = async () => {
  await SharedTestApp.cleanup();
};

export const clearTestDatabase = async () => {
  await SharedTestApp.clearDatabase();
};

export const clearThrottlingData = async () => {
  await SharedTestApp.clearThrottlingData();
};

// Export SharedTestApp for new implementations
export { SharedTestApp };
