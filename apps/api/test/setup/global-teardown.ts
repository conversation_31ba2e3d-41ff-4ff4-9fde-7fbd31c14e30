import { SharedTestApp } from './test-app.setup';

/**
 * Global teardown - runs once after all test suites
 * Cleans up shared test application
 */
export default async function globalTeardown(): Promise<void> {
  console.log('🧹 Cleaning up shared test application...');

  try {
    await SharedTestApp.cleanup();
    console.log('✅ Shared test application cleaned up successfully');
  } catch (error) {
    console.error('❌ Failed to cleanup shared test application:', error);
    throw error;
  }
}
