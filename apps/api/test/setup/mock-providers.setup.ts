import { Test, TestingModule } from '@nestjs/testing';
import { MockServicesUtil } from '../utils/mock-services.util';
import { of } from 'rxjs';

/**
 * Mock providers for external services
 */
export const createMockProviders = () => {
  return [
    {
      provide: 'SESService',
      useValue: {
        sendEmail: jest.fn().mockImplementation((to, subject, content) => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockSendEmail(to, subject, content);
        }),
      },
    },
    {
      provide: 'NotificationService',
      useValue: {
        sendToDevice: jest.fn().mockResolvedValue(true),
        sendToMany: jest.fn().mockResolvedValue(true),
        sendToManyPersonalized: jest.fn().mockResolvedValue(true),
      },
    },
    {
      provide: 'S3Service',
      useValue: {
        saveImage: jest.fn().mockImplementation((file) => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockSaveImage(file);
        }),
        deleteObjects: jest.fn().mockImplementation((keys) => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockDeleteObjects(keys);
        }),
        getPresignedUrl: jest.fn().mockImplementation((key) => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockGetPresignedUrl(key);
        }),
      },
    },
    {
      provide: 'ChimeService',
      useValue: {
        createMeeting: jest.fn().mockImplementation(() => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockCreateMeeting();
        }),
        createAttendee: jest.fn().mockImplementation(() => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockCreateAttendee();
        }),
      },
    },
    {
      provide: 'MailService',
      useValue: {
        send: jest.fn().mockImplementation((to, subject, content) => {
          const mockUtil = new MockServicesUtil();
          return mockUtil.mockSendEmail(to, subject, content);
        }),
      },
    },
    {
      provide: 'HttpService',
      useValue: {
        get: jest.fn().mockImplementation((url, config) => {
          // Mock HTTP responses for social auth
          if (url.includes('googleapis.com')) {
            return of({
              status: 200,
              data: {
                sub: 'mock_google_sub',
                email: '<EMAIL>',
                name: 'Mock Google User',
                picture: 'https://mock-avatar.com/google.jpg',
              },
            });
          }
          if (url.includes('graph.facebook.com')) {
            return of({
              status: 200,
              data: {
                id: 'mock_facebook_id',
                name: 'Mock Facebook User',
                picture: 'https://mock-avatar.com/facebook.jpg',
              },
            });
          }
          if (url.includes('api.line.me')) {
            return of({
              status: 200,
              data: {
                sub: 'mock_line_sub',
                userId: 'mock_line_user_id',
                name: 'Mock Line User',
                displayName: 'Mock Line Display Name',
                picture: 'https://mock-avatar.com/line.jpg',
                pictureUrl: 'https://mock-avatar.com/line.jpg',
              },
            });
          }
          return of({
            status: 200,
            data: {},
          });
        }),
        post: jest.fn().mockImplementation((url, data) => {
          return of({
            status: 200,
            data: {},
          });
        }),
      },
    },
  ];
};

/**
 * Override module providers with mock implementations
 */
export const overrideModuleProviders = (module: TestingModule) => {
  const mockProviders = createMockProviders();

  // Note: In NestJS testing, we typically override providers during module creation
  // rather than after module compilation. This function is for reference.
  return module;
};
