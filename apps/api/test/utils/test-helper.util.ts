import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { faker } from '@faker-js/faker';

export class TestHelper {
  private app: INestApplication;

  constructor(app: INestApplication) {
    this.app = app;
  }

  /**
   * Create a test request instance
   */
  request() {
    return request(this.app.getHttpServer());
  }

  /**
   * Generate random test data
   */
  generateTestData() {
    return {
      user: {
        username: faker.internet.userName(),
        email: faker.internet.email(),
        password: faker.internet.password(),
        name: faker.person.fullName(),
        phone: faker.phone.number(),
      },
      auth: {
        accessToken: faker.string.alphanumeric(100),
        refreshToken: faker.string.alphanumeric(100),
      },
    };
  }

  /**
   * Wait for a specified time
   */
  async wait(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Retry a function with exponential backoff
   */
  async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxAttempts) {
          throw lastError;
        }

        await this.wait(delay * Math.pow(2, attempt - 1));
      }
    }

    throw lastError!;
  }

  /**
   * Create a mock file for testing file uploads
   */
  createMockFile(
    fieldname: string = 'file',
    originalname: string = 'test.jpg',
    mimetype: string = 'image/jpeg',
    size: number = 1024,
  ) {
    return {
      fieldname,
      originalname,
      encoding: '7bit',
      mimetype,
      size,
      buffer: Buffer.from('test file content'),
      stream: null,
      destination: null,
      filename: null,
      path: null,
    };
  }

  /**
   * Create a mock social profile
   */
  createMockSocialProfile(provider: string = 'google') {
    return {
      id: faker.string.uuid(),
      provider,
      email: faker.internet.email(),
      name: faker.person.fullName(),
      picture: faker.image.avatar(),
    };
  }

  /**
   * Validate response structure
   */
  validateResponseStructure(response: any, expectedFields: string[]) {
    expectedFields.forEach((field) => {
      expect(response.body).toHaveProperty(field);
    });
  }

  /**
   * Validate error response
   */
  validateErrorResponse(
    response: any,
    expectedStatus: number,
    expectedMessage?: string,
  ) {
    expect(response.status).toBe(expectedStatus);

    if (expectedMessage) {
      expect(response.body.message).toContain(expectedMessage);
    }

    expect(response.body).toHaveProperty('error');
    expect(response.body).toHaveProperty('statusCode');
  }

  /**
   * Create test headers with authentication
   */
  createAuthHeaders(
    accessToken: string,
    additionalHeaders: Record<string, string> = {},
  ) {
    return {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      ...additionalHeaders,
    };
  }

  /**
   * Create test headers with cookies
   */
  createCookieHeaders(
    accessToken: string,
    refreshToken: string,
    additionalHeaders: Record<string, string> = {},
  ) {
    return {
      Cookie: `accessToken=${accessToken}; refreshToken=${refreshToken}`,
      'Content-Type': 'application/json',
      ...additionalHeaders,
    };
  }
}
