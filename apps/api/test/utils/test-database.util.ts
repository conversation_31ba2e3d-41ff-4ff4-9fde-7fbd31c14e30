import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';
import typeormConfig from '@app/shared/config/typeorm.config';

export class TestDatabaseSetup {
  private dataSource: DataSource;

  async init() {
    // Load env.test file
    dotenv.config({ path: 'env.test', override: true, quiet: true });

    // Use typeorm.config.ts configuration
    const testDbConfig: DataSourceOptions = {
      ...typeormConfig(),
      synchronize: true, // Enable synchronize for tests
      logging: false,
      maxQueryExecutionTime: 1000, // Increase threshold to avoid slow query logs in tests
    };

    this.dataSource = new DataSource(testDbConfig);
    await this.dataSource.initialize();

    return this.dataSource;
  }

  async cleanup() {
    if (this.dataSource && this.dataSource.isInitialized) {
      await this.dataSource.destroy();
    }
  }

  async clearDatabase() {
    if (this.dataSource && this.dataSource.isInitialized) {
      const entities = this.dataSource.entityMetadatas;

      // Clear tables in reverse order to avoid foreign key constraints
      const reversedEntities = entities.reverse();

      for (const entity of reversedEntities) {
        try {
          const repository = this.dataSource.getRepository(entity.name);
          await repository.clear();
        } catch (error) {
          // If clear fails, try TRUNCATE CASCADE
          await this.dataSource.query(
            `TRUNCATE TABLE "${entity.tableName}" CASCADE;`,
          );
        }
      }
    }
  }

  async runMigrations() {
    if (this.dataSource && this.dataSource.isInitialized) {
      await this.dataSource.runMigrations();
    }
  }

  getDataSource() {
    return this.dataSource;
  }
}
