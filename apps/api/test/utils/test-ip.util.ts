/**
 * Utility functions for IP address testing
 */

/**
 * Generates a random fake IP address for testing purposes
 * @returns A random IP address in the format 192.168.x.x
 */
export function randomFakeIP(): string {
  return `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
}

/**
 * Generates a list of random fake IP addresses
 * @param count Number of IP addresses to generate
 * @returns Array of random IP addresses
 */
export function generateRandomFakeIPs(count: number): string[] {
  return Array.from({ length: count }, () => randomFakeIP());
}
