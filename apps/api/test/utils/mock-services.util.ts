import { Injectable, BadRequestException } from '@nestjs/common';

export interface MockSocialProfile {
  sub?: string;
  id?: string;
  email?: string;
  name?: string;
  picture?: string;
  pictureUrl?: string;
  userId?: string;
  displayName?: string;
}

@Injectable()
export class MockServicesUtil {
  mockGoogleVerification(accessToken: string): MockSocialProfile {
    if (!accessToken || accessToken === 'invalid_token') {
      throw new BadRequestException('Invalid access token');
    }

    return {
      sub: 'mock_google_user_id',
      email: '<EMAIL>',
      name: 'Google User',
      picture: 'https://example.com/avatar.jpg',
    };
  }

  mockFacebookVerification(accessToken: string): MockSocialProfile {
    if (!accessToken || accessToken === 'invalid_token') {
      throw new BadRequestException('Invalid access token');
    }

    return {
      id: 'mock_facebook_user_id',
      name: 'Facebook User',
      picture: 'https://example.com/avatar.jpg',
    };
  }

  mockLineVerification(accessToken: string): MockSocialProfile {
    if (!accessToken || accessToken === 'invalid_token') {
      throw new BadRequestException('Invalid access token');
    }

    return {
      sub: 'mock_line_user_id',
      name: 'Line User',
      pictureUrl: 'https://example.com/avatar.jpg',
    };
  }

  mockSendEmail(
    to: string,
    subject: string,
    content: string,
  ): Promise<boolean> {
    return Promise.resolve(true);
  }

  mockSaveImage(file: any): Promise<{ url: string }> {
    return Promise.resolve({ url: 'mock-url' });
  }

  mockDeleteObjects(keys: string[]): Promise<boolean> {
    return Promise.resolve(true);
  }

  mockGetPresignedUrl(key: string): Promise<string> {
    return Promise.resolve('mock-presigned-url');
  }

  mockCreateMeeting(): Promise<{ meetingId: string }> {
    return Promise.resolve({ meetingId: 'mock-meeting-id' });
  }

  mockCreateAttendee(): Promise<{ attendeeId: string }> {
    return Promise.resolve({ attendeeId: 'mock-attendee-id' });
  }
}
