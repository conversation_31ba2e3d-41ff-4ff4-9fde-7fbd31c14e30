import { JwtService } from '@nestjs/jwt';
import { User } from '@app/shared/database/entities';
import { faker } from '@faker-js/faker';

export class TestAuthUtil {
  private jwtService: JwtService;

  constructor(jwtService: JwtService) {
    this.jwtService = jwtService;
  }

  generateAccessToken(user: Partial<User>): string {
    return this.jwtService.sign(
      { userId: user.id },
      { secret: process.env.JWT_SECRET || 'test_secret' },
    );
  }

  generateRefreshToken(user: Partial<User>): string {
    return this.jwtService.sign(
      { userId: user.id },
      {
        secret: process.env.JWT_REFRESH_SECRET || 'test_refresh_secret',
        expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES_IN || '7d',
      },
    );
  }

  createTestUser(overrides: Partial<User> = {}): Partial<User> {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      username: faker.internet.userName(),
      password: faker.internet.password(),
      verifiedAt: new Date(),
      status: 'active' as any,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  createTestUserWithTokens(overrides: Partial<User> = {}): {
    user: Partial<User>;
    accessToken: string;
    refreshToken: string;
  } {
    const user = this.createTestUser(overrides);
    const accessToken = this.generateAccessToken(user);
    const refreshToken = this.generateRefreshToken(user);

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  getAuthHeaders(accessToken: string): Record<string, string> {
    return {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    };
  }

  getCookieHeaders(
    accessToken: string,
    refreshToken: string,
  ): Record<string, string> {
    return {
      Cookie: `accessToken=${accessToken}; refreshToken=${refreshToken}`,
      'Content-Type': 'application/json',
    };
  }
}
