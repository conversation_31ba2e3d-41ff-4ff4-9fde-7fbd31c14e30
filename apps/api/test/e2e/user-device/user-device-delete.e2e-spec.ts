import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { UserDeviceTokenService } from '@app/user/services/user-device-token.service';
import { faker } from '@faker-js/faker';

describe('UserDeviceController (e2e) - Delete Device', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let deviceService: UserDeviceTokenService;
  let testHelper: UserTestHelper;
  const baseEndpoint = '/users';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    deviceService = app.get<UserDeviceTokenService>(UserDeviceTokenService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
    await global.clearThrottlingData();
  });

  afterEach(async () => {
    await global.clearThrottlingData();
  });

  describe('DELETE /users/:userId/devices/:deviceCodeOrId', () => {
    describe('Success cases', () => {
      it('should delete device by deviceCode successfully', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const deviceCode = 'DEV-DEL-001';
        await deviceService.createOrUpdate(user.id, {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode,
          deviceName: 'iPhone 14 Pro',
          deviceType: 'mobile',
          deviceOs: 'iOS',
          osVersion: '17.0.3',
          appVersion: '1.0.0',
        } as any);

        const response = await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${user.id}/devices/${deviceCode}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toEqual({});
      });

      it('should delete device by id successfully', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const created = await deviceService.createOrUpdate(user.id, {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode: 'DEV-DEL-002',
        } as any);

        const response = await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${user.id}/devices/${created.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toEqual({});
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const deviceCode = 'DEV-DEL-401';
        await deviceService.createOrUpdate(user.id, {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode,
        } as any);

        await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${user.id}/devices/${deviceCode}`)
          .expect(401);
      });

      it('should return 403 for unauthorized access (different user)', async () => {
        const { user: targetUser } = await testHelper.createAuthenticatedUser();
        const { accessToken } = await testHelper.createAuthenticatedUser();

        const deviceCode = 'DEV-DEL-403';
        await deviceService.createOrUpdate(targetUser.id, {
          userId: targetUser.id,
          deviceToken: faker.string.uuid(),
          deviceCode,
        } as any);

        const response = await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${targetUser.id}/devices/${deviceCode}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'USER_NOT_OWNER');
      });

      it('should return 200 with false for non-existent device', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();
        const nonExistent = 'DEV-NOT-FOUND';

        const response = await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${user.id}/devices/${nonExistent}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toEqual({});
      });

      it('should return 404 for non-existent user', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser();
        const nonExistentUserId = faker.string.uuid();

        await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${nonExistentUserId}/devices/DEV-404`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);
      });
    });

    describe('Edge cases', () => {
      it('should return 400 for invalid UUID as userId param', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser();
        const invalidUserId = 'not-a-uuid';

        const response = await request(app.getHttpServer())
          .delete(`${baseEndpoint}/${invalidUserId}/devices/DEV-EDGE`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });
    });
  });
});
