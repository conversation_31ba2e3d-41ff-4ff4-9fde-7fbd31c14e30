import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { faker } from '@faker-js/faker';

describe('UserDeviceController (e2e) - Register Device', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const baseEndpoint = '/users';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
    await global.clearThrottlingData();
  });

  afterEach(async () => {
    await global.clearThrottlingData();
  });

  describe('POST /users/:userId/devices/register', () => {
    describe('Success cases', () => {
      it('should register device successfully', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const payload = {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode: 'DEV-001',
          deviceName: 'iPhone 14 Pro',
          deviceType: 'mobile',
          deviceOs: 'iOS',
          osVersion: '17.0.3',
          appVersion: '1.0.0',
        };

        await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);
      });

      it('should update existing device when registering with same deviceCode', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();
        const deviceCode = 'DEV-UPDATE-001';

        await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            userId: user.id,
            deviceToken: faker.string.uuid(),
            deviceCode,
            deviceName: 'Pixel 8',
          })
          .expect(200);

        await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            userId: user.id,
            deviceToken: faker.string.uuid(),
            deviceCode,
            deviceName: 'Pixel 8 Updated',
          })
          .expect(200);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const payload = {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode: 'DEV-002',
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for unauthorized access (different user)', async () => {
        const { user: targetUser } = await testHelper.createAuthenticatedUser();
        const { accessToken } = await testHelper.createAuthenticatedUser();

        const payload = {
          userId: targetUser.id,
          deviceToken: faker.string.uuid(),
          deviceCode: 'DEV-003',
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${targetUser.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'USER_NOT_OWNER');
      });

      it('should return 404 for non-existent user', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser();
        const nonExistentId = faker.string.uuid();

        const payload = {
          userId: nonExistentId,
          deviceToken: faker.string.uuid(),
          deviceCode: 'DEV-404',
        };

        await request(app.getHttpServer())
          .post(`${baseEndpoint}/${nonExistentId}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(404);
      });
    });

    describe('Validation errors', () => {
      it('should return 422 for missing deviceToken', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const payload: any = {
          userId: user.id,
          deviceCode: 'DEV-VAL-001',
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing deviceCode', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const payload: any = {
          userId: user.id,
          deviceToken: faker.string.uuid(),
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid userId format in body', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const payload: any = {
          userId: 'not-a-uuid',
          deviceToken: faker.string.uuid(),
          deviceCode: 'DEV-VAL-002',
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for whitespace-only deviceCode', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const payload: any = {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for deviceCode exceeding max length', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const payload: any = {
          userId: user.id,
          deviceToken: faker.string.uuid(),
          deviceCode: 'a'.repeat(255),
        };

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle malformed JSON with 400', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should return 422 for empty payload', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${baseEndpoint}/${user.id}/devices/register`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
