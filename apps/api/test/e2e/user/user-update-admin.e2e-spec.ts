import { AuthService } from '@app/auth/auth.service';
import { UserRole, UserStatus } from '@app/shared/database/entities';
import { UpdateUserAdminDto } from '@app/user/dto';
import { UserService } from '@app/user/services/user.service';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import request from 'supertest';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('UserController (e2e) - Update Admin', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('PATCH /users/admin/:id', () => {
    describe('Success cases', () => {
      it('should update user information successfully by admin', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'Updated Name',
          email: '<EMAIL>',
          phone: '0987654321',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });

      it('should update user role from STAFF to ADMIN successfully', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create staff user to update
        const { user: staffUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456787',
          role: UserRole.STAFF,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${staffUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });

      it('should update user role from ADMIN to STAFF successfully', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create another admin user to update
        const { user: anotherAdminUser } =
          await testHelper.createAuthenticatedUser({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin 2',
            phone: '0123456786',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        const payload: UpdateUserAdminDto = {
          role: UserRole.STAFF,
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${anotherAdminUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });

      it('should update user status successfully', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          status: UserStatus.INACTIVE,
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });

      it('should update partial user information', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'Partial Update',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });
    });

    describe('Error cases - Authentication & Authorization', () => {
      it('should return 401 for missing access token', async () => {
        // Create regular user
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid access token', async () => {
        // Create regular user
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', 'Bearer invalid_token')
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for non-admin user', async () => {
        // Create regular user as requester
        const { user: regularUser, accessToken: regularToken } =
          await testHelper.createAuthenticatedUser({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Regular User',
            phone: '**********',
            role: UserRole.CUSTOMER,
            status: UserStatus.ACTIVE,
          });

        // Create another user to update
        const { user: targetUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Target User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${targetUser.id}`)
          .set('Authorization', `Bearer ${regularToken}`)
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });
    });

    describe('Error cases - Validation', () => {
      it('should return 422 for invalid email format', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          email: 'invalid-email',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid phone format', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          phone: '123', // Invalid phone format
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid role', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          role: 'INVALID_ROLE',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid status', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          status: 'INVALID_STATUS',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent user', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        const payload: UpdateUserAdminDto = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/00000000-0000-0000-0000-000000000000`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 409 for duplicate email', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create two regular users
        const { user: user1 } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'User 1',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const { user: user2 } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'User 2',
          phone: '0123456786',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          email: user1.email, // Try to set user2's email to user1's email
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${user2.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'EMAIL_EXISTED');
      });

      it('should return 409 for duplicate phone', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create two regular users
        const { user: user1 } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'User 1',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const { user: user2 } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'User 2',
          phone: '0123456786',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          phone: user1.phone, // Try to set user2's phone to user1's phone
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${user2.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'PHONE_EXISTED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle null values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          name: null,
          email: null,
          phone: null,
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200); // Null values are filtered out by ValidationPipe

        expect(response.body).toBeDefined();
      });

      it('should handle undefined values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          name: undefined,
          email: undefined,
          phone: undefined,
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200); // Undefined values are filtered out by ValidationPipe

        expect(response.body).toBeDefined();
      });

      it('should handle whitespace-only values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: '   ',
          email: '   ',
          phone: '   ',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'a'.repeat(151), // Exceeds max length of 150
          email: 'a'.repeat(255) + '@example.com', // Exceeds max length
          phone: 'a'.repeat(255), // Exceeds max length
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '**********',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to update
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '**********',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload: UpdateUserAdminDto = {
          name: 'Special chars !@#$%^&*()',
          email: '<EMAIL>!@#$%^&*()',
          phone: '**********!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .patch(`${endpoint}/admin/${regularUser.id}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
