import { AuthService } from '@app/auth/auth.service';
import { UserRole, UserStatus } from '@app/shared/database/entities';
import { UserService } from '@app/user/services/user.service';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('UserController (e2e) - Admin Change Password', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
  });

  describe('PUT /users/admin/:id/change-password', () => {
    describe('Success cases', () => {
      it('should change user password successfully by admin', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'OldPassword123!', // Use the user's current password
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });

      it('should change staff password successfully by admin', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create staff user to change password
        const { user: staffUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Staff User',
          phone: '0123456787',
          role: UserRole.STAFF,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'OldPassword123!', // Use the user's current password
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${staffUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });

      it('should change admin password successfully by another admin', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create another admin user to change password
        const { user: anotherAdminUser } =
          await testHelper.createAuthenticatedUser({
            email: '<EMAIL>',
            password: 'OldPassword123!',
            name: 'Admin 2',
            phone: '0123456786',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        const payload = {
          currentPassword: 'OldPassword123!', // Use the user's current password
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${anotherAdminUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBeDefined();
      });
    });

    describe('Error cases - Authentication & Authorization', () => {
      it('should return 401 for missing access token', async () => {
        // Create regular user
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid access token', async () => {
        // Create regular user
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', 'Bearer invalid_token')
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for non-admin user', async () => {
        // Create regular user as requester
        const { user: regularUser, accessToken: regularToken } =
          await testHelper.createAuthenticatedUser({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Regular User',
            phone: '0123456788',
            role: UserRole.CUSTOMER,
            status: UserStatus.ACTIVE,
          });

        // Create another user to change password
        const { user: targetUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Target User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${targetUser.id}/change-password`)
          .set('Authorization', `Bearer ${regularToken}`)
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });

      it('should return 403 for staff user', async () => {
        // Create staff user as requester
        const { user: staffUser, accessToken: staffToken } =
          await testHelper.createAuthenticatedUser({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Staff User',
            phone: '0123456787',
            role: UserRole.STAFF,
            status: UserStatus.ACTIVE,
          });

        // Create target user to change password
        const { user: targetUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Target User',
          phone: '0123456786',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${targetUser.id}/change-password`)
          .set('Authorization', `Bearer ${staffToken}`)
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });
    });

    describe('Error cases - Validation', () => {
      it('should return 422 for invalid password format', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'weak', // Invalid password format
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing newPassword', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          // Missing newPassword
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing currentPassword', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          // Missing currentPassword
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for empty newPassword', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: '',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent user', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(
            `${endpoint}/admin/00000000-0000-0000-0000-000000000000/change-password`,
          )
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 400 for invalid current password', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'WrongPassword123!', // Wrong current password for the user
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'INVALID_PASSWORD');
      });

      it('should return 400 for new password same as current password', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'OldPassword123!',
          newPassword: 'OldPassword123!', // Same as current password
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty(
          'code',
          'NEW_PASSWORD_SAME_AS_OLD',
        );
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: null,
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: undefined,
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: '   ',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'a'.repeat(129), // Exceeds max length of 128
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in password', async () => {
        // Create admin user with auth
        const { user: adminUser, accessToken: adminToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Admin User',
            phone: '0123456789',
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
          });

        // Create regular user to change password
        const { user: regularUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456788',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        const payload = {
          currentPassword: 'Password123!',
          newPassword: 'Password123!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/admin/${regularUser.id}/change-password`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200); // Should succeed with special characters

        expect(response.body).toBeDefined();
      });
    });
  });
});
