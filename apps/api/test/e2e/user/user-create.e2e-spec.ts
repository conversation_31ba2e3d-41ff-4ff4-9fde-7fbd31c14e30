import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { UserRole } from '@app/shared/database/entities/user.entity';
import { ConfigService } from '@nestjs/config';

describe('UserController (e2e) - Create Users', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let configService: ConfigService;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
  });

  describe('POST /users', () => {
    describe('Success cases', () => {
      it('should create admin successfully with valid data', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New Admin',
          phone: '0123456790',
          role: UserRole.ADMIN,
          isAllowedNotify: true,
          gender: 'male',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('email', '<EMAIL>');
        expect(response.body).toHaveProperty('name', 'New Admin');
        expect(response.body).toHaveProperty('role', UserRole.ADMIN);
      });

      it('should create staff successfully with valid data', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456791',
          role: UserRole.STAFF,
          isAllowedNotify: false,
          gender: 'female',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('email', '<EMAIL>');
        expect(response.body).toHaveProperty('name', 'Staff User');
        expect(response.body).toHaveProperty('role', UserRole.STAFF);
      });

      it('should create admin with optional fields', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New Admin',
          phone: '0123456790',
          role: UserRole.ADMIN,
          avatar: 'https://example.com/avatar.jpg',
          gender: 'other',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('email', '<EMAIL>');
        expect(response.body).toHaveProperty('name', 'New Admin');
        expect(response.body).toHaveProperty('role', UserRole.ADMIN);
      });

      it('should create user successfully with E.164-like phone format', async () => {
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'E164 User',
          phone: '****** 567 8901',
          role: UserRole.STAFF,
        };

        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(201);
      });

      it('should create user with max length email (255 chars)', async () => {
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const local = 'a'.repeat(64);
        const domain = `${'b'.repeat(63)}.${'c'.repeat(63)}.${'d'.repeat(62)}`; // 190
        const email = `${local}@${domain}`; // 255
        expect(email.length).toBe(255);

        const payload = {
          email,
          password: 'Password123!',
          name: 'Max Email User',
          phone: '0123456792',
          role: UserRole.STAFF,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('email', email);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New Admin',
          phone: '0123456790',
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);
      });

      it('should return 403 for non-admin user', async () => {
        // Create non-admin user
        const regularUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456789',
          role: UserRole.CUSTOMER,
        });

        const userToken = authService.generateAccessToken({
          userId: regularUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New Admin',
          phone: '0123456790',
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${userToken}`)
          .send(payload)
          .expect(403);
      });

      it('should return 422 for validation errors', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: 'invalid-email',
          password: 'weak',
          name: '',
          phone: '123',
          role: 'invalid-role',
          gender: 'invalid',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 when phone exceeds maximum allowed length', async () => {
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Too Long',
          phone: '12345678901234567890', // 20 chars -> exceeds 15
          role: UserRole.CUSTOMER,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 when email exceeds maximum length (256 chars)', async () => {
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const local = 'a'.repeat(64);
        const domain = `${'b'.repeat(63)}.${'c'.repeat(63)}.${'d'.repeat(63)}`; // 191
        const email = `${local}@${domain}`; // 256
        expect(email.length).toBe(256);

        const payload = {
          email,
          password: 'Password123!',
          name: 'Too Long Email',
          phone: '0123456793',
          role: UserRole.STAFF,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 409 for existing email', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        // Create user with same email
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Existing User',
          phone: '0123456790',
          role: UserRole.CUSTOMER,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New Admin',
          phone: '0123456791',
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'EMAIL_EXISTED');
      });

      it('should return 409 for existing phone', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        // Create user with same phone
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Existing User',
          phone: '0123456790',
          role: UserRole.CUSTOMER,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New Admin',
          phone: '0123456790',
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'PHONE_EXISTED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: null,
          password: null,
          name: null,
          phone: null,
          role: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: undefined,
          password: undefined,
          name: undefined,
          phone: undefined,
          role: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '   ',
          password: '   ',
          name: '   ',
          phone: '   ',
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: 'a'.repeat(255) + '@example.com',
          password: 'Password123!',
          name: 'a'.repeat(255),
          phone: 'a'.repeat(255),
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        // Create admin user first to authenticate
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });

        const payload = {
          email: '<EMAIL>!@#$%^&*()',
          password: 'Password123!',
          name: 'special chars !@#$%^&*()',
          phone: '0123456789!@#$%^&*()',
          role: UserRole.ADMIN,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Email Plus Symbol Validation', () => {
      let adminUser: any;
      let adminToken: string;

      beforeEach(async () => {
        // Create admin user for authentication
        adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        adminToken = authService.generateAccessToken({
          userId: adminUser.id,
        });
      });

      describe('When BLOCK_EMAIL_PLUS_SYMBOL is disabled (default)', () => {
        beforeEach(() => {
          // Reset config to default (disabled)
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'false';
        });

        it('should accept emails with + symbol when blocking is disabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test Admin',
            phone: '0123456790',
            role: UserRole.ADMIN,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(201);

          expect(response.body).toHaveProperty('id');
          expect(response.body).toHaveProperty(
            'email',
            '<EMAIL>',
          );
        });

        it('should accept staff emails with + symbol when blocking is disabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test Staff',
            phone: '0123456791',
            role: UserRole.STAFF,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(201);

          expect(response.body).toHaveProperty(
            'email',
            '<EMAIL>',
          );
          expect(response.body).toHaveProperty('role', UserRole.STAFF);
        });

        it('should accept complex email formats with + when blocking is disabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456792',
            role: UserRole.ADMIN,
          };

          await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(201);
        });
      });

      describe('When BLOCK_EMAIL_PLUS_SYMBOL is enabled', () => {
        beforeEach(() => {
          // Enable email plus symbol blocking
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'true';
        });

        afterEach(() => {
          // Reset to default after tests
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'false';
        });

        it('should reject admin emails with + symbol when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test Admin',
            phone: '0123456790',
            role: UserRole.ADMIN,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
          expect(response.body.message).toBe('Unprocessable Entity');
        });

        it('should reject staff emails with + symbol when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test Staff',
            phone: '0123456791',
            role: UserRole.STAFF,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
        });

        it('should reject emails with multiple + symbols when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456792',
            role: UserRole.ADMIN,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
        });

        it('should accept normal emails without + symbol when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Normal User',
            phone: '0123456793',
            role: UserRole.ADMIN,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(201);

          expect(response.body).toHaveProperty(
            'email',
            '<EMAIL>',
          );
        });

        it('should provide clear error message for + symbol in email', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456794',
            role: UserRole.ADMIN,
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
          expect(response.body.message).toBe('Unprocessable Entity');
          // The validation error should contain information about the + symbol restriction
        });
      });

      describe('Environment variable validation', () => {
        it('should handle invalid BLOCK_EMAIL_PLUS_SYMBOL values', async () => {
          // Test with invalid boolean string
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'invalid';

          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456795',
            role: UserRole.ADMIN,
          };

          // Should default to false (allow + symbols) when invalid value is provided
          await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(201);

          // Reset
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'false';
        });

        it('should handle missing BLOCK_EMAIL_PLUS_SYMBOL environment variable', async () => {
          // Temporarily remove the environment variable
          const originalValue = process.env.BLOCK_EMAIL_PLUS_SYMBOL;
          delete process.env.BLOCK_EMAIL_PLUS_SYMBOL;

          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456796',
            role: UserRole.ADMIN,
          };

          // Should default to false (allow + symbols) when env var is missing
          await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(payload)
            .expect(201);

          // Restore original value
          if (originalValue !== undefined) {
            process.env.BLOCK_EMAIL_PLUS_SYMBOL = originalValue;
          }
        });
      });
    });
  });
});
