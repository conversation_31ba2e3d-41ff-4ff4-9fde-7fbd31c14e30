import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { faker } from '@faker-js/faker';

describe('UserController (e2e) - Get', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('GET /users/:id', () => {
    const endpoint = '/users';

    describe('Success cases', () => {
      it('should get user details by id successfully', async () => {
        const { user, accessToken } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Test User',
          phone: '0123456789',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${user.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id', user.id);
        expect(response.body).toHaveProperty('email', user.email);
        expect(response.body).toHaveProperty('name', user.name);
        expect(response.body).toHaveProperty('phone', user.phone);
        expect(response.body).toHaveProperty('isAllowedNotify');
      });
    });

    describe('Test', () => {
      it('should return 401 for missing access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${user.id}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 404 for non-existent user', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser();
        const nonExistentId = faker.string.uuid(); // Use valid UUID format

        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${nonExistentId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 401 for invalid access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${user.id}`)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });
  });
});
