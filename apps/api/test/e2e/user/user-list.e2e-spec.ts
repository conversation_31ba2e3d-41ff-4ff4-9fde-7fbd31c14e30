import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { UserRole, UserStatus } from '@app/shared/database/entities';

describe('UserController (e2e) - List Users', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('GET /users', () => {
    describe('Success cases', () => {
      it('should get all users successfully with default pagination', async () => {
        // Create admin user for authentication
        const { accessToken } = await testHelper.createAdminUserWithAuth({
          email: '<EMAIL>',
          name: 'Admin User',
        });

        // Create some users with different roles
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin 1',
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'Staff 1',
          role: UserRole.STAFF,
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'Customer 1',
          role: UserRole.CUSTOMER,
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
        expect(Array.isArray(response.body.items)).toBe(true);
        expect(typeof response.body.count).toBe('number');
        expect(response.body.count).toBeGreaterThan(0);
      });

      it('should get users with custom pagination', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create multiple users
        for (let i = 0; i < 5; i++) {
          await testHelper.createAdminUserInDb({
            email: `admin${i}@example.com`,
            name: `Admin ${i}`,
          });
        }

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?skip=0&limit=3`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeLessThanOrEqual(3);
        expect(response.body.count).toBeGreaterThan(0);
      });

      it('should filter users by roles array', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with different roles
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin 1',
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'Staff 1',
          role: UserRole.STAFF,
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'Customer 1',
          role: UserRole.CUSTOMER,
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?roles=admin&roles=staff`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect([UserRole.ADMIN, UserRole.STAFF]).toContain(user.role);
        });
      });

      it('should filter users by statuses array', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with different statuses
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Active Admin',
          status: UserStatus.ACTIVE,
        });
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Inactive Admin',
          status: UserStatus.INACTIVE,
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?statuses=active`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.status).toBe(UserStatus.ACTIVE);
        });
      });

      it('should filter users by keyword', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with specific names
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'John Admin',
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'John Staff',
          role: UserRole.STAFF,
        });
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Jane Admin',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=John`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.name).toMatch(/John/i);
        });
      });

      it('should filter users by multiple keywords with AND logic', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with specific names
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'John Admin',
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'John Staff',
          role: UserRole.STAFF,
        });
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Jane Admin',
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'Peter Staff',
          role: UserRole.STAFF,
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=john admin`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.name.toLowerCase()).toMatch(/john/);
          expect(user.name.toLowerCase()).toMatch(/admin/);
        });
      });

      it('should handle case-insensitive search', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'JOHN ADMIN',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=john`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.name.toLowerCase()).toMatch(/john/);
        });
      });

      it('should handle partial matching', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'John Doe',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=jo`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.name.toLowerCase()).toMatch(/jo/);
        });
      });

      it('should handle empty keyword gracefully', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin User',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should handle whitespace-only keyword gracefully', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin User',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=   `)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should filter users by multiple criteria', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with specific criteria
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'John Active',
          status: UserStatus.ACTIVE,
        });
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'John Inactive',
          status: UserStatus.INACTIVE,
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'John Staff',
          role: UserRole.STAFF,
          status: UserStatus.ACTIVE,
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=John&roles=admin&statuses=active`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.name).toMatch(/John/i);
          expect(user.role).toBe(UserRole.ADMIN);
          expect(user.status).toBe(UserStatus.ACTIVE);
        });
      });

      it('should handle single role parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with different roles
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin 1',
        });
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          name: 'Staff 1',
          role: UserRole.STAFF,
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?roles=admin`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.role).toBe(UserRole.ADMIN);
        });
      });

      it('should handle single status parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        // Create users with different statuses
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Active Admin',
          status: UserStatus.ACTIVE,
        });
        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Inactive Admin',
          status: UserStatus.INACTIVE,
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?statuses=inactive`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        response.body.items.forEach((user: any) => {
          expect(user.status).toBe(UserStatus.INACTIVE);
        });
      });
    });

    describe('Authentication and Authorization', () => {
      it('should return 401 for missing access token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid access token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for non-admin user', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Customer User',
          role: UserRole.CUSTOMER,
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });

      it('should return 403 for staff user', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Staff User',
          role: UserRole.STAFF,
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });

      it('should allow admin user to access the endpoint', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth({
          email: '<EMAIL>',
          name: 'Admin User',
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });
    });

    describe('Query parameters validation', () => {
      it('should handle invalid skip parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?skip=-1`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle invalid limit parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?limit=0`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle invalid roles parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?roles=invalid_role`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle invalid statuses parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?statuses=invalid_status`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle non-numeric skip parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?skip=abc`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle non-numeric limit parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?limit=abc`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Edge cases', () => {
      it('should handle empty keyword parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin User',
        });

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should handle very long keyword parameter', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const longKeyword = 'a'.repeat(1000);

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=${longKeyword}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should handle special characters in keyword', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const specialKeyword = '<EMAIL>!@#$%^&*()';

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=${encodeURIComponent(specialKeyword)}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should handle large skip number', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?skip=999999`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
        expect(response.body.items).toEqual([]);
      });

      it('should handle large limit number', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?limit=1000`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should handle empty roles array', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?roles=`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });

      it('should handle empty statuses array', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?statuses=`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
      });
    });

    describe('Response structure validation', () => {
      it('should return correct response structure', async () => {
        const { accessToken } = await testHelper.createAdminUserWithAuth();

        await testHelper.createAdminUserInDb({
          email: '<EMAIL>',
          name: 'Admin User',
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        // Validate items structure (User entity format)
        if (response.body.items.length > 0) {
          const user = response.body.items[0];
          expect(user).toHaveProperty('id');
          expect(user).toHaveProperty('username');
          expect(user).toHaveProperty('email');
          expect(user).toHaveProperty('phone');
          expect(user).toHaveProperty('password');
          expect(user).toHaveProperty('name');
          expect(user).toHaveProperty('address');
          expect(user).toHaveProperty('birthday');
          expect(user).toHaveProperty('language');
          expect(user).toHaveProperty('avatar');
          expect(user).toHaveProperty('role');
          expect(user).toHaveProperty('status');
          expect(user).toHaveProperty('verifiedAt');
          expect(user).toHaveProperty('createdAt');
          expect(user).toHaveProperty('updatedAt');
          expect(user).toHaveProperty('deletedAt');
          expect(user).toHaveProperty('socials');
        }

        // Validate count structure
        expect(response.body).toHaveProperty('count');
        expect(typeof response.body.count).toBe('number');
        expect(response.body.count).toBeGreaterThanOrEqual(0);
      });
    });
  });
});
