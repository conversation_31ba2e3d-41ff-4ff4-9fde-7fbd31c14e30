import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { RedisClientService } from '@app/cache/services/redis-client.service';
import { faker } from '@faker-js/faker';

describe('UserController (e2e) - Verify', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let redisService: RedisClientService;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
    redisService = app.get<RedisClientService>(RedisClientService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /users/:id/verify', () => {
    // Helper functions
    const generateAccessToken = async (userId: string) => {
      return await authService.generateAccessToken({ userId });
    };

    const createAuthenticatedUser = async (userData: any = {}) => {
      const user = await testHelper.createTestUserInDb({
        ...userData,
        verifiedAt: null, // Ensure user is not verified
      });
      const accessToken = await generateAccessToken(user.id);
      return { user, accessToken };
    };

    describe('Success cases', () => {
      it('should verify email successfully', async () => {
        const { user, accessToken } = await createAuthenticatedUser();

        // Wait a bit for the event to complete and code to be set
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Get the actual code that was set by sendMailVerifyEmail
        const actualCode = await redisService.get(
          `user-verify-email:${user.id}`,
        );

        if (!actualCode) {
          throw new Error('No verification code found in Redis');
        }

        const payload = { code: actualCode };

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const user = await testHelper.createUnverifiedUserInDb();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .send({ code: '123456' })
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 409 for already verified email', async () => {
        // Create verified user directly
        const user = await testHelper.createTestUserInDb({
          verifiedAt: new Date(),
        });
        const accessToken = await generateAccessToken(user.id);

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ code: '123456' })
          .expect(409);

        expect(response.body).toHaveProperty('code', 'EMAIL_ALREADY_VERIFIED');
      });

      it('should return 409 for invalid verification code', async () => {
        const user = await testHelper.createUnverifiedUserInDb();
        const accessToken = await generateAccessToken(user.id);

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ code: '000000' })
          .expect(409);

        expect(response.body).toHaveProperty(
          'code',
          'INVALID_VERIFY_EMAIL_CODE',
        );
      });

      it('should return 404 for non-existent user', async () => {
        const { accessToken } = await createAuthenticatedUser();
        const nonExistentId = faker.string.uuid(); // Use valid UUID format

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${nonExistentId}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ code: '123456' })
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });
    });

    describe('Rate limiting', () => {
      // DISABLED: Rate limiting is currently disabled
      /*
      it('should return 429 when rate limit exceeded', async () => {
        const user = await testHelper.createUnverifiedUserInDb();
        const accessToken = await generateAccessToken(user.id);

        // Send 6 requests consecutively to exceed limit (5 attempts)
        for (let i = 0; i < 6; i++) {
          const response = await request(app.getHttpServer())
            .post(`${endpoint}/${user.id}/verify`)
            .set('Authorization', `Bearer ${accessToken}`)
            .send({ code: '123456' });

          if (i < 5) {
            expect(response.status).toBe(409); // Invalid code
          } else {
            expect(response.status).toBe(429);
            expect(response.body).toHaveProperty(
              'message',
              'Too many requests. Please try again later.',
            );
            expect(response.body).toHaveProperty('statusCode', 429);
            expect(response.body).toHaveProperty('error', 'Too Many Requests');
          }
        }
      });
      */
    });

    describe('Validation errors', () => {
      it('should return 422 for missing code', async () => {
        const user = await testHelper.createUnverifiedUserInDb();
        const accessToken = await generateAccessToken(user.id);

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid code format', async () => {
        const user = await testHelper.createUnverifiedUserInDb();
        const accessToken = await generateAccessToken(user.id);

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ code: '123' }) // Too short
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 409 for code with non-numeric characters', async () => {
        const user = await testHelper.createUnverifiedUserInDb();
        const accessToken = await generateAccessToken(user.id);

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ code: '12a456' })
          .expect(409);

        expect(response.body).toHaveProperty(
          'code',
          'INVALID_VERIFY_EMAIL_CODE',
        );
      });
    });
  });
});
