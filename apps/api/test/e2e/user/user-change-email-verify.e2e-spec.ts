import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { faker } from '@faker-js/faker';

describe('UserController (e2e) - Change Email Verify', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /users/:id/change-email/verify', () => {
    describe('Success cases', () => {
      it('should verify and change email successfully', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        // Mock the verification code in Redis
        await testHelper.mockRedisVerificationCode(
          user.id,
          '123456',
          'change-email',
        );

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newEmail: '<EMAIL>',
            code: '123456',
          })
          .expect(200);

        // Only check status code, skip response body validation
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .send({
            newEmail: '<EMAIL>',
            code: '123456',
          })
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for unauthorized access (different user)', async () => {
        const { user: user1 } = await testHelper.createAuthenticatedUser();
        const { user: user2, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user1.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newEmail: '<EMAIL>',
            code: '123456',
          })
          .expect(403);

        expect(response.body).toHaveProperty('code', 'USER_NOT_OWNER');
      });

      it('should return 409 for email already exists', async () => {
        const { user: user1, accessToken } =
          await testHelper.createAuthenticatedUser({
            email: '<EMAIL>',
          });
        const { user: user2 } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
        });

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user1.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newEmail: '<EMAIL>',
            code: '123456',
          })
          .expect(409);

        expect(response.body).toHaveProperty('code', 'EMAIL_EXISTED');
      });

      it('should return 409 for invalid verification code', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newEmail: '<EMAIL>',
            code: '000000',
          })
          .expect(409);

        expect(response.body).toHaveProperty(
          'code',
          'INVALID_VERIFICATION_CODE',
        );
      });

      it('should return 404 for non-existent user', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();
        const nonExistentId = faker.string.uuid();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${nonExistentId}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: '123456',
          })
          .expect(404);

        // Only check status code, skip response body validation
      });
    });

    describe('Rate limiting', () => {
      // DISABLED: Rate limiting is currently disabled
      /*
      it('should return 429 when rate limit exceeded', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        // Send 6 requests consecutively to exceed limit (5 attempts)
        for (let i = 0; i < 6; i++) {
          const response = await request(app.getHttpServer())
            .post(`${endpoint}/${user.id}/change-email/verify`)
            .set('Authorization', `Bearer ${accessToken}`)
            .send({
              newEmail: '<EMAIL>',
              code: '123456',
            });

          if (i < 5) {
            expect(response.status).toBe(409); // Invalid code
          } else {
            expect(response.status).toBe(429);
            expect(response.body).toHaveProperty(
              'message',
              'Too many requests. Please try again later.',
            );
            expect(response.body).toHaveProperty('statusCode', 429);
            expect(response.body).toHaveProperty('error', 'Too Many Requests');
          }
        }
      });
      */
    });

    describe('Validation errors', () => {
      it('should return 422 for missing new email', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ code: '123456' })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing code', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ newEmail: '<EMAIL>' })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid email format', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newEmail: 'invalid-email',
            code: '123456',
          })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid code format', async () => {
        const { user, accessToken } =
          await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .post(`${endpoint}/${user.id}/change-email/verify`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newEmail: '<EMAIL>',
            code: '123',
          })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
