import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';

describe('UserController (e2e) - Me', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let jwtService: JwtService;
  const endpoint = '/users/me';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
    jwtService = app.get<JwtService>(JwtService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data giữa các test
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data sau mỗi test
  });

  describe('GET /users/me', () => {
    describe('Success cases', () => {
      it('should get current user details successfully', async () => {
        const { user, accessToken } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Test User',
          phone: '0123456789',
          isAllowedNotify: false,
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id', user.id);
        expect(response.body).toHaveProperty('email', user.email);
        expect(response.body).toHaveProperty('name', user.name);
        expect(response.body).toHaveProperty('phone', user.phone);
        expect(response.body).toHaveProperty('isAllowedNotify');
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid access token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for expired access token', async () => {
        // Create a token with a secret (using a dummy secret for test)
        const expiredToken = jwtService.sign(
          { userId: 'test-user-id' },
          { expiresIn: '0s', secret: 'test-secret' },
        );

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${expiredToken}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });
  });
});
