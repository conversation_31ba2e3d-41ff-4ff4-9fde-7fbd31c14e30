import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { faker } from '@faker-js/faker';

describe('UserController (e2e) - Change Password', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  // Helper function to generate access token
  const generateAccessToken = async (userId: string) => {
    return await authService.generateAccessToken({ userId });
  };

  // Helper function to create authenticated user
  const createAuthenticatedUser = async (userData: any = {}) => {
    // Generate unique email to avoid duplicate constraint
    const uniqueEmail = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
    const user = await testHelper.createTestUserInDb({
      ...userData,
      email: userData.email || uniqueEmail,
      phone:
        userData.phone ||
        `0123456${Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, '0')}`,
    });
    const accessToken = await generateAccessToken(user.id);
    return { user, accessToken };
  };

  describe('PUT /users/:id/change-password', () => {
    describe('Success cases', () => {
      it('should change password successfully', async () => {
        // Create test user with specific password
        const user = await testHelper.createTestUserInDb({
          password: 'OldPassword123!',
        });
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'OldPassword123!',
            newPassword: 'NewPassword123!',
          })
          .expect(200);

        // Only check status code, skip response body validation
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const { user } = await createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .send({
            oldPassword: 'OldPassword123!',
            newPassword: 'NewPassword123!',
          })
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for unauthorized access (different user)', async () => {
        const { user: user1 } = await createAuthenticatedUser();
        const { user: user2, accessToken } = await createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user1.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'OldPassword123!',
            newPassword: 'NewPassword123!',
          })
          .expect(403);

        expect(response.body).toHaveProperty('code', 'USER_NOT_OWNER');
      });

      it('should return 404 for non-existent user', async () => {
        const { user, accessToken } = await createAuthenticatedUser();
        const nonExistentId = faker.string.uuid();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${nonExistentId}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'Password123!',
            newPassword: 'NewPassword123!',
          })
          .expect(404);

        // Only check status code, skip response body validation
      });

      it('should return 400 for incorrect old password', async () => {
        const { user, accessToken } = await testHelper.createAuthenticatedUser({
          password: 'OldPassword123!',
        });

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'WrongPassword123!',
            newPassword: 'NewPassword123!',
          })
          .expect(400);

        // Only check status code, skip response body validation
      });
    });

    describe('Validation errors', () => {
      it('should return 422 for missing old password', async () => {
        const { user, accessToken } = await createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            newPassword: 'NewPassword123!',
          })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing new password', async () => {
        const { user, accessToken } = await createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'OldPassword123!',
          })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for weak new password', async () => {
        const { user, accessToken } = await createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'OldPassword123!',
            newPassword: 'weak',
          })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for new password without special characters', async () => {
        const { user, accessToken } = await createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${user.id}/change-password`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            oldPassword: 'OldPassword123!',
            newPassword: 'NewPassword123',
          })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
