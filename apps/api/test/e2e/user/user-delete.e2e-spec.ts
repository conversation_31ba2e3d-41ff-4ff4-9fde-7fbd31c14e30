import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { faker } from '@faker-js/faker';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('UserController (e2e) - Delete', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/users';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('DELETE /users/:id', () => {
    describe('Success cases', () => {
      it('should delete user successfully (admin)', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            name: 'Admin User',
          });

        const { user: targetUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Target User',
        });

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${targetUser.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);
      });

      it('should delete user successfully (admin deleting another user)', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            name: 'Admin User',
          });

        const { user: targetUser } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Target User',
        });

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${targetUser.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);
      });

      it('should delete user successfully (admin deleting self)', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth({
            email: '<EMAIL>',
            name: 'Admin Self User',
          });

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${adminUser.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${user.id}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for unauthorized access (non-admin deleting other user)', async () => {
        const { user: user1, accessToken } =
          await testHelper.createAuthenticatedUser();
        const { user: user2 } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${user2.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'USER_NOT_OWNER');
      });

      it('should delete self successfully (non-admin deleting self)', async () => {
        const { user, accessToken } = await testHelper.createAuthenticatedUser({
          email: '<EMAIL>',
          name: 'Self User',
        });

        await request(app.getHttpServer())
          .delete(`${endpoint}/${user.id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);
      });

      it('should return 404 for non-existent user', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth();
        const nonExistentId = faker.string.uuid(); // Use valid UUID format

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${nonExistentId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 401 for invalid access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${user.id}`)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle invalid UUID format', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth();
        const invalidId = 'invalid-uuid-format';

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${invalidId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code');
      });

      it('should handle empty user ID', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth();

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404); // Route not found

        expect(response.status).toBe(404);
      });

      it('should handle very long user ID', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth();
        const longId = 'a'.repeat(1000);

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${longId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code');
      });

      it('should handle special characters in user ID', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth();
        const specialId = 'user-id-with-special-chars!@#$%^&*()';

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${specialId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code');
      });

      it('should handle numeric user ID', async () => {
        const { user: adminUser, accessToken } =
          await testHelper.createAdminUserWithAuth();
        const numericId = '123456789';

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${numericId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code');
      });

      it('should handle malformed authorization header', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${user.id}`)
          .set('Authorization', 'malformed-header')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle missing authorization header', async () => {
        const { user } = await testHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${user.id}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle expired access token', async () => {
        const { user } = await testHelper.createAuthenticatedUser();
        const expiredToken =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${user.id}`)
          .set('Authorization', `Bearer ${expiredToken}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });
  });
});
