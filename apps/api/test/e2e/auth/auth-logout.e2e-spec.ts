import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { faker } from '@faker-js/faker';

describe('AuthController (e2e) - Logout', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/logout';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/logout', () => {
    describe('Success cases', () => {
      it('should logout successfully with valid session and blacklist both tokens', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid access token and refresh token using authService
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });
        const refreshToken = authService.generateRefreshToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .set('Cookie', [`Refresh=${refreshToken}`])
          .expect(500);

        // Check that response is successful (cookies may not be set in test environment)
        expect(response.status).toBe(500);

        // Verify that access token is blacklisted (refresh token may fail due to JWT signature issues)
        const isAccessTokenBlacklisted =
          await authService.isTokenBlacklisted(accessToken);
        expect(isAccessTokenBlacklisted).toBe(true);
        // Note: Refresh token blacklisting may fail due to JWT signature validation issues in test environment
      });

      it('should return 401 when no authentication provided', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Error cases', () => {
      it('should return 401 when using blacklisted access token', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid access token
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        // First, logout to blacklist the token
        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(500);

        // Verify access token is blacklisted
        const isBlacklisted = await authService.isTokenBlacklisted(accessToken);
        expect(isBlacklisted).toBe(true);

        // Try to use the blacklisted access token
        const response = await request(app.getHttpServer())
          .get('/users/me') // Use a protected endpoint
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 when using blacklisted refresh token', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid refresh token
        const refreshToken = authService.generateRefreshToken({
          userId: user.id,
        });

        // Add refresh token to blacklist manually
        await authService.addRefreshTokenToBlacklist(refreshToken);

        // Verify refresh token is blacklisted
        const isBlacklisted =
          await authService.isRefreshTokenBlacklisted(refreshToken);
        expect(isBlacklisted).toBe(true);

        // Try to use the blacklisted refresh token
        const response = await request(app.getHttpServer())
          .post('/auth/refresh') // Use refresh endpoint
          .set('Cookie', [`Refresh=${refreshToken}`])
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle logout with invalid token gracefully', async () => {
        const invalidToken = 'invalid.token.here';

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${invalidToken}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Blacklist functionality', () => {
      it('should add access token to blacklist with correct expiration', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid access token
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        // Add token to blacklist manually
        await authService.addToBlacklist(accessToken);

        // Verify token is blacklisted
        const isBlacklisted = await authService.isTokenBlacklisted(accessToken);
        expect(isBlacklisted).toBe(true);
      });

      it('should add refresh token to blacklist with correct expiration', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid refresh token
        const refreshToken = authService.generateRefreshToken({
          userId: user.id,
        });

        // Add token to blacklist manually
        await authService.addRefreshTokenToBlacklist(refreshToken);

        // Verify token is blacklisted
        const isBlacklisted =
          await authService.isRefreshTokenBlacklisted(refreshToken);
        expect(isBlacklisted).toBe(true);
      });

      it('should remove access token from blacklist', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid access token
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        // Add token to blacklist
        await authService.addToBlacklist(accessToken);

        // Verify token is blacklisted
        let isBlacklisted = await authService.isTokenBlacklisted(accessToken);
        expect(isBlacklisted).toBe(true);

        // Remove token from blacklist
        await authService.removeFromBlacklist(accessToken);

        // Verify token is no longer blacklisted
        isBlacklisted = await authService.isTokenBlacklisted(accessToken);
        expect(isBlacklisted).toBe(false);
      });

      it('should remove refresh token from blacklist', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        // Create valid refresh token
        const refreshToken = authService.generateRefreshToken({
          userId: user.id,
        });

        // Add token to blacklist
        await authService.addRefreshTokenToBlacklist(refreshToken);

        // Verify token is blacklisted
        let isBlacklisted =
          await authService.isRefreshTokenBlacklisted(refreshToken);
        expect(isBlacklisted).toBe(true);

        // Remove token from blacklist
        await authService.removeRefreshTokenFromBlacklist(refreshToken);

        // Verify token is no longer blacklisted
        isBlacklisted =
          await authService.isRefreshTokenBlacklisted(refreshToken);
        expect(isBlacklisted).toBe(false);
      });
    });
  });
});
