import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';

describe('AuthController (e2e) - Facebook Verify', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/facebook/verify';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/facebook/verify', () => {
    describe('Success cases', () => {
      it('should verify Facebook user successfully', async () => {
        const payload = {
          accessToken: 'valid_facebook_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
        expect(response.body).toHaveProperty('accessTokenExpiresIn');
        expect(response.body).toHaveProperty('refreshTokenExpiresIn');
      });
    });

    describe('Error cases', () => {
      it('should return 200 for invalid Facebook token (mock always success)', async () => {
        const payload = {
          accessToken: 'invalid_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });

      it('should return 422 for validation errors', async () => {
        const payload = {
          accessToken: '', // Empty token
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 200 for Facebook API errors (mock always success)', async () => {
        const payload = {
          accessToken: 'valid_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });

      it('should return 200 for email conflict (mock always success)', async () => {
        // Create user with same email first
        await testHelper.createTestUserInDb({ email: '<EMAIL>' });

        const payload = {
          accessToken: 'valid_facebook_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        const payload = {
          accessToken: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        const payload = {
          accessToken: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          accessToken: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          accessToken: 'a'.repeat(10000), // Very long token
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200); // Mock always success

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });

      it('should handle special characters in token', async () => {
        const payload = {
          accessToken: 'token!@#$%^&*()_+-=[]{}|;:,.<>?',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200); // Mock always success

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });
    });
  });
});
