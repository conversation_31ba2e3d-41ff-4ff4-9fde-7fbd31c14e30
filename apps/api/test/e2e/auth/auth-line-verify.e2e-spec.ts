import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('AuthController (e2e) - Line Verify', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/line/verify';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/line/verify', () => {
    describe('Success cases', () => {
      it('should verify Line user successfully', async () => {
        const payload = {
          accessToken: 'valid_line_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
        expect(response.body).toHaveProperty('accessTokenExpiresIn');
        expect(response.body).toHaveProperty('refreshTokenExpiresIn');
      });

      it('should verify Line user without access token (skip create user)', async () => {
        const payload = {
          accessToken: 'valid_line_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });
    });

    describe('Error cases', () => {
      it('should return 200 for invalid Line token (mock always success)', async () => {
        const payload = {
          accessToken: 'invalid_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });

      it('should return 409 for validation errors (Line specific)', async () => {
        const payload = {
          accessToken: '', // Empty token
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code');
      });

      it('should return 200 for Line API errors (mock always success)', async () => {
        const payload = {
          accessToken: 'valid_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });

      it('should return 200 for email conflict (mock always success)', async () => {
        // Create user with same email first
        await testHelper.createTestUserInDb({ email: '<EMAIL>' });

        const payload = {
          accessToken: 'valid_line_token',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });
    });

    describe('Test', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(409); // Line specific validation

        expect(response.body).toHaveProperty('code');
      });

      it('should handle null values', async () => {
        const payload = {
          accessToken: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409); // Line specific validation

        expect(response.body).toHaveProperty('code');
      });

      it('should handle undefined values', async () => {
        const payload = {
          accessToken: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409); // Line specific validation

        expect(response.body).toHaveProperty('code');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          accessToken: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409); // Line specific validation

        expect(response.body).toHaveProperty('code');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          accessToken: 'a'.repeat(10000), // Very long token
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200); // Mock always success

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });

      it('should handle special characters in token', async () => {
        const payload = {
          accessToken: 'token!@#$%^&*()_+-=[]{}|;:,.<>?',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200); // Mock always success

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
      });
    });
  });
});
