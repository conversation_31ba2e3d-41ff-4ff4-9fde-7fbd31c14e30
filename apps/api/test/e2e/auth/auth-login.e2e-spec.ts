import request from 'supertest';
import { INestApplication } from '@nestjs/common';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { AuthService } from '@app/auth';
import { UserService } from '@app/user/services/user.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

describe('AuthController (e2e) - Login', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();

    // Get services for test setup
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  afterEach(async () => {});

  describe('POST /auth/login', () => {
    const endpoint = '/auth/login';

    describe('Success cases', () => {
      it('should login successfully with email and password', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
        });

        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
        expect(response.body).toHaveProperty('accessTokenExpiresIn');
        expect(response.body).toHaveProperty('refreshTokenExpiresIn');
        expect(response.body.user.email).toBe(user.email);
      });

      it('should login successfully with phone and password', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
        });

        const payload = {
          username: '0123456789',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.user.phone).toBe(user.phone);
      });

      it('should login successfully with username and password', async () => {
        // Create test user with username
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
          username: 'testuser',
        });

        const payload = {
          username: 'testuser',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.user.username).toBe(user.username);
      });
    });

    describe('Error cases', () => {
      it('should return 404 for non-existent user', async () => {
        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 400 for invalid password', async () => {
        // Create test user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
        });

        const payload = {
          username: '<EMAIL>',
          password: 'WrongPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'INVALID_PASSWORD');
      });

      it('should return 422 for validation errors', async () => {
        const payload = {
          username: '',
          password: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401); // LocalAuthGuard throws 401 for empty credentials

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 422 for missing required fields', async () => {
        const payload = {
          username: '<EMAIL>',
          // Missing password
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401); // LocalAuthGuard throws 401 for missing password

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(401); // LocalAuthGuard throws 401 for empty payload

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle null values', async () => {
        const payload = {
          username: null,
          password: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401); // LocalAuthGuard throws 401 for null values

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle undefined values', async () => {
        const payload = {
          username: undefined,
          password: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401); // LocalAuthGuard throws 401 for undefined values

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          username: '   ',
          password: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404); // LocalAuthGuard throws 404 for non-existent user

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          username: 'a'.repeat(255),
          password: 'a'.repeat(255),
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404); // LocalAuthGuard throws 404 for non-existent user

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          username: 'test!@#$%^&*()@example.com',
          password: 'Password123!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404); // User not found

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle case sensitivity in username', async () => {
        // Create test user with lowercase email
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
        });

        const payload = {
          username: '<EMAIL>', // Uppercase
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404); // Should not find user due to case sensitivity

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle case sensitivity in password', async () => {
        // Create test user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
        });

        const payload = {
          username: '<EMAIL>',
          password: 'password123!', // Lowercase
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'INVALID_PASSWORD');
      });

      it('should handle different HTTP methods', async () => {
        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const methods = ['GET', 'PUT', 'PATCH', 'DELETE'];

        for (const method of methods) {
          const response = await request(app.getHttpServer())
            [method.toLowerCase()](endpoint)
            .send(payload);

          // Should return not found (404) for unsupported methods
          expect(response.status).toBe(404);
        }
      });

      it('should handle concurrent login attempts', async () => {
        // Create test user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
        });

        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        // Send multiple concurrent login requests
        const promises = Array(3)
          .fill(null)
          .map(() => request(app.getHttpServer()).post(endpoint).send(payload));

        const responses = await Promise.all(promises);

        // All should succeed
        responses.forEach((response) => {
          expect(response.status).toBe(200);
          expect(response.body).toHaveProperty('accessToken');
        });
      });
    });
  });
});
