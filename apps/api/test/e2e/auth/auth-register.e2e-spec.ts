import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { ConfigService } from '@nestjs/config';

describe('AuthController (e2e) - Register', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let configService: ConfigService;
  const endpoint = '/auth/register';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/register', () => {
    describe('Success cases', () => {
      it('should register user successfully with valid data', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201);

        // Only check status code, skip response body validation
      });

      it('should register user successfully with gender', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Has Gender',
          phone: '0123456799',
          birthday: '1990-01-01',
          gender: 'male',
        };

        await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201);
      });

      it('should register user with optional fields', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User 2',
          phone: '0123456790',
          birthday: '1990-01-01',
          avatar: 'https://example.com/avatar.jpg',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201);

        // Only check status code, skip response body validation
      });

      it('should register user successfully with E.164-like phone format', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test E164',
          phone: '+84 1234 567 890',
          birthday: '1990-01-01',
        };

        await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201);
      });

      it('should register user successfully with max length email (255 chars)', async () => {
        const local = 'a'.repeat(64);
        const domain = `${'b'.repeat(63)}.${'c'.repeat(63)}.${'d'.repeat(62)}`; // 63+1+63+1+62 = 190
        const email = `${local}@${domain}`; // 64 + 1 + 190 = 255
        expect(email.length).toBe(255);

        const payload = {
          email,
          password: 'Password123!',
          name: 'Max Email 255',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201);
      });
    });

    describe('Error cases', () => {
      it('should return 409 for existing email', async () => {
        // Create user with same email first
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Existing User',
          phone: '0123456789',
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New User',
          phone: '0123456790',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409);

        // Only check status code, skip response body validation
      });

      it('should return 409 for existing phone', async () => {
        // Create user with same phone first
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Existing User',
          phone: '0123456789',
        });

        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'New User',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409);

        // Only check status code, skip response body validation
      });

      it('should return 422 for validation errors', async () => {
        const payload = {
          email: 'invalid-email',
          password: 'weak',
          name: '',
          phone: '123', // Invalid phone format
          birthday: 'invalid-date', // Invalid date format
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid gender value', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Bad Gender',
          phone: '0123456798',
          birthday: '1990-01-01',
          gender: 'invalid',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 when phone exceeds maximum allowed length', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Too Long Phone',
          phone: '12345678901234567890', // 20 chars -> exceeds 15
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 when email exceeds maximum length (256 chars)', async () => {
        const local = 'a'.repeat(64);
        const domain = `${'b'.repeat(63)}.${'c'.repeat(63)}.${'d'.repeat(63)}`; // 63+1+63+1+63 = 191
        const email = `${local}@${domain}`; // 64 + 1 + 191 = 256
        expect(email.length).toBe(256);

        const payload = {
          email,
          password: 'Password123!',
          name: 'Too Long Email',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing required fields', async () => {
        const payload = {
          email: '<EMAIL>',
          // Missing password, name, phone, birthday
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Test', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);

        // Only check status code, skip response body validation
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        const payload = {
          email: null,
          password: null,
          name: null,
          phone: null,
          birthday: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        const payload = {
          email: undefined,
          password: undefined,
          name: undefined,
          phone: undefined,
          birthday: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          email: '   ',
          password: '   ',
          name: '   ',
          phone: '   ',
          birthday: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          email: 'a'.repeat(255) + '@example.com',
          password: 'a'.repeat(255),
          name: 'a'.repeat(255),
          phone: 'a'.repeat(255),
          birthday: 'a'.repeat(255),
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          email: 'test!@#$%^&*()@example.com',
          password: 'Password123!@#$%^&*()',
          name: 'User!@#$%^&*()',
          phone: '0123456789!@#$%^&*()',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle email with multiple @ symbols', async () => {
        const payload = {
          email: 'test@@example.com',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle password without required complexity', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'password', // No uppercase, no number, no special char
          name: 'Test User',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle phone with invalid format', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '123', // Too short
          birthday: '1990-01-01',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle invalid birthday format', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
          birthday: 'invalid-date-format', // Invalid date format
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle different HTTP methods', async () => {
        const payload = {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Test User',
          phone: '0123456789',
          birthday: '1990-01-01',
        };

        const methods = ['GET', 'PUT', 'PATCH', 'DELETE'];

        for (const method of methods) {
          const response = await request(app.getHttpServer())
            [method.toLowerCase()](endpoint)
            .send(payload);

          // Should return not found (404) for unsupported methods
          expect(response.status).toBe(404);
        }
      });
    });

    describe('Email Plus Symbol Validation', () => {
      describe('When BLOCK_EMAIL_PLUS_SYMBOL is disabled (default)', () => {
        beforeEach(() => {
          // Reset config to default (disabled)
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'false';
        });

        it('should accept emails with + symbol when blocking is disabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456789',
            birthday: '1990-01-01',
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(201);

          // Should succeed when blocking is disabled
          expect(response.status).toBe(201);
        });

        it('should accept emails with multiple + symbols when blocking is disabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456790',
            birthday: '1990-01-01',
          };

          await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(201);
        });

        it('should accept complex email formats with + when blocking is disabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456791',
            birthday: '1990-01-01',
          };

          await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(201);
        });
      });

      describe('When BLOCK_EMAIL_PLUS_SYMBOL is enabled', () => {
        beforeEach(() => {
          // Enable email plus symbol blocking
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'true';
        });

        afterEach(() => {
          // Reset to default after tests
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'false';
        });

        it('should reject emails with + symbol when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456789',
            birthday: '1990-01-01',
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
          expect(response.body.message).toBe('Unprocessable Entity');
        });

        it('should reject emails with multiple + symbols when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456790',
            birthday: '1990-01-01',
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
        });

        it('should reject complex email formats with + when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456791',
            birthday: '1990-01-01',
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
        });

        it('should accept normal emails without + symbol when blocking is enabled', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456792',
            birthday: '1990-01-01',
          };

          await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(201);
        });

        it('should accept emails with + in domain part when blocking is enabled', async () => {
          // Note: This is technically invalid email format, but testing edge case
          const payload = {
            email: 'test@exam+ple.com',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456793',
            birthday: '1990-01-01',
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload);

          // This should fail due to invalid email format, not plus symbol blocking
          expect(response.status).toBe(422);
          expect(response.body).toHaveProperty('errors');
        });

        it('should provide clear error message for + symbol in email', async () => {
          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456794',
            birthday: '1990-01-01',
          };

          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(422);

          expect(response.body).toHaveProperty('errors');
          expect(response.body.message).toBe('Unprocessable Entity');
          // The validation error should contain information about the + symbol restriction
        });
      });

      describe('Environment variable validation', () => {
        it('should handle invalid BLOCK_EMAIL_PLUS_SYMBOL values', async () => {
          // Test with invalid boolean string
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'invalid';

          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456795',
            birthday: '1990-01-01',
          };

          // Should default to false (allow + symbols) when invalid value is provided
          await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(201);

          // Reset
          process.env.BLOCK_EMAIL_PLUS_SYMBOL = 'false';
        });

        it('should handle missing BLOCK_EMAIL_PLUS_SYMBOL environment variable', async () => {
          // Temporarily remove the environment variable
          const originalValue = process.env.BLOCK_EMAIL_PLUS_SYMBOL;
          delete process.env.BLOCK_EMAIL_PLUS_SYMBOL;

          const payload = {
            email: '<EMAIL>',
            password: 'Password123!',
            name: 'Test User',
            phone: '0123456796',
            birthday: '1990-01-01',
          };

          // Should default to false (allow + symbols) when env var is missing
          await request(app.getHttpServer())
            .post(endpoint)
            .send(payload)
            .expect(201);

          // Restore original value
          if (originalValue !== undefined) {
            process.env.BLOCK_EMAIL_PLUS_SYMBOL = originalValue;
          }
        });
      });
    });
  });
});
