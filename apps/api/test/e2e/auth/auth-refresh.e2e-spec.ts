import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('AuthController (e2e) - Refresh', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/refresh';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/refresh', () => {
    describe('Success cases', () => {
      it('should refresh access token successfully', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();

        const refreshToken = authService.generateRefreshToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${refreshToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.accessToken).toBeDefined();
      });

      it('should return 401 for missing refresh token', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid refresh token', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for expired refresh token', async () => {
        const user = await testHelper.createTestUserInDb();
        const expiredRefreshToken = authService.generateRefreshToken(
          { userId: user.id },
          '0s',
        );

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${expiredRefreshToken}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });
  });
});
