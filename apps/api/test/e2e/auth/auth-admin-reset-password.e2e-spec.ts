import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { UserRole, UserStatus } from '@app/shared/database/entities';
import { ResetPasswordDto } from '@app/auth/dto/reset-password.dto';
import { ConfigService } from '@nestjs/config';
import { generateSignedUrl } from '@app/shared/helpers/url.helper';
import { randomFakeIP } from '../../utils/test-ip.util';

describe('AuthController (e2e) - Admin/Staff Reset Password', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/admin/reset-password';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('POST /auth/admin/reset-password', () => {
    describe('Success cases', () => {
      it('should reset admin password successfully with valid signed URL', async () => {
        // Create admin user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
        });

        // Generate signed URL data
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });

      it('should reset staff password successfully with valid signed URL', async () => {
        // Create staff user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Staff User',
          phone: '0123456788',
          role: UserRole.STAFF,
          status: UserStatus.ACTIVE,
        });

        // Generate signed URL data
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });
    });

    describe('Error cases - Authentication & Authorization', () => {
      it('should return 404 for regular user (not admin/staff)', async () => {
        // Create regular user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Regular User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        });

        // Generate signed URL data for regular user
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });
    });

    describe('Error cases - Validation', () => {
      it('should return 403 for invalid password format', async () => {
        const payload = {
          newPassword: 'weak', // Too weak password
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should return 403 for missing password', async () => {
        const payload = {};

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should return 403 for empty password', async () => {
        const payload = {
          newPassword: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent user', async () => {
        // Generate signed URL data for non-existent user
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 404 for unactivated user', async () => {
        // Create unverified user
        await testHelper.createUnverifiedUserInDb({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Unactivated User',
          phone: '0123456788',
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
        });

        // Generate signed URL data
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_ACTIVATED');
      });

      it('should return 404 for blocked user', async () => {
        // Create blocked user
        await testHelper.createBlockedUserInDb({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Blocked User',
          phone: '0123456787',
          role: UserRole.ADMIN,
          status: UserStatus.BLOCKED,
        });

        // Generate signed URL data
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_BLOCKED');
      });

      it('should return 403 for invalid signature', async () => {
        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'invalid-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should return 403 for expired signature', async () => {
        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1000000000', // Expired timestamp
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('Content-Type', 'application/json')
          .set('X-Forwarded-For', randomFakeIP())
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send({})
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should handle missing query parameters', async () => {
        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_MISSING_PARAMETERS');
      });

      it('should handle null values', async () => {
        const payload = {
          newPassword: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should handle undefined values', async () => {
        const payload = {
          newPassword: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          newPassword: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should handle maximum length password', async () => {
        const payload = {
          newPassword: 'a'.repeat(255), // Max length password
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: '<EMAIL>',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });

      it('should handle special characters in password', async () => {
        // Create admin user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'OldPassword123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
        });

        // Generate signed URL data
        const email = '<EMAIL>';
        const expiresIn = 6 * 60 * 10; // 60 minutes
        const signedUrlData = generateSignedUrl(
          '/auth/admin/reset-password',
          expiresIn,
          { email },
          configService,
        );

        // Reset password with special characters
        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: email,
            signature: signedUrlData.signature,
            expires: signedUrlData.expires.toString(),
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });

      it('should handle invalid email in query parameters', async () => {
        const payload: ResetPasswordDto = {
          newPassword: 'NewPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .query({
            email: 'invalid-email',
            signature: 'test-signature',
            expires: '1234567890',
          })
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'URL_EXPIRED');
      });
    });
  });
});
