import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';
import { faker } from '@faker-js/faker';

describe('AuthController (e2e) - Line Link', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/line/link';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/line/link', () => {
    describe('Success cases', () => {
      it('should redirect to Line OAuth for linking', async () => {
        // Create test user
        const user = await testHelper.createTestUserInDb();
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(302);

        // Should redirect to Line OAuth
        expect(response.headers.location).toBeDefined();
      });
    });

    describe('Error cases', () => {
      it('should return 401 for missing access token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);
      });

      it('should return 401 for invalid access token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'Bearer invalid_token')
          .expect(401);
      });

      it('should return 401 for expired access token', async () => {
        // Create expired token using authService
        const expiredToken = authService.generateAccessToken({
          userId: faker.string.uuid(),
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${expiredToken}`)
          .expect(401);
      });

      it('should return 401 for non-existent user', async () => {
        // Create token for non-existent user
        const token = authService.generateAccessToken({
          userId: faker.string.uuid(),
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .expect(401);
      });
    });

    describe('Error cases', () => {
      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'invalid_format')
          .expect(401);
      });

      it('should handle empty authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', '')
          .expect(401);
      });

      it('should handle whitespace-only authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', '   ')
          .expect(401);
      });

      it('should handle token with special characters', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'Bearer token!@#$%^&*()')
          .expect(401);
      });

      it('should handle very long token', async () => {
        const longToken = 'a'.repeat(10000);
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${longToken}`)
          .expect(401);
      });
    });
  });
});
