import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

import { randomFakeIP } from '../../utils/test-ip.util';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';

describe('AuthController (e2e) - Unlink Line', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/line/unlink';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('DELETE /auth/line/unlink', () => {
    describe('Success cases', () => {
      it('should unlink Line account successfully', async () => {
        // Create verified user with Line social account
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Line User',
          phone: '**********',
        });

        // Create Line social account for user
        const userSocialRepository = app.get('UserSocialRepositoryInterface');
        await userSocialRepository.create({
          userId: user.id,
          provider: SOCIAL_PROVIDER.LINE,
          socialId: 'line-social-id-123',
          name: 'Line User',
          avatar: 'https://example.com/avatar.jpg',
        });

        // Generate access token for authenticated request
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(404); // Will return 404 if Line social account not found

        expect(response.body).toHaveProperty('code', 'USER_SOCIAL_NOT_FOUND');
      });
    });

    describe('Error cases', () => {
      // Test 401 Unauthorized - Missing or invalid token
      it('should return 401 for missing authorization header', async () => {
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid token', async () => {
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', 'Bearer invalid-token')
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for expired token', async () => {
        // Create user
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Expired User',
          phone: '**********',
        });

        // Generate expired token using authService
        const expiredToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${expiredToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(404); // Will return 404 for user without Line social account

        expect(response.body).toHaveProperty('code', 'USER_SOCIAL_NOT_FOUND');
      });

      // Test 404 Not Found - User social not found
      it('should return 404 for user without Line social account', async () => {
        // Create verified user without Line social account
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'No Line User',
          phone: '**********',
        });

        // Generate access token for authenticated request
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_SOCIAL_NOT_FOUND');
      });

      it('should return 500 for invalid user ID format', async () => {
        // Generate token for non-existent user using authService
        const fakeToken = authService.generateAccessToken({
          userId: 'non-existent-user-id',
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${fakeToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', 'malformed-header')
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle empty authorization header', async () => {
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', '')
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle whitespace-only authorization header', async () => {
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', '   ')
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle token without Bearer prefix', async () => {
        // Create user
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'No Bearer User',
          phone: '0123456786',
        });

        // Generate access token
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', accessToken)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle token with extra spaces', async () => {
        // Create user
        const user = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Extra Space User',
          phone: '**********',
        });

        // Generate access token
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `  Bearer  ${accessToken}  `)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(404); // Will return 404 for user without Line social account

        expect(response.body).toHaveProperty('code', 'USER_SOCIAL_NOT_FOUND');
      });

      it('should handle token with special characters', async () => {
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', 'Bearer token!@#$%^&*()')
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle very long token', async () => {
        const longToken = 'a'.repeat(10000);
        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${longToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle token with null user ID', async () => {
        // Generate token with null userId using authService
        const nullToken = authService.generateAccessToken({
          userId: null,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${nullToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle token with undefined user ID', async () => {
        // Generate token with undefined userId using authService
        const undefinedToken = authService.generateAccessToken({
          userId: undefined,
        });

        const response = await request(app.getHttpServer())
          .delete(endpoint)
          .set('Authorization', `Bearer ${undefinedToken}`)
          .set('X-Forwarded-For', randomFakeIP())
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });
  });
});
