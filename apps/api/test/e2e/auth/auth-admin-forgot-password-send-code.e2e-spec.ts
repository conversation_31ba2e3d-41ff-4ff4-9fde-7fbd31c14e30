import { AuthService } from '@app/auth';
import { ForgotPasswordDto } from '@app/auth/dto/forgot-password.dto';
import { UserRole, UserStatus } from '@app/shared/database/entities';
import { UserService } from '@app/user/services/user.service';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import request from 'supertest';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('AuthController (e2e) - Admin/Staff Forgot Password Send Code', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();

    // Get services for test setup
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
    await global.clearThrottlingData();
  });

  afterEach(async () => {
    await global.clearThrottlingData();
  });

  describe('POST /auth/admin/forgot-password/send-code', () => {
    const endpoint = '/auth/admin/forgot-password/send-code';

    describe('Success cases', () => {
      it('should send forgot password code successfully for admin user', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });

      it('should send forgot password code successfully for staff user', async () => {
        // Create staff user
        const staffUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456788',
          role: UserRole.STAFF,
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent admin/staff user', async () => {
        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 404 for customer user (not admin/staff)', async () => {
        // Create customer user
        const customerUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Customer User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 404 for unactivated admin user', async () => {
        // Create unactivated admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
          status: UserStatus.INACTIVE,
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_ACTIVATED');
      });

      it('should return 404 for blocked admin user', async () => {
        // Create blocked admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
          status: UserStatus.BLOCKED,
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_BLOCKED');
      });
    });

    describe('Error cases - Validation', () => {
      it('should return 400 for invalid email format', async () => {
        const payload = {
          email: 'invalid-email',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for missing email', async () => {
        const payload = {};

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for empty email', async () => {
        const payload = {
          email: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for null email', async () => {
        const payload = {
          email: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for undefined email', async () => {
        const payload = {
          email: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });
    });

    describe('Rate limiting', () => {
      it('should return 429 when rate limit exceeded', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        // Send 4 times consecutively to exceed limit (3 times)
        for (let i = 0; i < 4; i++) {
          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload);

          if (i < 3) {
            expect(response.status).toBe(200);
          } else {
            expect(response.status).toBe(429);
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('statusCode', 429);
            expect(response.body).toHaveProperty('error', 'Too Many Requests');
          }
        }
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should handle whitespace-only email', async () => {
        const payload = {
          email: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should handle maximum length email', async () => {
        const payload = {
          email: 'a'.repeat(255) + '@example.com',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle special characters in email', async () => {
        const payload = {
          email: 'admin!@#$%^&*()@example.com',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should handle case sensitivity in email', async () => {
        // Create admin user with lowercase email
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          email: '<EMAIL>', // Uppercase
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle different HTTP methods', async () => {
        const payload = {
          email: '<EMAIL>',
        };

        const methods = ['GET', 'PUT', 'PATCH', 'DELETE'];

        for (const method of methods) {
          const response = await request(app.getHttpServer())
            [method.toLowerCase()](endpoint)
            .send(payload);

          // Should return not found (404) for unsupported methods
          expect(response.status).toBe(404);
        }
      });

      it('should verify role validation works correctly', async () => {
        // Test that role validation happens before calling userService.forgotPassword
        // Create both admin and customer users
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const customerUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Customer User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
        });

        // Admin should succeed
        const adminPayload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const adminResponse = await request(app.getHttpServer())
          .post(endpoint)
          .send(adminPayload)
          .expect(200);

        expect(adminResponse.body).toBe(true);

        // Customer should fail with USER_NOT_FOUND (role validation)
        const customerPayload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const customerResponse = await request(app.getHttpServer())
          .post(endpoint)
          .send(customerPayload)
          .expect(404);

        expect(customerResponse.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });
    });
  });
});
