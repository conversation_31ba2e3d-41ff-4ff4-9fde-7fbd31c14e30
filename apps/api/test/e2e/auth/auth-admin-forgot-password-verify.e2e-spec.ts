import { AuthService } from '@app/auth';
import { VerifyForgotPasswordDto } from '@app/auth/dto/verify-forgot-password.dto';
import { UserRole, UserStatus } from '@app/shared/database/entities';
import { UserService } from '@app/user/services/user.service';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import request from 'supertest';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('AuthController (e2e) - Admin/Staff Forgot Password Verify', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();

    // Get services for test setup
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
    await global.clearThrottlingData();
  });

  afterEach(async () => {
    await global.clearThrottlingData();
  });

  describe('POST /auth/admin/forgot-password/verify', () => {
    const endpoint = '/auth/admin/forgot-password/verify';

    describe('Success cases', () => {
      it('should verify forgot password code successfully for admin user', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        // First, send forgot password code
        await request(app.getHttpServer())
          .post('/auth/admin/forgot-password/send-code')
          .send({ email: '<EMAIL>' })
          .expect(200);

        // Get the code from Redis (in real scenario, user would get it via email)
        const code = await userService['redisService'].get(
          `user-forgot-password:${adminUser.id}`,
        );

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: code,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('signature');
        expect(response.body).toHaveProperty('expires');
        expect(typeof response.body.signature).toBe('string');
        expect(typeof response.body.expires).toBe('number');
      });

      it('should verify forgot password code successfully for staff user', async () => {
        // Create staff user
        const staffUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456788',
          role: UserRole.STAFF,
        });

        // First, send forgot password code
        await request(app.getHttpServer())
          .post('/auth/admin/forgot-password/send-code')
          .send({ email: '<EMAIL>' })
          .expect(200);

        // Get the code from Redis (in real scenario, user would get it via email)
        const code = await userService['redisService'].get(
          `user-forgot-password:${staffUser.id}`,
        );

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: code,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('signature');
        expect(response.body).toHaveProperty('expires');
        expect(typeof response.body.signature).toBe('string');
        expect(typeof response.body.expires).toBe('number');
      });
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent admin/staff user', async () => {
        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 404 for customer user (not admin/staff)', async () => {
        // Create customer user
        const customerUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Customer User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
        });

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 404 for unactivated admin user', async () => {
        // Create unactivated admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
          status: UserStatus.INACTIVE,
        });

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_ACTIVATED');
      });

      it('should return 404 for blocked admin user', async () => {
        // Create blocked admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
          status: UserStatus.BLOCKED,
        });

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_BLOCKED');
      });

      it('should return 404 for invalid verification code', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '000000', // Invalid code
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'INVALID_VERIFICATION_CODE',
        );
      });

      it('should return 404 for expired verification code', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        // Send code and then expire it
        await request(app.getHttpServer())
          .post('/auth/admin/forgot-password/send-code')
          .send({ email: '<EMAIL>' })
          .expect(200);

        // Delete the code from Redis to simulate expiration
        await userService['redisService'].del(
          `user-forgot-password:${adminUser.id}`,
        );

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'INVALID_VERIFICATION_CODE',
        );
      });
    });

    describe('Error cases - Validation', () => {
      it('should return 400 for invalid email format', async () => {
        const payload = {
          email: 'invalid-email',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for missing email', async () => {
        const payload = {
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for missing code', async () => {
        const payload = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for empty email', async () => {
        const payload = {
          email: '',
          code: '123456',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for empty code', async () => {
        const payload = {
          email: '<EMAIL>',
          code: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for null values', async () => {
        const payload = {
          email: null,
          code: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should return 400 for undefined values', async () => {
        const payload = {
          email: undefined,
          code: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });
    });

    describe('Rate limiting', () => {
      it('should return 429 when rate limit exceeded', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        // Send code first
        await request(app.getHttpServer())
          .post('/auth/admin/forgot-password/send-code')
          .send({ email: '<EMAIL>' })
          .expect(200);

        const code = await userService['redisService'].get(
          `user-forgot-password:${adminUser.id}`,
        );

        const payload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: code,
        };

        // Send 6 times consecutively to exceed limit (5 times)
        for (let i = 0; i < 6; i++) {
          const response = await request(app.getHttpServer())
            .post(endpoint)
            .send(payload);

          if (i < 5) {
            expect(response.status).toBe(200);
          } else {
            expect(response.status).toBe(429);
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('statusCode', 429);
            expect(response.body).toHaveProperty('error', 'Too Many Requests');
          }
        }
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          email: '   ',
          code: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          email: 'a'.repeat(255) + '@example.com',
          code: 'a'.repeat(255),
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          email: 'admin!@#$%^&*()@example.com',
          code: '123!@#',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('message');
      });

      it('should handle case sensitivity in email', async () => {
        // Create admin user with lowercase email
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        // Send code first
        await request(app.getHttpServer())
          .post('/auth/admin/forgot-password/send-code')
          .send({ email: '<EMAIL>' })
          .expect(200);

        const code = await userService['redisService'].get(
          `user-forgot-password:${adminUser.id}`,
        );

        const payload = {
          email: '<EMAIL>', // Uppercase
          code: code,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle different HTTP methods', async () => {
        const payload = {
          email: '<EMAIL>',
          code: '123456',
        };

        const methods = ['GET', 'PUT', 'PATCH', 'DELETE'];

        for (const method of methods) {
          const response = await request(app.getHttpServer())
            [method.toLowerCase()](endpoint)
            .send(payload);

          // Should return not found (404) for unsupported methods
          expect(response.status).toBe(404);
        }
      });

      it('should verify role validation works correctly', async () => {
        // Test that role validation happens before calling userService.verifyForgotPasswordCode
        // Create both admin and customer users
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const customerUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Customer User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
        });

        // Send code for admin first
        await request(app.getHttpServer())
          .post('/auth/admin/forgot-password/send-code')
          .send({ email: '<EMAIL>' })
          .expect(200);

        const adminCode = await userService['redisService'].get(
          `user-forgot-password:${adminUser.id}`,
        );

        // Admin should succeed
        const adminPayload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: adminCode,
        };

        const adminResponse = await request(app.getHttpServer())
          .post(endpoint)
          .send(adminPayload)
          .expect(200);

        expect(adminResponse.body).toHaveProperty('signature');
        expect(adminResponse.body).toHaveProperty('expires');

        // Customer should fail with USER_NOT_FOUND (role validation)
        const customerPayload: VerifyForgotPasswordDto = {
          email: '<EMAIL>',
          code: '123456',
        };

        const customerResponse = await request(app.getHttpServer())
          .post(endpoint)
          .send(customerPayload)
          .expect(404);

        expect(customerResponse.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });
    });
  });
});
