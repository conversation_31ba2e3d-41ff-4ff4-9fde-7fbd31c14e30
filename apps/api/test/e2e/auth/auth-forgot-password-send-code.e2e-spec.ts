import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { JwtService } from '@nestjs/jwt';

import { randomFakeIP } from '../../utils/test-ip.util';
import { ForgotPasswordDto } from '@app/auth/dto/forgot-password.dto';

describe('AuthController (e2e) - Forgot Password Send Code', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let testHelper: UserTestHelper;
  const endpoint = '/auth/forgot-password/send-code';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
    jwtService = app.get<JwtService>(JwtService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // Clear database between tests
    await global.clearThrottlingData(); // Clear throttling data between tests
  });

  afterEach(async () => {
    await global.clearThrottlingData(); // Clear throttling data after each test
  });

  describe('POST /auth/forgot-password/send-code', () => {
    describe('Success cases', () => {
      it('should send forgot password code successfully', async () => {
        // Create verified user first
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Forgot User',
          phone: '0123456789',
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(200);

        expect(response.body).toEqual({});
      });

      it('should send forgot password code successfully with phone number as email', async () => {
        // Create verified user with phone
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Phone User',
          phone: '0123456788',
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(200);

        expect(response.body).toEqual({});
      });
    });

    describe('Error cases - Based on API Documentation', () => {
      // Test 422 Validation Errors - class-validator errors
      it('should return 422 for invalid email format', async () => {
        const payload = {
          email: 'invalid-email',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing email', async () => {
        const payload = {};

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for empty email', async () => {
        const payload = {
          email: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      // Test 404 Not Found - User not found, not activated
      it('should return 404 for non-existent user', async () => {
        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 404 for unactivated user', async () => {
        // Create unverified user
        await testHelper.createUnverifiedUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Unactivated User',
          phone: '0123456787',
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_ACTIVATED');
      });

      it('should return 404 for blocked user', async () => {
        // Create blocked user
        await testHelper.createBlockedUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Blocked User',
          phone: '0123456786',
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_BLOCKED');
      });

      // Test 429 Too Many Requests - Rate limiting
      // DISABLED: Rate limiting is currently disabled
      /*
      it('should return 429 when rate limit exceeded', async () => {
        // Create verified user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Rate Limit User',
          phone: '0123456786',
        });

        const payload: ForgotPasswordDto = {
          email: '<EMAIL>',
        };

        const uniqueIP = '10.0.0.301';

        // Send multiple requests to exceed limit (4 attempts for 3 limit)
        for (let i = 0; i < 4; i++) {
          const response = await request(app.getHttpServer())
            .post(endpoint)
            .set('X-Forwarded-For', uniqueIP)
            .send(payload);

          if (i < 3) {
            expect(response.status).toBe(200);
          } else {
            expect(response.status).toBe(429);
            expect(response.body).toHaveProperty(
              'message',
              'Too many requests. Please try again later.',
            );
            expect(response.body).toHaveProperty('statusCode', 429);
            expect(response.body).toHaveProperty('error', 'Too Many Requests');
          }
        }
      });
      */
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .set('X-Forwarded-For', randomFakeIP())
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        const payload = {
          email: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        const payload = {
          email: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          email: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length email', async () => {
        const payload = {
          email: 'a'.repeat(254) + '@example.com', // Max length email
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422); // Should return 422 for validation error

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in email', async () => {
        const payload = {
          email: '<EMAIL>',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(404); // Should return 404 for non-existent user

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle email with multiple @ symbols', async () => {
        const payload = {
          email: 'test@@example.com',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle email without domain', async () => {
        const payload = {
          email: 'test@',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('X-Forwarded-For', randomFakeIP())
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
