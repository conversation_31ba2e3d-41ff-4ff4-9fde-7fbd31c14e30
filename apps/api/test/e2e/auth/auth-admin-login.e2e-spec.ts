import { AuthService } from '@app/auth';
import { UserRole } from '@app/shared/database/entities';
import { UserService } from '@app/user/services/user.service';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import request from 'supertest';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('AuthController (e2e) - Admin/Staff Login', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testHelper: UserTestHelper;

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();

    // Get services for test setup
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  afterEach(async () => {});

  describe('POST /auth/admin/login', () => {
    const endpoint = '/auth/admin/login';

    describe('Success cases', () => {
      it('should login admin successfully with email and password', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
        expect(response.body).toHaveProperty('accessTokenExpiresIn');
        expect(response.body).toHaveProperty('refreshTokenExpiresIn');
        expect(response.body.user.email).toBe(adminUser.email);
        expect(response.body.user.role).toBe(UserRole.ADMIN);
      });

      it('should login staff successfully with email and password', async () => {
        // Create staff user
        const staffUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456788',
          role: UserRole.STAFF,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
        expect(response.body.user.email).toBe(staffUser.email);
        expect(response.body.user.role).toBe(UserRole.STAFF);
      });

      it('should login admin successfully with phone and password', async () => {
        // Create admin user
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: '0123456789',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.user.phone).toBe(adminUser.phone);
        expect(response.body.user.role).toBe(UserRole.ADMIN);
      });

      it('should login staff successfully with phone and password', async () => {
        // Create staff user
        const staffUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456788',
          role: UserRole.STAFF,
        });

        const payload = {
          username: '0123456788',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.user.phone).toBe(staffUser.phone);
        expect(response.body.user.role).toBe(UserRole.STAFF);
      });

      it('should login admin successfully with username and password', async () => {
        // Create admin user with username
        const adminUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          username: 'adminuser',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: 'adminuser',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.user.username).toBe(adminUser.username);
        expect(response.body.user.role).toBe(UserRole.ADMIN);
      });

      it('should login staff successfully with username and password', async () => {
        // Create staff user with username
        const staffUser = await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456788',
          username: 'staffuser',
          role: UserRole.STAFF,
        });

        const payload = {
          username: 'staffuser',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.user.username).toBe(staffUser.username);
        expect(response.body.user.role).toBe(UserRole.STAFF);
      });
    });

    describe('Error cases - Authentication & Authorization', () => {
      it('should return 403 for non-admin/staff user login attempt', async () => {
        // Create regular user (not admin or staff)
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Regular User',
          phone: '0123456787',
          role: UserRole.CUSTOMER,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });

      it('should return 404 for non-existent admin/staff user', async () => {
        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should return 400 for invalid password for admin', async () => {
        // Create admin user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'WrongPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'INVALID_PASSWORD');
      });

      it('should return 400 for invalid password for staff', async () => {
        // Create staff user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Staff User',
          phone: '0123456788',
          role: UserRole.STAFF,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'WrongPassword123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'INVALID_PASSWORD');
      });

      it('should return 401 for validation errors', async () => {
        const payload = {
          username: '',
          password: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for missing required fields', async () => {
        const payload = {
          username: '<EMAIL>',
          // Missing password
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON for admin/staff login', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload for admin/staff login', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle null values for admin/staff login', async () => {
        const payload = {
          username: null,
          password: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle undefined values for admin/staff login', async () => {
        const payload = {
          username: undefined,
          password: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle whitespace-only values for admin/staff login', async () => {
        const payload = {
          username: '   ',
          password: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle maximum length values for admin/staff login', async () => {
        const payload = {
          username: 'a'.repeat(255),
          password: 'a'.repeat(255),
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle special characters in inputs for admin/staff login', async () => {
        const payload = {
          username: 'admin!@#$%^&*()@example.com',
          password: 'Password123!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle case sensitivity in username for admin/staff login', async () => {
        // Create admin user with lowercase email
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: '<EMAIL>', // Uppercase
          password: 'Password123!',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
      });

      it('should handle case sensitivity in password for admin/staff login', async () => {
        // Create admin user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'password123!', // Lowercase
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'INVALID_PASSWORD');
      });

      it('should handle different HTTP methods for admin/staff login', async () => {
        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        const methods = ['GET', 'PUT', 'PATCH', 'DELETE'];

        for (const method of methods) {
          const response = await request(app.getHttpServer())
            [method.toLowerCase()](endpoint)
            .send(payload);

          // Should return not found (404) for unsupported methods
          expect(response.status).toBe(404);
        }
      });

      it('should handle concurrent admin/staff login attempts', async () => {
        // Create admin user
        await testHelper.createTestUserInDb({
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          phone: '0123456789',
          role: UserRole.ADMIN,
        });

        const payload = {
          username: '<EMAIL>',
          password: 'Password123!',
        };

        // Send multiple concurrent login requests
        const promises = Array(3)
          .fill(null)
          .map(() =>
            request(app.getHttpServer()).post(endpoint).send(payload),
          );

        const responses = await Promise.all(promises);

        // All should succeed
        responses.forEach((response) => {
          expect(response.status).toBe(200);
          expect(response.body).toHaveProperty('accessToken');
          expect(response.body.user.role).toBe(UserRole.ADMIN);
        });
      });
    });
  });
});
