import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('OrderController (e2e) - Complete Order', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  let accessToken: string;
  let userId: string;
  let productId: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);

    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await userTestHelper.createTestUserInDb();
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = await productTestHelper.createTestProduct();
    productId = product.id;
  });

  describe('PUT /orders/:id/complete', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should complete order successfully', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Update order status to PAID (simulating payment success)
        await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: 'paid' })
          .expect(200);

        // Then complete the order
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id', orderId);
        expect(response.body).toHaveProperty('status', 'completed');
      });

      it('should return completed order with updated fields', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 2,
            },
          ],
          paymentMethod: PaymentMethod.MOMOPAY,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Update order status to PAID
        await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: 'paid' })
          .expect(200);

        // Then complete the order
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('userId', userId);
        expect(response.body).toHaveProperty('status', 'completed');
        expect(response.body).toHaveProperty('updatedAt');
        expect(new Date(response.body.updatedAt)).toBeInstanceOf(Date);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/complete`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 404 for non-existent order', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should return 400 for already completed order', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Update order status to PAID
        await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: 'paid' })
          .expect(200);

        // Complete the order first time
        await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        // Try to complete again
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'ORDER_ALREADY_COMPLETED');
      });

      it('should return 400 for order that cannot be completed', async () => {
        // First create an order (status: pending)
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Try to complete pending order
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty(
          'code',
          'ORDER_CANNOT_BE_COMPLETED',
        );
      });

      it('should return 401 for invalid token', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/complete`)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle missing authorization header', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/complete`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/complete`)
          .set('Authorization', 'InvalidFormat')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle invalid UUID format', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/invalid-uuid/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle very long UUID', async () => {
        const longUuid = 'a'.repeat(100);
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${longUuid}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle special characters in UUID', async () => {
        const specialUuid = '550e8400-e29b-41d4-a716-************!@#$%^&*()';
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${specialUuid}/complete`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });
    });
  });
});
