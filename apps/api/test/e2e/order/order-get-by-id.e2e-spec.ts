import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('OrderController (e2e) - Get Order by ID', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  let accessToken: string;
  let userId: string;
  let productId: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);

    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await userTestHelper.createTestUserInDb();
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = await productTestHelper.createTestProduct();
    productId = product.id;
  });

  describe('GET /orders/:id', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should get order by ID successfully', async () => {
        // Create order using helper
        const { order } = await orderTestHelper.createTestOrderWithExistingUser(
          {
            items: [{ productId: productId, quantity: 1 }],
            paymentMethod: PaymentMethod.POINTS,
          },
          { id: userId },
          accessToken,
        );

        const orderId = order.id;

        // Then get the order by ID
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id', orderId);
        expect(response.body).toHaveProperty('userId', userId);
        expect(response.body).toHaveProperty('items');
        expect(Array.isArray(response.body.items)).toBe(true);
        expect(response.body.items.length).toBeGreaterThan(0);
      });

      it('should return order with all required fields', async () => {
        // Create order using helper
        const { order } = await orderTestHelper.createTestOrderWithExistingUser(
          {
            items: [
              {
                productId: productId,
                quantity: 2,
                metadata: {
                  machineId: 'machine-uuid',
                  programId: 'program-uuid',
                },
              },
            ],
            paymentMethod: PaymentMethod.MOMOPAY,
          },
          { id: userId },
          accessToken,
        );

        const orderId = order.id;

        // Then get the order by ID
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('userId');
        expect(response.body).toHaveProperty('totalPoints');
        expect(response.body).toHaveProperty('totalAmount');
        expect(response.body).toHaveProperty('currency');
        expect(response.body).toHaveProperty('paymentMethod');
        expect(response.body).toHaveProperty('status');
        expect(response.body).toHaveProperty('createdAt');
        expect(response.body).toHaveProperty('updatedAt');
        expect(response.body).toHaveProperty('items');
        expect(response.body.items[0]).toHaveProperty('productId');
        expect(response.body.items[0]).toHaveProperty('quantity');
        expect(response.body.items[0]).toHaveProperty('totalPoints');
        expect(response.body.items[0]).toHaveProperty('metadata');
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 404 for non-existent order', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should return 404 for invalid UUID format', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/invalid-uuid`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should return 401 for invalid token', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle missing authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .set('Authorization', 'InvalidFormat')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle very long UUID', async () => {
        const longUuid = 'a'.repeat(100);
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${longUuid}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle special characters in UUID', async () => {
        const specialUuid = '550e8400-e29b-41d4-a716-************!@#$%^&*()';
        const response = await request(app.getHttpServer())
          .get(`${endpoint}/${specialUuid}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });
    });
  });
});
