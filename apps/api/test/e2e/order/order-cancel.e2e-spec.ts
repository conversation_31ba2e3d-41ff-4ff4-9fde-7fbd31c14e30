import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import {
  PaymentMethod,
  Product,
  ProductType,
  ProductCategory,
  ProductStatus,
} from '@app/shared/database/entities';
import { ProductRepositoryInterface } from '@app/shared/database/repositories';

describe('OrderController (e2e) - Cancel Order', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let productRepository: ProductRepositoryInterface;
  let accessToken: string;
  let userId: string;
  let productId: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    productRepository = app.get<ProductRepositoryInterface>(
      'ProductRepositoryInterface',
    );
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await testHelper.createTestUserInDb();
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = new Product();
    product.name = 'Test Product';
    product.slug = 'test-product';
    product.type = ProductType.SERVICE;
    product.category = ProductCategory.LAUNDRY_SERVICE;
    product.pricePoints = 1000;
    product.status = ProductStatus.ACTIVE;
    product.description = 'Test product for order creation';
    const savedProduct = await productRepository.save(product);
    productId = savedProduct.id;
  });

  describe('PUT /orders/:id/cancel', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should cancel order successfully', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Then cancel the order
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id', orderId);
        expect(response.body).toHaveProperty('status', 'cancelled');
      });

      it('should return cancelled order with updated fields', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 2,
            },
          ],
          paymentMethod: PaymentMethod.MOMOPAY,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Then cancel the order
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('userId', userId);
        expect(response.body).toHaveProperty('status', 'cancelled');
        expect(response.body).toHaveProperty('updatedAt');
        expect(new Date(response.body.updatedAt)).toBeInstanceOf(Date);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/cancel`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 404 for non-existent order', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should return 400 for already cancelled order', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Cancel the order first time
        await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        // Try to cancel again
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'ORDER_ALREADY_CANCELLED');
      });

      it('should return 400 for order that cannot be cancelled', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Update order status to PAID (simulating payment success)
        await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: 'paid' })
          .expect(200);

        // Try to cancel paid order
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${orderId}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(400);

        expect(response.body).toHaveProperty(
          'code',
          'ORDER_CANNOT_BE_CANCELLED',
        );
      });

      it('should return 401 for invalid token', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/cancel`)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle missing authorization header', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/cancel`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/550e8400-e29b-41d4-a716-************/cancel`)
          .set('Authorization', 'InvalidFormat')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle invalid UUID format', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/invalid-uuid/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle very long UUID', async () => {
        const longUuid = 'a'.repeat(100);
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${longUuid}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle special characters in UUID', async () => {
        const specialUuid = '550e8400-e29b-41d4-a716-************!@#$%^&*()';
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${specialUuid}/cancel`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });
    });
  });
});
