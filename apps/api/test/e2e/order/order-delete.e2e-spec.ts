import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import {
  PaymentMethod,
  Product,
  ProductType,
  ProductCategory,
  ProductStatus,
} from '@app/shared/database/entities';
import { ProductRepositoryInterface } from '@app/shared/database/repositories';

describe('OrderController (e2e) - Delete Order', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let productRepository: ProductRepositoryInterface;
  let accessToken: string;
  let userId: string;
  let productId: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    productRepository = app.get<ProductRepositoryInterface>(
      'ProductRepositoryInterface',
    );
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await testHelper.createTestUserInDb();
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = new Product();
    product.name = 'Test Product';
    product.slug = 'test-product';
    product.type = ProductType.SERVICE;
    product.category = ProductCategory.LAUNDRY_SERVICE;
    product.pricePoints = 1000;
    product.status = ProductStatus.ACTIVE;
    product.description = 'Test product for order creation';
    const savedProduct = await productRepository.save(product);
    productId = savedProduct.id;
  });

  describe('DELETE /orders/:id', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should delete order successfully', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Then delete the order
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
      });

      it('should delete order and its items', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 2,
              metadata: {
                machineId: 'machine-uuid',
                programId: 'program-uuid',
              },
            },
          ],
          paymentMethod: PaymentMethod.MOMOPAY,
        };

        const createResponse = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const orderId = createResponse.body.id;

        // Verify order exists
        await request(app.getHttpServer())
          .get(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        // Delete the order
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('success', true);

        // Verify order is deleted
        await request(app.getHttpServer())
          .get(`${endpoint}/${orderId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 404 for non-existent order', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should return 401 for invalid token', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle missing authorization header', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/550e8400-e29b-41d4-a716-************`)
          .set('Authorization', 'InvalidFormat')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle invalid UUID format', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/invalid-uuid`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle very long UUID', async () => {
        const longUuid = 'a'.repeat(100);
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${longUuid}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });

      it('should handle special characters in UUID', async () => {
        const specialUuid = '550e8400-e29b-41d4-a716-************!@#$%^&*()';
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${specialUuid}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'ORDER_NOT_FOUND');
      });
    });
  });
});
