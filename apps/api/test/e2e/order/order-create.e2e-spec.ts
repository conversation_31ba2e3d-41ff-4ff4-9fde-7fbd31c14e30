import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import {
  PaymentMethod,
  ProductType,
  ProductCategory,
  ProductStatus,
} from '@app/shared/database/entities';
import { ProductRepositoryInterface } from '@app/shared/database/repositories';

describe('OrderController (e2e) - Create Order', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  let accessToken: string;
  let userId: string;
  let productId: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);

    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await userTestHelper.createTestUserInDb();
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = await productTestHelper.createTestProduct();
    productId = product.id;
  });

  describe('POST /orders', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should create order successfully', async () => {
        const payload = {
          items: [
            {
              productId: productId,
              quantity: 1,
              metadata: {
                machineId: 'machine-uuid',
                programId: 'program-uuid',
              },
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('userId', userId);
        // expect(response.body).toHaveProperty('code');
        // expect(response.body.code).toMatch(/^ORD_\d{8}_\d{3}$/);
        expect(response.body).toHaveProperty('totalPoints');
        expect(response.body).toHaveProperty('totalAmount');
        expect(response.body).toHaveProperty('currency', 'VND');
        expect(response.body).toHaveProperty('exchangeRate', 1000.0);
        expect(response.body).toHaveProperty(
          'paymentMethod',
          PaymentMethod.POINTS,
        );
        expect(response.body).toHaveProperty('status', 'pending');
        expect(response.body).toHaveProperty('items');
        expect(response.body.items).toHaveLength(1);
        expect(response.body.items[0]).toHaveProperty(
          'productId',
          payload.items[0].productId,
        );
        expect(response.body.items[0]).toHaveProperty(
          'quantity',
          payload.items[0].quantity,
        );
      });

      it('should create order with momopay payment method', async () => {
        const payload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.MOMOPAY,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty(
          'paymentMethod',
          PaymentMethod.MOMOPAY,
        );
      });

      it('should create order with zalopay payment method', async () => {
        const payload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.ZALOPAY,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty(
          'paymentMethod',
          PaymentMethod.ZALOPAY,
        );
      });

      it('should create order with vnpay payment method', async () => {
        const payload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.VNPAY,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty(
          'paymentMethod',
          PaymentMethod.VNPAY,
        );
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const payload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload);

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 422 for validation errors', async () => {
        const payload = {
          items: [
            {
              productId: 'invalid-uuid',
              quantity: 0,
            },
          ],
          paymentMethod: '',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for empty items array', async () => {
        const payload = {
          items: [],
          paymentMethod: PaymentMethod.POINTS,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing required fields', async () => {
        const payload = {
          items: [
            {
              productId: productId,
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for null values', async () => {
        const payload = {
          items: [
            {
              productId: null,
              quantity: null,
            },
          ],
          paymentMethod: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for whitespace-only values', async () => {
        const payload = {
          items: [
            {
              productId: '   ',
              quantity: 1,
            },
          ],
          paymentMethod: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid payment method', async () => {
        const payload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: 'invalid-payment-method',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should return 404 for non-existent product', async () => {
        const payload = {
          items: [
            {
              productId: '550e8400-e29b-41d4-a716-446655440001',
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('code', 'PRODUCT_NOT_FOUND');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .set('Content-Type', 'application/json')
          .send('invalid json');

        expect(response.status).toBe(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({});

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        const payload = {
          items: [
            {
              productId: undefined,
              quantity: undefined,
            },
          ],
          paymentMethod: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          items: [
            {
              productId: 'a'.repeat(255),
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          items: [
            {
              productId: '<EMAIL>!@#$%^&*()',
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(422);
        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
