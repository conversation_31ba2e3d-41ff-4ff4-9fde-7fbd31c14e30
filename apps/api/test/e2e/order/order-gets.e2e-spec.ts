import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('OrderController (e2e) - Get Orders', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  let accessToken: string;
  let userId: string;
  let productId: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);

    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await userTestHelper.createTestUserInDb();
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = await productTestHelper.createTestProduct();
    productId = product.id;
  });

  describe('GET /orders', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should get user orders successfully', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
      });

      it('should return empty array when user has no orders', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toEqual([]);
      });

      it('should return user orders with items', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        await orderTestHelper.createTestOrder(createPayload, accessToken);

        // Then get orders
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);
        expect(response.body[0]).toHaveProperty('items');
        expect(Array.isArray(response.body[0].items)).toBe(true);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid token', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for expired token', async () => {
        const expiredToken = authService.generateAccessToken(
          { userId: '550e8400-e29b-41d4-a716-446655440000' },
          '0s',
        );

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${expiredToken}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle missing authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should handle malformed authorization header', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'InvalidFormat')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });
  });
});
