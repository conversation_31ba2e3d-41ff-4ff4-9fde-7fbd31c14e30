import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod, OrderStatus } from '@app/shared/database/entities';
import { Store } from '@app/shared/database/entities/store.entity';
import { WashingMachine } from '@app/shared/database/entities/washing-machine.entity';
import { MachineProgram } from '@app/shared/database/entities/machine-program.entity';
import {
  StoreRepositoryInterface,
  WashingMachineRepositoryInterface,
  MachineProgramRepositoryInterface,
  ProductRepositoryInterface,
} from '@app/shared/database/repositories';

describe('OrderController (e2e) - List Orders with Pagination', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  let accessToken: string;
  let userId: string;
  let productId: string;
  let storeRepository: StoreRepositoryInterface;
  let washingMachineRepository: WashingMachineRepositoryInterface;
  let machineProgramRepository: MachineProgramRepositoryInterface;
  let productRepository: ProductRepositoryInterface;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);

    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);

    storeRepository = app.get<StoreRepositoryInterface>(
      'StoreRepositoryInterface',
    );
    washingMachineRepository = app.get<WashingMachineRepositoryInterface>(
      'WashingMachineRepositoryInterface',
    );
    machineProgramRepository = app.get<MachineProgramRepositoryInterface>(
      'MachineProgramRepositoryInterface',
    );
    productRepository = app.get<ProductRepositoryInterface>(
      'ProductRepositoryInterface',
    );
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user and get access token
    const user = await userTestHelper.createTestUserInDb({ name: 'Alice Doe' });
    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create test product
    const product = await productTestHelper.createTestProduct();
    productId = product.id;
  });

  describe('GET /orders (with pagination)', () => {
    const endpoint = '/orders';

    describe('Success cases', () => {
      it('should get orders with pagination successfully', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(200);

        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('count');
        expect(Array.isArray(response.body.items)).toBe(true);
        expect(typeof response.body.count).toBe('number');
      });

      it('should return empty array when no orders exist', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(200);

        expect(response.body.items).toEqual([]);
        expect(response.body.count).toBe(0);
      });

      it('should return orders with payment transactions when orders exist', async () => {
        // First create an order
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        await orderTestHelper.createTestOrder(createPayload, accessToken);

        // Then get orders
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        expect(response.body.count).toBeGreaterThan(0);

        const order = response.body.items[0];
        expect(order).toHaveProperty('id');
        expect(order).toHaveProperty('userId');
        expect(order).toHaveProperty('code');
        expect(order).toHaveProperty('totalPoints');
        expect(order).toHaveProperty('totalAmount');
        expect(order).toHaveProperty('status');
        expect(order).toHaveProperty('paymentTransactions');
        expect(Array.isArray(order.paymentTransactions)).toBe(true);
      });

      it('should filter orders by userId', async () => {
        // Create an order for the current user
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };

        await orderTestHelper.createTestOrder(createPayload, accessToken);

        // Get orders filtered by userId
        const response = await request(app.getHttpServer())
          .get(`${endpoint}?userId=${userId}`)
          .expect(200);

        expect(response.body.items.length).toBeGreaterThan(0);
        expect(response.body.count).toBeGreaterThan(0);
      });

      it('should filter orders by userName (customer name)', async () => {
        const createPayload = {
          items: [
            {
              productId: productId,
              quantity: 1,
            },
          ],
          paymentMethod: PaymentMethod.POINTS,
        };
        await orderTestHelper.createTestOrder(createPayload, accessToken);

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?userName=Alice`)
          .expect(200);

        expect(response.body.count).toBeGreaterThan(0);
      });

      it('should filter orders by status', async () => {
        const { accessToken: token } =
          await orderTestHelper.createTestOrderWithStatus(OrderStatus.PAID);
        const response = await request(app.getHttpServer())
          .get(`${endpoint}?status=${OrderStatus.PAID}`)
          .set('Authorization', `Bearer ${token}`)
          .expect(200);
        expect(response.body.count).toBeGreaterThanOrEqual(1);
      });

      it('should filter orders by paymentMethod', async () => {
        const payload = {
          items: [{ productId, quantity: 1 }],
          paymentMethod: PaymentMethod.MOMOPAY,
        };
        await orderTestHelper.createTestOrder(payload, accessToken);

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?paymentMethod=${PaymentMethod.MOMOPAY}`)
          .expect(200);
        expect(response.body.count).toBeGreaterThanOrEqual(1);
      });

      it('should filter orders by storeName via product.storeId', async () => {
        const store: Store = await storeRepository.save(
          Object.assign(new Store(), {
            name: 'My Filter Store',
            code: 'STORE-001',
            address: '123 Main St',
            phone: '0123456789',
            manager_name: 'Manager',
            status: 'active',
          }),
        );

        const productWithStore = await productRepository.save(
          Object.assign(await productRepository.create(), {
            name: 'Store-linked Product',
            slug: 'store-linked-product',
            type: 'service',
            category: 'laundry_service',
            pricePoints: 1000,
            status: 'active',
            storeId: store.id,
          }),
        );

        const payload = {
          items: [{ productId: productWithStore.id, quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        };
        await orderTestHelper.createTestOrder(payload, accessToken);

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?storeName=My%20Filter%20Store`)
          .expect(200);
        expect(response.body.count).toBeGreaterThanOrEqual(1);
      });

      it('should filter orders by washingMachineName through machine program mapping', async () => {
        const store: Store = await storeRepository.save(
          Object.assign(new Store(), {
            name: 'WM Store',
            code: 'WM-STORE-001',
            address: '456 Another St',
            phone: '0987654321',
            manager_name: 'WM Manager',
            status: 'active',
          }),
        );

        const productWithProgram = await productRepository.save(
          Object.assign(await productRepository.create(), {
            name: 'Program Product',
            slug: 'program-product',
            type: 'service',
            category: 'laundry_service',
            pricePoints: 1000,
            status: 'active',
            storeId: store.id,
          }),
        );

        const machine: WashingMachine = await washingMachineRepository.save(
          Object.assign(new WashingMachine(), {
            code: 'M001',
            name: 'Washer Alpha',
            model: 'X1',
            type: 'combo',
            status: 'available',
            capacityKg: 7,
            storeId: store.id,
          }),
        );

        await machineProgramRepository.save(
          Object.assign(new MachineProgram(), {
            name: 'Quick 15',
            code: 'QUICK15',
            durationMinutes: 15,
            productId: productWithProgram.id,
            machineId: machine.id,
            isActive: true,
          }),
        );

        const payload = {
          items: [{ productId: productWithProgram.id, quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        };
        await orderTestHelper.createTestOrder(payload, accessToken);

        const response = await request(app.getHttpServer())
          .get(`${endpoint}?washingMachineName=Washer`)
          .expect(200);
        expect(response.body.count).toBeGreaterThanOrEqual(1);
      });

      it('should filter orders by date range', async () => {
        const payload = {
          items: [{ productId, quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        };
        await orderTestHelper.createTestOrder(payload, accessToken);

        const now = new Date();
        const past = new Date(
          now.getTime() - 24 * 60 * 60 * 1000,
        ).toISOString();
        const future = new Date(
          now.getTime() + 24 * 60 * 60 * 1000,
        ).toISOString();

        const responseInRange = await request(app.getHttpServer())
          .get(`${endpoint}?startDate=${past}&endDate=${future}`)
          .expect(200);
        expect(responseInRange.body.count).toBeGreaterThanOrEqual(1);

        const responseOutOfRange = await request(app.getHttpServer())
          .get(`${endpoint}?startDate=${future}&endDate=${future}`)
          .expect(200);
        expect(responseOutOfRange.body.count).toBe(0);
      });
    });

    describe('Error cases', () => {
      it('should return 422 for invalid pagination parameters', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}?skip=-1`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid limit parameter', async () => {
        const response = await request(app.getHttpServer())
          .get(`${endpoint}?limit=0`)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
