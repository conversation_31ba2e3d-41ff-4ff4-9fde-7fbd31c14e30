import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('PaymentController (e2e) - Cash only', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  const base = '/payment';
  let validOrderId: string;
  let userId: string;
  let accessToken: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create a real order using OrderTestHelper
    const { order, user } = await orderTestHelper.createTestOrderWithUser({
      items: [{ productId: '', quantity: 1 }],
      paymentMethod: PaymentMethod.POINTS,
    });

    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });
    validOrderId = order.id; // Use real order ID
  });

  describe('POST /payment (createPayment) - cash', () => {
    it('should create cash payment successfully', async () => {
      const payload = {
        gatewayType: 'cash',
        orderId: validOrderId,
        amount: 50000,
        currency: 'VND',
        description: 'Pay at counter',
        metadata: { userId },
      };

      const res = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(payload);
      expect(res.status).toBe(201);
      expect(res.body).toHaveProperty('success', true);
      expect(res.body).toHaveProperty('transactionId');
      expect(res.body).toHaveProperty('gatewayType', 'cash');
      // gatewayToken is not present for cash payments
    });

    it('should return 422 for unsupported gateway (enum validation)', async () => {
      const payload = {
        gatewayType: 'invalid-gateway',
        orderId: validOrderId,
        amount: 50000,
        currency: 'VND',
      } as any;

      const res = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(payload)
        .expect(422);

      expect(res.body).toHaveProperty('errors');
    });

    it('should return 422 for validation errors', async () => {
      const payload = {
        gatewayType: 'cash',
        orderId: 123, // invalid
      } as any;

      const res = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(payload)
        .expect(422);

      expect(res.body).toHaveProperty('errors');
    });
  });

  describe('POST /payment/status (checkPaymentStatus) - cash', () => {
    it('should return pending status initially', async () => {
      // first create a cash payment to get transactionId
      const create = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gatewayType: 'cash',
          orderId: validOrderId,
          amount: 20000,
          currency: 'VND',
          metadata: { userId },
        })
        .expect(201);

      const transactionId = create.body.transactionId;

      const res = await request(app.getHttpServer())
        .post(`${base}/status`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ gateway: 'cash', transactionId })
        .expect(200);

      expect(res.body).toHaveProperty('success', true);
      expect(res.body).toHaveProperty('status', 'pending');
      expect(res.body).toHaveProperty('transactionId', transactionId);
    });

    it('should return 422 for unsupported gateway (enum validation)', async () => {
      const res = await request(app.getHttpServer())
        .post(`${base}/status`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ gateway: 'invalid', transactionId: 'ANY' })
        .expect(422);

      expect(res.body).toHaveProperty('errors');
    });

    it('should return 422 for validation errors', async () => {
      const res = await request(app.getHttpServer())
        .post(`${base}/status`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({})
        .expect(422);

      expect(res.body).toHaveProperty('errors');
    });
  });

  describe('POST /payment/cash/confirm/:transactionId', () => {
    it('should confirm cash payment successfully', async () => {
      const create = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gatewayType: 'cash',
          orderId: validOrderId,
          amount: 30000,
          currency: 'VND',
          metadata: { userId },
        })
        .expect(201);

      const transactionId = create.body.transactionId;

      const res = await request(app.getHttpServer())
        .post(`${base}/cash/confirm/${transactionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ amount: 30000, staffId: 'STAFF_001', notes: 'ok' })
        .expect(200);

      expect(res.body).toHaveProperty('transactionId', transactionId);
      expect(res.body).toHaveProperty('status', 'success');
    });
  });

  describe('POST /payment/cash/cancel/:transactionId', () => {
    it('should cancel cash payment successfully', async () => {
      const create = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gatewayType: 'cash',
          orderId: validOrderId,
          amount: 15000,
          currency: 'VND',
          metadata: { userId },
        })
        .expect(201);

      const transactionId = create.body.transactionId;

      // Small delay to ensure transaction persistence by async event
      await new Promise((r) => setTimeout(r, 50));

      const res = await request(app.getHttpServer())
        .post(`${base}/cash/cancel/${transactionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ staffId: 'STAFF_002', reason: 'Customer left' })
        .expect(200);

      expect(res.body).toHaveProperty('success', true);
      expect(res.body).toHaveProperty('transactionId', transactionId);
      expect(res.body).toHaveProperty('status', 'cancelled');
    });
  });
});
