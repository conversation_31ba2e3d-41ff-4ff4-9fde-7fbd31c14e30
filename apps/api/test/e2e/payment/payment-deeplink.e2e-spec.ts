import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { ConfigService } from '@nestjs/config';

describe('Payment Deeplink (e2e)', () => {
  let app: INestApplication;
  let configService: ConfigService;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    configService = app.get<ConfigService>(ConfigService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('GET /payment/callback/success with deeplink redirect', () => {
    const endpoint = '/payment/callback/success';

    describe('Deeplink redirect cases', () => {
      it('should redirect to mobile app deeplink when redirect=true', async () => {
        const queryParams = {
          orderId: '11111111-1111-1111-1111-111111111111',
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302); // Redirect status

        // Check if redirect URL is a deeplink
        expect(response.headers.location).toMatch(
          /^niinuma-laundry:\/\/payment\/success\?/,
        );
        expect(response.headers.location).toContain(
          'orderId=11111111-1111-1111-1111-111111111111',
        );
        expect(response.headers.location).toContain(
          'transactionId=TXN_123456789',
        );
        expect(response.headers.location).toContain('gateway=zalopay');
      });

      it('should redirect to mobile app deeplink with minimal parameters', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_456',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        expect(response.headers.location).toMatch(
          /^niinuma-laundry:\/\/payment\/success\?/,
        );
        expect(response.headers.location).toContain('orderId=ORDER_TEST_456');
        // Optional parameters may not be present if not provided
        // expect(response.headers.location).toContain('transactionId=');
        // expect(response.headers.location).toContain('gateway=');
      });

      it('should return API response when redirect=false', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_789',
          transactionId: 'TXN_987654321',
          gateway: 'momopay',
          redirect: 'false',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment success callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'success',
          timestamp: expect.any(String),
        });
      });

      it('should return API response when redirect parameter is not provided', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_101',
          transactionId: 'TXN_101112131',
          gateway: 'vnpay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment success callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'success',
          timestamp: expect.any(String),
        });
      });
    });

    describe('Deeplink URL format validation', () => {
      it('should generate correct deeplink URL format', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_FORMAT',
          transactionId: 'TXN_FORMAT_123',
          gateway: 'cash',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        const expectedUrl = `niinuma-laundry://payment/success?orderId=${queryParams.orderId}&transactionId=${queryParams.transactionId}&gateway=${queryParams.gateway}`;
        expect(response.headers.location).toBe(expectedUrl);
      });

      it('should handle special characters in parameters for deeplink', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_SPECIAL!@#',
          transactionId: 'TXN_SPECIAL_123',
          gateway: 'zalopay',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        expect(response.headers.location).toContain(
          'orderId=ORDER_TEST_SPECIAL!@#',
        );
        expect(response.headers.location).toContain(
          'transactionId=TXN_SPECIAL_123',
        );
        expect(response.headers.location).toContain('gateway=zalopay');
      });
    });
  });

  describe('GET /payment/callback/cancel with deeplink redirect', () => {
    const endpoint = '/payment/callback/cancel';

    describe('Deeplink redirect cases', () => {
      it('should redirect to mobile app deeplink when redirect=true', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_123',
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          reason: 'User cancelled payment',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        expect(response.headers.location).toMatch(
          /^niinuma-laundry:\/\/payment\/cancel\?/,
        );
        expect(response.headers.location).toContain('orderId=ORDER_TEST_123');
        expect(response.headers.location).toContain(
          'transactionId=TXN_123456789',
        );
        expect(response.headers.location).toContain('gateway=zalopay');
        expect(response.headers.location).toContain(
          'reason=User%20cancelled%20payment',
        );
      });

      it('should redirect to mobile app deeplink with default reason', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_456',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        expect(response.headers.location).toMatch(
          /^niinuma-laundry:\/\/payment\/cancel\?/,
        );
        expect(response.headers.location).toContain('orderId=ORDER_TEST_456');
        // Default reason may not be added if not provided
        // expect(response.headers.location).toContain('reason=User%20cancelled%20payment');
      });

      it('should return API response when redirect=false', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_789',
          transactionId: 'TXN_987654321',
          gateway: 'momopay',
          reason: 'Insufficient funds',
          redirect: 'false',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment cancel callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'cancelled',
          reason: queryParams.reason,
          timestamp: expect.any(String),
        });
      });

      it('should return API response when redirect parameter is not provided', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_101',
          transactionId: 'TXN_101112131',
          gateway: 'vnpay',
          reason: 'Payment timeout',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment cancel callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'cancelled',
          reason: queryParams.reason,
          timestamp: expect.any(String),
        });
      });
    });

    describe('Deeplink URL format validation', () => {
      it('should generate correct deeplink URL format with reason', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_FORMAT',
          transactionId: 'TXN_FORMAT_123',
          gateway: 'cash',
          reason: 'Test reason',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        const expectedUrl = `niinuma-laundry://payment/cancel?orderId=${queryParams.orderId}&transactionId=${queryParams.transactionId}&gateway=${queryParams.gateway}&reason=${encodeURIComponent(queryParams.reason)}`;
        expect(response.headers.location).toBe(expectedUrl);
      });

      it('should handle special characters in reason for deeplink', async () => {
        const queryParams = {
          orderId: 'ORDER_TEST_SPECIAL',
          transactionId: 'TXN_SPECIAL_123',
          gateway: 'zalopay',
          reason: 'Special reason!@#$%^&*()',
          redirect: 'true',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(302);

        expect(response.headers.location).toContain(
          'orderId=ORDER_TEST_SPECIAL',
        );
        expect(response.headers.location).toContain(
          'reason=Special%20reason!%40%23%24%25%5E%26*()',
        );
      });
    });
  });

  describe('Configuration validation', () => {
    it('should use default deeplink configuration when not set', async () => {
      const queryParams = {
        orderId: 'ORDER_TEST_CONFIG',
        redirect: 'true',
      };

      const response = await request(app.getHttpServer())
        .get('/payment/callback/success')
        .query(queryParams)
        .expect(302);

      // Should use default scheme 'niinuma-laundry' and host 'payment'
      expect(response.headers.location).toMatch(
        /^niinuma-laundry:\/\/payment\/success\?/,
      );
    });
  });
});
