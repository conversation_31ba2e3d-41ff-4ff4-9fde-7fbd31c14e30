import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('Payment Balance Deduction (e2e)', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('Balance deduction when order is paid', () => {
    it('should deduct points from user balance when order is paid', async () => {
      // 1. Create test user and product
      const user = await userTestHelper.createTestUserInDb();
      const accessToken = authService.generateAccessToken({ userId: user.id });
      const product = await productTestHelper.createTestProduct({
        pricePoints: 1000,
      });

      // 2. Create order with points payment method
      const { order } = await orderTestHelper.createTestOrderWithExistingUser(
        {
          items: [{ productId: product.id, quantity: 2 }],
          paymentMethod: PaymentMethod.POINTS,
        },
        { id: user.id },
        accessToken,
      );

      expect(order.totalPoints).toBe(2000);

      // 3. Add points to user balance first
      const paymentRes = await request(app.getHttpServer())
        .post('/payment')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gatewayType: 'cash',
          orderId: order.id,
          amount: 2000000,
          currency: 'VND',
          metadata: { userId: user.id },
        })
        .expect(201);

      await request(app.getHttpServer())
        .post(`/payment/cash/confirm/${paymentRes.body.transactionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ amount: 2000000, staffId: 'STAFF_001' })
        .expect(200);

      await new Promise((r) => setTimeout(r, 100));

      // 4. Pay for order to trigger deduction
      const orderPaymentRes = await request(app.getHttpServer())
        .post('/payment')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gatewayType: 'cash',
          orderId: order.id,
          amount: 2000,
          currency: 'VND',
          metadata: { userId: user.id },
        })
        .expect(201);

      await request(app.getHttpServer())
        .post(`/payment/cash/confirm/${orderPaymentRes.body.transactionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ amount: 2000, staffId: 'STAFF_002' })
        .expect(200);

      await new Promise((r) => setTimeout(r, 100));

      // 5. Verify balance deduction (should be 0 after 2000 points deducted)
      // Note: This test assumes there's an endpoint to check user balance
      // You may need to add this endpoint or check database directly
    });
  });
});
