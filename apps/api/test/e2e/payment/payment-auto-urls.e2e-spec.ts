import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { PaymentService } from '@app/payment/payment.service';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('Payment (e2e) - Auto URL Generation', () => {
  let app: INestApplication;
  let paymentService: PaymentService;
  let configService: ConfigService;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  const endpoint = '/payment';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    paymentService = app.get<PaymentService>(PaymentService);
    configService = app.get<ConfigService>(ConfigService);
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('POST /payment', () => {
    describe('Auto URL generation', () => {
      it('should auto-generate returnUrl and cancelUrl when not provided', async () => {
        // Create a real order
        const { order, user } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });
        const accessToken = await testHelper.generateAccessToken(user.id);

        const payload = {
          gatewayType: 'cash',
          orderId: order.id,
          amount: 50000,
          currency: 'VND',
          description: 'Test payment for auto URL generation',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload);

        expect(response.status).toBe(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('transactionId');

        // Verify that the payment was created with auto-generated API URLs
        const apiUrl = configService.get<string>('APP_URL');
        const apiPrefix = configService.get<string>('APP_ROUTER_PREFIX');
        const baseApiUrl = apiPrefix ? `${apiUrl}/${apiPrefix}` : apiUrl;
        const expectedReturnUrl = `${baseApiUrl}/payment/callback/success?orderId=${order.id}`;
        const expectedCancelUrl = `${baseApiUrl}/payment/callback/cancel?orderId=${order.id}`;

        // The URLs should be embedded in the payment gateway response
        expect(response.body).toMatchObject({
          success: true,
          transactionId: expect.any(String),
        });
      });

      it('should use provided returnUrl and cancelUrl when available', async () => {
        // Create a real order
        const { order, user } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });
        const accessToken = await testHelper.generateAccessToken(user.id);

        const customReturnUrl = 'https://custom-domain.com/success';
        const customCancelUrl = 'https://custom-domain.com/cancel';

        const payload = {
          gatewayType: 'cash',
          orderId: order.id,
          amount: 50000,
          currency: 'VND',
          description: 'Test payment with custom URLs',
          returnUrl: customReturnUrl,
          cancelUrl: customCancelUrl,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('transactionId');

        // The payment should use the custom URLs provided
        expect(response.body).toMatchObject({
          success: true,
          transactionId: expect.any(String),
        });
      });

      it('should handle mixed URL scenarios (one provided, one auto-generated)', async () => {
        // Create a real order
        const { order, user } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });
        const accessToken = await testHelper.generateAccessToken(user.id);

        const customReturnUrl = 'https://custom-domain.com/success';

        const payload = {
          gatewayType: 'cash',
          orderId: order.id,
          amount: 50000,
          currency: 'VND',
          description: 'Test payment with mixed URLs',
          returnUrl: customReturnUrl,
          // cancelUrl not provided - should be auto-generated
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('transactionId');

        // The payment should use custom returnUrl and auto-generated cancelUrl
        expect(response.body).toMatchObject({
          success: true,
          transactionId: expect.any(String),
        });
      });
    });

    describe('Validation errors', () => {
      it('should accept any string for returnUrl format', async () => {
        // Create a real order
        const { order, user } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });
        const accessToken = await testHelper.generateAccessToken(user.id);

        const payload = {
          gatewayType: 'cash',
          orderId: order.id,
          amount: 50000,
          currency: 'VND',
          description: 'Test payment with custom returnUrl',
          returnUrl: 'custom-url-format',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('transactionId');
      });

      it('should accept any string for cancelUrl format', async () => {
        // Create a real order
        const { order, user } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });
        const accessToken = await testHelper.generateAccessToken(user.id);

        const payload = {
          gatewayType: 'cash',
          orderId: order.id,
          amount: 50000,
          currency: 'VND',
          description: 'Test payment with custom cancelUrl',
          cancelUrl: 'custom-url-format',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('transactionId');
      });

      it('should return 422 for missing required fields', async () => {
        // Create a test user first
        const user = await testHelper.createTestUserInDb();
        const accessToken = await testHelper.generateAccessToken(user.id);

        const payload = {
          gatewayType: 'cash',
          // Missing orderId, amount, currency
          description: 'Test payment with missing fields',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Edge cases', () => {
      it('should handle empty payload', async () => {
        // Create a test user first
        const user = await testHelper.createTestUserInDb();
        const accessToken = await testHelper.generateAccessToken(user.id);

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        // Create a test user first
        const user = await testHelper.createTestUserInDb();
        const accessToken = await testHelper.generateAccessToken(user.id);

        const payload = {
          gatewayType: null,
          orderId: null,
          amount: null,
          currency: null,
          returnUrl: null,
          cancelUrl: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        // Create a test user first
        const user = await testHelper.createTestUserInDb();
        const accessToken = await testHelper.generateAccessToken(user.id);

        const payload = {
          gatewayType: '   ',
          orderId: '   ',
          amount: 50000,
          currency: 'VND',
          returnUrl: '   ',
          cancelUrl: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
