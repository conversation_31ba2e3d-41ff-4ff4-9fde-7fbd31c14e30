import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';

describe('Payment Callback (e2e)', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('GET /payment/callback/success', () => {
    const endpoint = '/payment/callback/success';

    describe('Success cases', () => {
      it('should handle payment success callback with all parameters', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment success callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'success',
          timestamp: expect.any(String),
        });
      });

      it('should handle payment success callback with only required parameters', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment success callback processed successfully',
          orderId: queryParams.orderId,
          status: 'success',
          timestamp: expect.any(String),
        });
        expect(response.body.transactionId).toBeUndefined();
      });

      it('should handle payment success callback with special characters in orderId', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_987654321',
          gateway: 'momopay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment success callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'success',
          timestamp: expect.any(String),
        });
      });
    });

    describe('Error cases', () => {
      it('should return 400 for missing orderId', async () => {
        const queryParams = {
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });

      it('should return 400 for empty orderId', async () => {
        const queryParams = {
          orderId: '',
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });

      it('should return 400 for whitespace-only orderId', async () => {
        const queryParams = {
          orderId: '   ',
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });
    });

    describe('Edge cases', () => {
      it('should handle very long orderId', async () => {
        const longOrderId = 'A'.repeat(1000);
        const queryParams = {
          orderId: longOrderId,
          transactionId: 'TXN_123456789',
          gateway: 'vnpay',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          orderId: longOrderId,
          status: 'success',
        });
      });

      it('should handle very long transactionId', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const longTransactionId = 'TXN_' + 'A'.repeat(1000);
        const queryParams = {
          orderId: order.id,
          transactionId: longTransactionId,
          gateway: 'cash',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          transactionId: longTransactionId,
          status: 'success',
        });
      });
    });
  });

  describe('GET /payment/callback/cancel', () => {
    const endpoint = '/payment/callback/cancel';

    describe('Success cases', () => {
      it('should handle payment cancel callback with all parameters', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          reason: 'User cancelled payment',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment cancel callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'cancelled',
          reason: queryParams.reason,
          timestamp: expect.any(String),
        });
      });

      it('should handle payment cancel callback with only required parameters', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment cancel callback processed successfully',
          orderId: queryParams.orderId,
          status: 'cancelled',
          reason: 'User cancelled payment', // Default reason
          timestamp: expect.any(String),
        });
        expect(response.body.transactionId).toBeUndefined();
      });

      it('should handle payment cancel callback with custom reason', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_987654321',
          gateway: 'momopay',
          reason: 'Insufficient funds',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment cancel callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'cancelled',
          reason: queryParams.reason,
          timestamp: expect.any(String),
        });
      });

      it('should handle payment cancel callback with special characters in reason', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_SPECIAL_123',
          gateway: 'vnpay',
          reason: 'User cancelled payment!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Payment cancel callback processed successfully',
          orderId: queryParams.orderId,
          transactionId: queryParams.transactionId,
          status: 'cancelled',
          reason: queryParams.reason,
          timestamp: expect.any(String),
        });
      });
    });

    describe('Error cases', () => {
      it('should return 400 for missing orderId', async () => {
        const queryParams = {
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          reason: 'User cancelled payment',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });

      it('should return 400 for empty orderId', async () => {
        const queryParams = {
          orderId: '',
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          reason: 'User cancelled payment',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });

      it('should return 400 for whitespace-only orderId', async () => {
        const queryParams = {
          orderId: '   ',
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          reason: 'User cancelled payment',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(400);

        expect(response.body).toHaveProperty('code', 'BAD_REQUEST');
      });
    });

    describe('Edge cases', () => {
      it('should handle very long orderId', async () => {
        const longOrderId = 'A'.repeat(1000);
        const queryParams = {
          orderId: longOrderId,
          transactionId: 'TXN_123456789',
          gateway: 'vnpay',
          reason: 'User cancelled payment',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          orderId: longOrderId,
          status: 'cancelled',
        });
      });

      it('should handle very long reason', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const longReason = 'A'.repeat(1000);
        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_123456789',
          gateway: 'cash',
          reason: longReason,
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          reason: longReason,
          status: 'cancelled',
        });
      });

      it('should handle empty reason (should use default)', async () => {
        // Create a real order
        const { order } = await orderTestHelper.createTestOrderWithUser({
          items: [{ productId: '', quantity: 1 }],
          paymentMethod: PaymentMethod.POINTS,
        });

        const queryParams = {
          orderId: order.id,
          transactionId: 'TXN_123456789',
          gateway: 'zalopay',
          reason: '',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(queryParams)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          reason: 'User cancelled payment', // Default reason
          status: 'cancelled',
        });
      });
    });
  });
});
