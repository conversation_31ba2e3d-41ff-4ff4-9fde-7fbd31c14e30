import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('Payment (e2e) - Update user points', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;

  const buildEndpoint = (userId: string) => `/payment/user/${userId}/points`;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    userTestHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('PATCH /payment/user/:userId/points', () => {
    describe('Success cases', () => {
      it('should update user points successfully with admin token', async () => {
        const { user: admin, accessToken: adminToken } =
          await userTestHelper.createAdminUserWithAuth();
        const { user: customer } =
          await userTestHelper.createAuthenticatedUser();

        const payload = {
          points: 500,
          reason: 'Adjustment for testing',
        };

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(customer.id))
          .set('Authorization', `Bearer ${adminToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });
    });

    describe('Validation errors (422)', () => {
      it('should return 422 for missing required fields', async () => {
        const { accessToken: adminToken } =
          await userTestHelper.createAdminUserWithAuth();
        const { user: customer } =
          await userTestHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(customer.id))
          .set('Authorization', `Bearer ${adminToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid points (negative)', async () => {
        const { accessToken: adminToken } =
          await userTestHelper.createAdminUserWithAuth();
        const { user: customer } =
          await userTestHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(customer.id))
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ points: -10 })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid points (not a number)', async () => {
        const { accessToken: adminToken } =
          await userTestHelper.createAdminUserWithAuth();
        const { user: customer } =
          await userTestHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(customer.id))
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ points: 'abc' })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for whitespace-only reason (if provided)', async () => {
        const { accessToken: adminToken } =
          await userTestHelper.createAdminUserWithAuth();
        const { user: customer } =
          await userTestHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(customer.id))
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ points: 100, reason: '   ' })
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Authorization errors', () => {
      it('should return 401 when no Authorization header provided', async () => {
        const { user: customer } =
          await userTestHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(customer.id))
          .send({ points: 50 })
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 when using non-admin token', async () => {
        const { user: customer, accessToken } =
          await userTestHelper.createAuthenticatedUser();
        const { user: target } = await userTestHelper.createAuthenticatedUser();

        const response = await request(app.getHttpServer())
          .patch(buildEndpoint(target.id))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ points: 100 })
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });
    });
  });
});
