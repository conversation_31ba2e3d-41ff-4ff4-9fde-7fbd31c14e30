import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import {
  UserTestHelper,
  ProductTestHelper,
  OrderTestHelper,
} from '../../helpers';
import { PaymentMethod } from '@app/shared/database/entities';
import { PaymentGatewayType, CURRENCY_TYPE } from '@app/payment/constants';

describe('PaymentController (e2e) - VNPay Gateway', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let userTestHelper: UserTestHelper;
  let productTestHelper: ProductTestHelper;
  let orderTestHelper: OrderTestHelper;
  const base = '/payment';
  let validOrderId: string;
  let userId: string;
  let accessToken: string;

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    userTestHelper = new UserTestHelper(app, authService, userService);
    productTestHelper = new ProductTestHelper(app);
    orderTestHelper = new OrderTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create a real order using OrderTestHelper
    const { order, user } = await orderTestHelper.createTestOrderWithUser({
      items: [{ productId: '', quantity: 1 }],
      paymentMethod: PaymentMethod.POINTS,
    });

    userId = user.id;
    accessToken = authService.generateAccessToken({ userId: user.id });
    validOrderId = order.id; // Use real order ID
  });

  describe('POST /payment (createPayment) - VNPay', () => {
    describe('Success cases', () => {
      it('should create VNPay payment successfully with basic data', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: 'Payment for order via VNPay',
          returnUrl: 'https://example.com/payment/success',
          cancelUrl: 'https://example.com/payment/cancel',
          customerInfo: {
            name: 'Nguyen Van A',
            email: '<EMAIL>',
            phone: '**********',
          },
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
        expect(response.body).toHaveProperty('transactionId');
        expect(response.body).toHaveProperty(
          'gatewayType',
          PaymentGatewayType.VNPAY,
        );
        expect(response.body).toHaveProperty('amount', 50000);
        expect(response.body).toHaveProperty('currency', CURRENCY_TYPE.VND);
        expect(response.body).toHaveProperty('expiresAt');

        // Verify payment URL format
        expect(response.body.paymentUrl).toContain('sandbox.vnpayment.vn');
        expect(response.body.paymentUrl).toContain('vnp_Amount=5000000'); // amount * 100
        expect(response.body.paymentUrl).toContain('vnp_Command=pay');
        expect(response.body.paymentUrl).toContain('vnp_SecureHash=');
      });

      it('should create VNPay payment with bank code specified', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 100000,
          currency: CURRENCY_TYPE.VND,
          description: 'Payment with NCB bank',
          returnUrl: 'https://example.com/payment/success',
          cancelUrl: 'https://example.com/payment/cancel',
          metadata: {
            bankCode: 'NCB',
          },
          customerInfo: {
            name: 'Nguyen Van B',
            email: '<EMAIL>',
            phone: '**********',
          },
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
        expect(response.body).toHaveProperty('transactionId');
        expect(response.body).toHaveProperty(
          'gatewayType',
          PaymentGatewayType.VNPAY,
        );

        // Verify bank code is included in URL
        expect(response.body.paymentUrl).toContain('vnp_BankCode=NCB');
        expect(response.body.paymentUrl).toContain('vnp_Amount=********'); // amount * 100
      });

      it('should create VNPay payment with large amount', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 1000000, // 1,000,000 VND
          currency: CURRENCY_TYPE.VND,
          description: 'Large payment via VNPay',
          returnUrl: 'https://example.com/payment/success',
          cancelUrl: 'https://example.com/payment/cancel',
          customerInfo: {
            name: 'Nguyen Van C',
            email: '<EMAIL>',
            phone: '**********',
          },
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
        expect(response.body).toHaveProperty('transactionId');
        expect(response.body).toHaveProperty('amount', 1000000);
        expect(response.body.paymentUrl).toContain('vnp_Amount=********0'); // amount * 100
      });

      it('should create VNPay payment without optional fields', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 25000,
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
        expect(response.body).toHaveProperty('transactionId');
        expect(response.body).toHaveProperty(
          'gatewayType',
          PaymentGatewayType.VNPAY,
        );
        expect(response.body).toHaveProperty('amount', 25000);
      });
    });

    describe('Error cases', () => {
      it('should return 422 for invalid gateway type', async () => {
        const payload = {
          gatewayType: 'invalid-gateway',
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
        } as any;

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid amount (negative)', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: -1000,
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid amount (zero)', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 0,
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid currency', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: 'USD', // Invalid currency
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid orderId type', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: 123, // Invalid type
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
        } as any;

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing required fields', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          // Missing orderId, amount, currency
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid customerInfo email', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          customerInfo: {
            name: 'Test User',
            email: 'invalid-email',
            phone: '**********',
          },
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 401 for missing authorization', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid token', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', 'Bearer invalid-token')
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle minimum valid amount', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 1, // Minimum amount
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('amount', 1);
        expect(response.body.paymentUrl).toContain('vnp_Amount=100'); // 1 * 100
      });

      it('should handle maximum reasonable amount', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 999999999, // Large amount
          currency: CURRENCY_TYPE.VND,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('amount', 999999999);
        expect(response.body.paymentUrl).toContain('vnp_Amount=99999999900'); // amount * 100
      });

      it('should handle special characters in description', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: 'Payment with special chars: !@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
      });

      it('should handle empty description', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: '',
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
      });

      it('should handle null values in optional fields', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: null,
          returnUrl: null,
          cancelUrl: null,
          customerInfo: null,
          metadata: null,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
      });

      it('should handle undefined values in optional fields', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: undefined,
          returnUrl: undefined,
          cancelUrl: undefined,
          customerInfo: undefined,
          metadata: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: '   ',
          returnUrl: '   ',
          cancelUrl: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
      });

      it('should handle maximum length values', async () => {
        const longString = 'a'.repeat(255);
        const payload = {
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: longString,
          returnUrl: `https://example.com/${longString}`,
          cancelUrl: `https://example.com/${longString}`,
        };

        const response = await request(app.getHttpServer())
          .post(base)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('paymentUrl');
      });
    });
  });

  describe('POST /payment/status (checkPaymentStatus) - VNPay', () => {
    it('should check VNPay payment status successfully', async () => {
      // First create a VNPay payment to get transactionId
      const createResponse = await request(app.getHttpServer())
        .post(base)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gatewayType: PaymentGatewayType.VNPAY,
          orderId: validOrderId,
          amount: 50000,
          currency: CURRENCY_TYPE.VND,
          description: 'Test payment for status check',
        })
        .expect(201);

      const transactionId = createResponse.body.transactionId;

      // Check payment status
      const statusResponse = await request(app.getHttpServer())
        .post(`${base}/status`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gateway: PaymentGatewayType.VNPAY,
          transactionId,
        })
        .expect(200);

      expect(statusResponse.body).toHaveProperty('success', true);
      expect(statusResponse.body).toHaveProperty(
        'transactionId',
        transactionId,
      );
      expect(statusResponse.body).toHaveProperty('status');
      expect(statusResponse.body).toHaveProperty('amount');
      expect(statusResponse.body).toHaveProperty('currency');
    });

    it('should return 422 for invalid gateway in status check', async () => {
      const response = await request(app.getHttpServer())
        .post(`${base}/status`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gateway: 'invalid-gateway',
          transactionId: 'VNPAY_123456789',
        })
        .expect(422);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 422 for missing transactionId in status check', async () => {
      const response = await request(app.getHttpServer())
        .post(`${base}/status`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          gateway: PaymentGatewayType.VNPAY,
          // Missing transactionId
        })
        .expect(422);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 401 for missing authorization in status check', async () => {
      const response = await request(app.getHttpServer())
        .post(`${base}/status`)
        .send({
          gateway: PaymentGatewayType.VNPAY,
          transactionId: 'VNPAY_123456789',
        })
        .expect(401);

      expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
    });
  });
});
