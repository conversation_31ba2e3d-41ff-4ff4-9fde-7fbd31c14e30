import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import {
  SYSTEM_SETTING_KEY,
  SYSTEM_SETTING_KEY_SCHEMA,
} from '@app/system/const/system.constant';

describe('SystemController (e2e) - Get Setting Keys', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/system/keys';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('GET /system/keys', () => {
    describe('Success cases', () => {
      it('should return available setting keys and schemas', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(200);

        expect(response.body).toHaveProperty('keys');
        expect(response.body).toHaveProperty('schemas');

        // Verify keys structure
        expect(response.body.keys).toEqual(SYSTEM_SETTING_KEY);

        // Verify schemas structure
        expect(response.body.schemas).toEqual(SYSTEM_SETTING_KEY_SCHEMA);

        // Verify specific keys exist
        expect(response.body.keys).toHaveProperty('BONUS_POINT_RATIO');
        expect(response.body.keys).toHaveProperty('EXCHANGE_RATE_VND');
        expect(response.body.keys).toHaveProperty('COMPANY_NAME');
        expect(response.body.keys).toHaveProperty('COMPANY_EMAIL');
        expect(response.body.keys).toHaveProperty('COMPANY_PHONE');
        expect(response.body.keys).toHaveProperty('TECHNICAL_PHONE');
        expect(response.body.keys).toHaveProperty('COMPANY_ADDRESS');
        expect(response.body.keys).toHaveProperty('WORKING_HOURS');
        expect(response.body.keys).toHaveProperty('COMPANY_LOGO');

        // Verify specific schemas exist
        expect(response.body.schemas).toHaveProperty(
          SYSTEM_SETTING_KEY.BONUS_POINT_RATIO,
        );
        expect(response.body.schemas).toHaveProperty(
          SYSTEM_SETTING_KEY.EXCHANGE_RATE_VND,
        );
        expect(response.body.schemas).toHaveProperty(
          SYSTEM_SETTING_KEY.COMPANY_NAME,
        );
        expect(response.body.schemas).toHaveProperty(
          SYSTEM_SETTING_KEY.COMPANY_EMAIL,
        );
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for non-admin users', async () => {
        const user = await testHelper.createTestUser();
        const accessToken = authService.generateAccessToken({
          userId: user.id,
        });

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });
    });
  });
});
