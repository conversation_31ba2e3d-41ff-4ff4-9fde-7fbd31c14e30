import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { SystemService } from '@app/system/system.service';
import { FaqService } from '@app/system/services';

describe('SystemController (e2e) - Delete FAQ', () => {
  let app: INestApplication;
  let systemService: SystemService;
  let faqService: FaqService;
  const endpoint = '/faq';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    systemService = app.get<SystemService>(SystemService);
    faqService = app.get<FaqService>(FaqService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('DELETE /system/faq/:id', () => {
    describe('Success cases', () => {
      it('should delete FAQ successfully', async () => {
        // First create a FAQ
        const createPayload = {
          type: 'faq',
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Câu hỏi test',
              content: 'Nội dung test',
            },
            {
              language: 'en',
              title: 'Test question',
              content: 'Test content',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        // Then delete it
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${createdFaq.body.id}`)
          .expect(200);

        expect(response.body).toBe(true);

        // Verify it's deleted by trying to get it
        await request(app.getHttpServer())
          .get(`${endpoint}`)
          .expect(200)
          .then((res) => {
            const faqs = res.body;
            const deletedFaq = faqs.find(
              (faq: any) => faq.id === createdFaq.body.id,
            );
            expect(deletedFaq).toBeUndefined();
          });
      });
    });

    describe('Error cases', () => {
      it('should return 404 for non-existent FAQ', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/non-existent-id`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'FAQ_NOT_FOUND');
      });

      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/test-id`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle invalid UUID format', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/invalid-uuid-format`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'FAQ_NOT_FOUND');
      });

      it('should handle empty ID parameter', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/`)
          .expect(404);

        expect(response.status).toBe(404);
      });

      it('should handle very long ID parameter', async () => {
        const longId = 'a'.repeat(1000);
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${longId}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'FAQ_NOT_FOUND');
      });

      it('should handle special characters in ID parameter', async () => {
        const specialId = 'test-id!@#$%^&*()';
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${specialId}`)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'FAQ_NOT_FOUND');
      });
    });
  });
});
