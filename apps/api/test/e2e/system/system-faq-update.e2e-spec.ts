import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { SystemService } from '@app/system/system.service';
import { FaqService } from '@app/system/services';

describe('SystemController (e2e) - Update FAQ', () => {
  let app: INestApplication;
  let systemService: SystemService;
  let faqService: FaqService;
  const endpoint = '/faq';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    systemService = app.get<SystemService>(SystemService);
    faqService = app.get<FaqService>(FaqService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('PUT /system/faq/:id', () => {
    describe('Success cases', () => {
      it('should update FAQ successfully', async () => {
        // First create a FAQ
        const createPayload = {
          type: 'faq',
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Câu hỏi gốc',
              content: 'Nội dung gốc',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        // Then update it
        const updatePayload = {
          type: 'help',
          display_order: 2,
          is_active: false,
          items: [
            {
              language: 'vi',
              title: 'Câu hỏi đã cập nhật',
              content: 'Nội dung đã cập nhật',
            },
            {
              language: 'en',
              title: 'Updated question',
              content: 'Updated content',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(200);

        expect(response.body).toHaveProperty('id', createdFaq.body.id);
        expect(response.body).toHaveProperty('children');
        expect(response.body.children).toHaveLength(2);
        expect(response.body.type).toBe('help');
        expect(response.body.display_order).toBe(2);
        expect(response.body.is_active).toBe(false);
      });
    });

    describe('Error cases', () => {
      it('should return 404 for non-existent FAQ', async () => {
        const payload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/non-existent-id`)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty('code', 'FAQ_NOT_FOUND');
      });

      it('should return 422 for validation errors', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        // Then try to update with invalid data
        const updatePayload = {
          display_order: 0, // Invalid: minimum is 1
          items: [
            {
              language: 'invalid', // Invalid language
              title: '', // Empty title
              content: '', // Empty content
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 401 for unauthorized access', async () => {
        const payload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/test-id`)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .put(`${endpoint}/test-id`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        const updatePayload = {
          display_order: null,
          items: null,
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        const updatePayload = {
          display_order: undefined,
          items: undefined,
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        const updatePayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: '   ',
              content: '   ',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        const updatePayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'a'.repeat(501), // Exceeds max length 500
              content: 'Test content',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        // First create a FAQ
        const createPayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const createdFaq = await request(app.getHttpServer())
          .post(endpoint)
          .send(createPayload)
          .expect(201);

        const updatePayload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Updated!@#$%^&*()',
              content: 'Updated content with special chars !@#$%^&*()',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(`${endpoint}/${createdFaq.body.id}`)
          .send(updatePayload)
          .expect(200); // Should accept special characters

        expect(response.body).toHaveProperty('id');
      });
    });
  });
});
