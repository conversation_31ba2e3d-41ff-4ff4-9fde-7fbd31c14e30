import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('SystemController (e2e) - Update System Configurations', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/system';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('POST /system', () => {
    describe('Success cases', () => {
      it('should update multiple settings successfully', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const payload = {
          settings: [
            {
              key: 'payment.bonus_point_ratio',
              value: 0.035,
              type: 'number',
            },
            {
              key: 'app.name',
              value: 'Updated App Name',
              type: 'string',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .send(payload)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBe(2);

        // Check first setting
        const firstSetting = response.body.find(
          (s: any) => s.key === 'payment.bonus_point_ratio',
        );
        expect(firstSetting).toBeDefined();
        expect(firstSetting.value).toBe('0.035');
        expect(firstSetting.type).toBe('number');

        // Check second setting
        const secondSetting = response.body.find(
          (s: any) => s.key === 'app.name',
        );
        expect(secondSetting).toBeDefined();
        expect(secondSetting.value).toBe('"Updated App Name"');
        expect(secondSetting.type).toBe('string');
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const payload = {
          settings: [
            {
              key: 'payment.bonus_point_ratio',
              value: 0.02,
              type: 'number',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for non-admin user', async () => {
        const regularUser = await testHelper.createTestUserInDb();
        const token = await testHelper.generateAccessToken(regularUser.id);

        const payload = {
          settings: [
            {
              key: 'payment.bonus_point_ratio',
              value: 0.02,
              type: 'number',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .send(payload)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });

      it('should return 422 for validation errors - empty settings array', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const payload = {
          settings: [],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
