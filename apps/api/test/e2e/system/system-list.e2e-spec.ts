import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('SystemController (e2e) - List System Configurations', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  const endpoint = '/system';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('GET /system', () => {
    describe('Success cases', () => {
      it('should list all configurations successfully', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);

        // Check structure of first item
        if (response.body.length > 0) {
          const firstItem = response.body[0];
          expect(firstItem).toHaveProperty('id');
          expect(firstItem).toHaveProperty('key');
          expect(firstItem).toHaveProperty('value');
          expect(firstItem).toHaveProperty('type');
          expect(firstItem).toHaveProperty('category');
          expect(firstItem).toHaveProperty('description');
          expect(firstItem).toHaveProperty('is_active');
          expect(firstItem).toHaveProperty('created_at');
          expect(firstItem).toHaveProperty('updated_at');
        }
      });

      it('should filter by keys successfully', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ keys: 'payment.bonus_point_ratio' })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBe(1);
        expect(response.body[0].key).toBe('payment.bonus_point_ratio');
      });

      it('should filter by category successfully', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ category: 'payment' })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);

        // All items should have payment category
        response.body.forEach((item: any) => {
          expect(item.category).toBe('payment');
        });
      });

      it('should filter by active status successfully', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ is_active: true })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);

        // All items should be active
        response.body.forEach((item: any) => {
          expect(item.is_active).toBe(true);
        });
      });

      it('should combine multiple filters successfully', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({
            category: 'payment',
            is_active: true,
          })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);

        // All items should match both filters
        response.body.forEach((item: any) => {
          expect(item.category).toBe('payment');
          expect(item.is_active).toBe(true);
        });
      });

      it('should return empty array when no matches found', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ 'keys[]': 'non.existent.key' })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBe(0);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 403 for non-admin user', async () => {
        const regularUser = await testHelper.createTestUserInDb();
        const token = await testHelper.generateAccessToken(regularUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('Authorization', `Bearer ${token}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle empty keys filter', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ 'keys[]': '' })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBe(0);
      });

      it('should handle multiple keys filter', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({
            'keys[]': [
              'payment.bonus_point_ratio',
              'payment.exchange_rate.vnd',
            ],
          })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);

        const keys = response.body.map((item: any) => item.key);
        expect(keys).toContain('payment.bonus_point_ratio');
        expect(keys).toContain('payment.exchange_rate.vnd');
      });

      it('should handle whitespace in keys filter', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ 'keys[]': ' payment.bonus_point_ratio ' })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBe(1);
        expect(response.body[0].key).toBe('payment.bonus_point_ratio');
      });

      it('should handle boolean string values for is_active', async () => {
        const adminUser = await testHelper.createAdminUserInDb();
        const token = await testHelper.generateAccessToken(adminUser.id);

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query({ is_active: 'true' })
          .set('Authorization', `Bearer ${token}`)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);

        response.body.forEach((item: any) => {
          expect(item.is_active).toBe(true);
        });
      });
    });
  });
});
