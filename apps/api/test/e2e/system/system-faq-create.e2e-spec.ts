import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { SystemService } from '@app/system/system.service';
import { FaqService } from '@app/system/services';

describe('SystemController (e2e) - Create FAQ', () => {
  let app: INestApplication;
  let systemService: SystemService;
  let faqService: FaqService;
  const endpoint = '/faq';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    systemService = app.get<SystemService>(SystemService);
    faqService = app.get<FaqService>(FaqService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('POST /system/faq', () => {
    describe('Success cases', () => {
      it('should create FAQ successfully', async () => {
        const payload = {
          type: 'faq',
          slug: 'test-faq',
          display_order: 1,
          is_active: true,
          is_expanded: false,
          items: [
            {
              language: 'vi',
              title: 'Faq question title',
              content: 'Faq questio n content',
            },
            {
              language: 'en',
              title: 'Faq question title',
              content: 'Faq question content',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('children');
        expect(response.body.children).toHaveLength(2);
      });
    });

    describe('Error cases', () => {
      it('should return 422 for validation errors', async () => {
        const payload = {
          display_order: 0, // Invalid: minimum is 1
          items: [
            {
              language: 'invalid', // Invalid language
              title: '', // Empty title
              content: '', // Empty content
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 401 for unauthorized access', async () => {
        const payload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test',
              content: 'Test',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        const payload = {
          display_order: null,
          items: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        const payload = {
          display_order: undefined,
          items: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: '   ',
              content: '   ',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'a'.repeat(501), // Exceeds max length 500
              content: 'Test content',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          display_order: 1,
          items: [
            {
              language: 'vi',
              title: 'Test!@#$%^&*()',
              content: 'Content with special chars !@#$%^&*()',
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(201); // Should accept special characters

        expect(response.body).toHaveProperty('id');
      });
    });
  });
});
