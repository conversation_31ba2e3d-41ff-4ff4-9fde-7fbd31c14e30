import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { SystemService } from '@app/system/system.service';
import { FaqService } from '@app/system/services';

describe('SystemController (e2e) - List FAQ', () => {
  let app: INestApplication;
  let systemService: SystemService;
  let faqService: FaqService;
  const endpoint = '/faq';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    systemService = app.get<SystemService>(SystemService);
    faqService = app.get<FaqService>(FaqService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('GET /system/faq', () => {
    describe('Success cases', () => {
      it('should list FAQs successfully', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
      });

      it('should list FAQs with filters', async () => {
        const query = {
          language: 'vi',
          type: 'faq',
          is_active: true,
          is_expanded: false,
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(query)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
      });
    });

    describe('Error cases', () => {
      it('should return 401 for unauthorized access', async () => {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle invalid query parameters', async () => {
        const query = {
          language: 'invalid_language',
          type: 'invalid_type',
          is_active: 'invalid_boolean',
        };

        const response = await request(app.getHttpServer())
          .get(endpoint)
          .query(query)
          .expect(200); // Should still return 200 with empty results

        expect(Array.isArray(response.body)).toBe(true);
      });
    });
  });
});
