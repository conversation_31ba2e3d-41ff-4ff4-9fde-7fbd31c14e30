import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('WashingMachineController (e2e) - Create Washing Machine', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let accessToken: string;
  const endpoint = '/washing-machines';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    // REQUIRED: Always clear database before each test
    await global.clearTestDatabase();
    const user = await testHelper.createTestUserInDb();
    accessToken = authService.generateAccessToken({ userId: user.id });
  });

  describe('POST /washing-machines', () => {
    describe('Success cases', () => {
      it('should create washing machine successfully with valid data (auto store)', async () => {
        const payload = {
          code: 'A01',
          name: 'Washing machine Samsung',
          model: 'WD172CS',
          capacityKg: 11,
          machinePrograms: [
            {
              name: 'Wash and dry standard',
              code: 'WASH_AND_DRY_STANDARD',
              price: 10000,
              settings: {
                duration: 60,
                temperature: 40,
                spinSpeed: 1000,
                weight: 11,
              },
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('code', 'A01');
        expect(response.body).toHaveProperty('name', 'Washing machine Samsung');
        expect(response.body).toHaveProperty('model', 'WD172CS');
        expect(response.body).toHaveProperty('storeId');
        expect(response.body).toHaveProperty('capacityKg', 11);
        expect(response.body).toHaveProperty('status', 'available');
      });

      it('should create washing machine successfully with optional fields provided', async () => {
        const payload = {
          code: 'A02',
          name: 'Washing machine LG',
          model: 'LG-XYZ',
          type: 'combo',
          capacityKg: 13,
          machinePrograms: [
            {
              name: 'Quick wash',
              code: 'QUICK_WASH',
              price: 8000,
              settings: {
                duration: 30,
                temperature: 30,
                spinSpeed: 800,
                weight: 8,
              },
              features: {
                ecoMode: true,
              },
            },
          ],
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(201);

        expect(response.body).toHaveProperty('code', 'A02');
        expect(response.body).toHaveProperty('capacityKg', 13);
        expect(response.body).toHaveProperty('status', 'available');
      });
    });

    describe('Error cases', () => {
      it('should return 409 for existing machine code', async () => {
        const basePayload = {
          code: 'B01',
          name: 'Machine 1',
          model: 'MDL-1',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 9000,
              settings: { duration: 45 },
            },
          ],
        };

        // First creation succeeds
        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(basePayload)
          .expect(201);

        // Second creation with same code should conflict
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ ...basePayload, name: 'Machine 2' })
          .expect(409);

        expect(response.body).toHaveProperty('code', 'MACHINE_CODE_EXISTS');
      });

      it('should return 422 for validation errors', async () => {
        const payload = {
          code: 'A 01', // Space not allowed by regex
          name: '', // Required
          model: '', // Required
          capacityKg: 0, // Min 1
          machinePrograms: [], // ArrayMinSize(1)
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid program fields', async () => {
        const payload = {
          code: 'C01',
          name: 'Machine Invalid Program',
          model: 'MDL-INV',
          capacityKg: 12,
          machinePrograms: [
            {
              name: '', // Required
              code: '', // Required
              price: 0, // Min 1
              settings: { duration: 0 }, // Duration 0 will still allow, but price invalid already
            },
          ],
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .set('Authorization', `Bearer ${accessToken}`)
          .send('invalid json')
          .expect(400);

        expect(response.text).toBeDefined();
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle null values', async () => {
        const payload = {
          code: null,
          name: null,
          model: null,
          capacityKg: null,
          machinePrograms: null,
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle undefined values', async () => {
        const payload = {
          code: undefined,
          name: undefined,
          model: undefined,
          capacityKg: undefined,
          machinePrograms: undefined,
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          code: '   ',
          name: '   ',
          model: '   ',
          capacityKg: 11,
          machinePrograms: [
            {
              name: '   ',
              code: '   ',
              price: 10000,
              settings: { duration: 60 },
            },
          ],
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          code: 'A'.repeat(255), // Exceeds max length 20
          name: 'A'.repeat(256), // Exceeds max length 255
          model: 'M'.repeat(256),
          capacityKg: 11,
          machinePrograms: [
            {
              name: 'N'.repeat(0),
              code: 'C'.repeat(0),
              price: 10000,
              settings: { duration: 60 },
            },
          ],
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          code: 'A01!@#', // Not allowed by regex
          name: 'Washing Machine!@#',
          model: 'MDL!@#',
          capacityKg: 11,
          machinePrograms: [
            {
              name: 'Standard!@#',
              code: 'STD!@#',
              price: 10000,
              settings: { duration: 60 },
            },
          ],
        } as any;

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
