import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { UserRole, UserStatus } from '@app/shared/database/entities';

describe('WashingMachineController (e2e) - Delete Washing Machine', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let accessToken: string;
  const endpoint = '/washing-machines';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    // REQUIRED: Always clear database before each test
    await global.clearTestDatabase();
    const user = await testHelper.createTestUserInDb();
    accessToken = authService.generateAccessToken({ userId: user.id });
  });

  describe('DELETE /washing-machines/:idOrCode', () => {
    describe('Success cases', () => {
      it('should delete washing machine successfully by ID', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM001',
          name: 'Test Machine 1',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toBe(true);
      });

      it('should delete washing machine successfully by code', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM002',
          name: 'Test Machine 2',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/WM002`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toBe(true);
      });

      it('should delete washing machine and clean up related data', async () => {
        // Create a washing machine with programs first
        const createPayload = {
          code: 'WM003',
          name: 'Test Machine 3',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
            {
              name: 'Quick',
              code: 'QUICK',
              price: 5000,
              settings: { duration: 20 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toBe(true);

        // Verify washing machine is soft deleted
        // Note: We can't easily verify this without direct DB access in this test structure
        // The business logic ensures washing machine is soft deleted and related data is cleaned up
      });
    });

    describe('Error cases - Authentication & Authorization', () => {
      it('should return 401 for missing access token', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM004',
          name: 'Test Machine 4',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 401 for invalid access token', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM005',
          name: 'Test Machine 5',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .set('Authorization', 'Bearer invalid_token')
          .expect(401);

        expect(response.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      // Note: Endpoint currently doesn't have role guard, so any authenticated user can delete
      // These tests are commented out until role guard is added
      /*
      it('should return 403 for non-admin user', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM006',
          name: 'Test Machine 6',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });

      it('should return 403 for staff user', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM007',
          name: 'Test Machine 7',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(403);

        expect(response.body).toHaveProperty('code', 'FORBIDDEN');
      });
      */
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent washing machine by ID', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/00000000-0000-0000-0000-000000000000`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });

      it('should return 404 for non-existent washing machine by code', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/NONEXISTENT`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });

      it('should return 409 for machine currently in use', async () => {
        // Create a washing machine first
        const createPayload = {
          code: 'WM008',
          name: 'Test Machine 8',
          model: 'Test Model',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const washingMachineId = created.body.id;

        // Create an active service session to simulate machine in use
        const sessionUser = await testHelper.createTestUserInDb();
        const serviceSessionRepository = app.get(
          'ServiceSessionRepositoryInterface',
        );
        await serviceSessionRepository.save({
          machineId: washingMachineId,
          userId: sessionUser.id,
          status: 'waiting', // or 'in_use'
          startTime: new Date(),
          endTime: new Date(Date.now() + 3600000), // 1 hour later
        });

        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${washingMachineId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'MACHINE_IN_USE');
      });

      // Note: Testing machine in use scenarios with service sessions would require creating
      // complex test data. These scenarios are covered by the business logic in the service layer.
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed UUID', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/invalid-uuid`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });

      it('should handle empty ID parameter', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404); // Route not found

        expect(response.status).toBe(404);
      });

      it('should handle special characters in code', async () => {
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/WM@#$%`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });

      it('should handle very long code', async () => {
        const longCode = 'A'.repeat(100);
        const response = await request(app.getHttpServer())
          .delete(`${endpoint}/${longCode}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });
    });
  });
});
