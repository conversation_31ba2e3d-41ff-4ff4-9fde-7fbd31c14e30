import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { AuthService } from '@app/auth';
import { UserService } from '@app/user/services/user.service';
import { UserRole, MachineStatus } from '@app/shared/database/entities';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('WashingMachineController (e2e) - Update Washing Machine', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let accessToken: string;
  let user: any;
  let washingMachineId: string;

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();

    // Create test user
    user = await testHelper.createTestUserInDb({
      email: '<EMAIL>',
      password: 'Password123!',
      name: 'Test User',
      phone: '0123456789',
      role: UserRole.CUSTOMER,
    });

    accessToken = authService.generateAccessToken({ userId: user.id });

    // Create a washing machine for testing
    const createPayload = {
      code: 'A01',
      name: 'Washing machine Samsung',
      model: 'WD172CS',
      capacityKg: 11,
      machinePrograms: [
        {
          name: 'Wash and dry standard',
          code: 'WASH_AND_DRY_STANDARD',
          price: 10000,
          settings: {
            duration: 60,
            temperature: 40,
            spinSpeed: 1000,
            weight: 11,
          },
        },
      ],
    };

    const createResponse = await request(app.getHttpServer())
      .post('/washing-machines')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(createPayload)
      .expect(201);

    washingMachineId = createResponse.body.id;
  });

  describe('PUT /washing-machines/:idOrCode', () => {
    const endpoint = (idOrCode: string) => `/washing-machines/${idOrCode}`;

    describe('Success cases', () => {
      it('should update washing machine name successfully', async () => {
        const payload = {
          name: 'Washing machine Samsung Updated',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty(
          'name',
          'Washing machine Samsung Updated',
        );
        expect(response.body).toHaveProperty('model', 'WD172CS');
        expect(response.body).toHaveProperty('capacityKg', 11);
      });

      it('should update washing machine model successfully', async () => {
        const payload = {
          model: 'WD172CS-Updated',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('name', 'Washing machine Samsung');
        expect(response.body).toHaveProperty('model', 'WD172CS-Updated');
        expect(response.body).toHaveProperty('capacityKg', 11);
      });

      it('should update washing machine capacity successfully', async () => {
        const payload = {
          capacityKg: 12,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('name', 'Washing machine Samsung');
        expect(response.body).toHaveProperty('model', 'WD172CS');
        expect(response.body).toHaveProperty('capacityKg', 12);
      });

      it('should update multiple fields successfully', async () => {
        const payload = {
          name: 'Washing machine Samsung Updated',
          model: 'WD172CS-Updated',
          capacityKg: 12,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty(
          'name',
          'Washing machine Samsung Updated',
        );
        expect(response.body).toHaveProperty('model', 'WD172CS-Updated');
        expect(response.body).toHaveProperty('capacityKg', 12);
      });

      it('should update machine code successfully', async () => {
        const payload = {
          code: 'A02',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('code', 'A02');
      });

      it('should update store ID successfully', async () => {
        const newStoreId = uuidv4(); // Generate UUID once
        const payload = {
          storeId: newStoreId,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('storeId', newStoreId);
      });

      it('should update machine status successfully', async () => {
        const payload = {
          status: 'maintenance',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('status', 'maintenance');
      });

      it('should update machine status from maintenance to available', async () => {
        // First set to maintenance
        await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: 'maintenance' })
          .expect(200);

        // Then change to available
        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: 'available' })
          .expect(200);

        expect(response.body).toHaveProperty('status', 'available');
      });

      it('should update machine status to any valid status', async () => {
        const statuses = [
          'available',
          'maintenance',
          'out_of_service',
          'completed',
        ];

        for (const status of statuses) {
          const response = await request(app.getHttpServer())
            .put(endpoint(washingMachineId))
            .set('Authorization', `Bearer ${accessToken}`)
            .send({ status })
            .expect(200);

          expect(response.body).toHaveProperty('status', status);
        }
      });

      it('should update using machine code instead of ID', async () => {
        const payload = {
          name: 'Washing machine Samsung Updated',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint('A01'))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty(
          'name',
          'Washing machine Samsung Updated',
        );
      });

      it('should update existing machine program successfully', async () => {
        // First get the washing machine to get program IDs
        const getResponse = await request(app.getHttpServer())
          .get(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        const programId = getResponse.body.programs[0].id;

        const payload = {
          machinePrograms: [
            {
              id: programId,
              price: 12000,
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('programs');
        expect(response.body.programs).toHaveLength(1);
        expect(response.body.programs[0].product).toHaveProperty(
          'pricePoints',
          12000,
        );
      });
    });

    describe('Error cases - Business Logic', () => {
      it('should return 404 for non-existent washing machine ID', async () => {
        const payload = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint('non-existent-id'))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });

      it('should return 404 for non-existent washing machine code', async () => {
        const payload = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint('NONEXISTENT'))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
      });

      it('should return 404 for non-existent machine program', async () => {
        const payload = {
          machinePrograms: [
            {
              id: uuidv4(), // Valid UUID but non-existent
              price: 12000,
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'MACHINE_PROGRAM_NOT_FOUND',
        );
      });

      it('should return 409 for duplicate machine code', async () => {
        // First create another washing machine
        const createPayload = {
          code: 'A02',
          name: 'Washing machine Samsung 2',
          model: 'WD172CS',
          capacityKg: 11,
          machinePrograms: [
            {
              name: 'Wash and dry standard',
              code: 'WASH_AND_DRY_STANDARD',
              price: 10000,
              settings: {
                duration: 60,
                temperature: 40,
                spinSpeed: 1000,
                weight: 11,
              },
            },
          ],
        };

        await request(app.getHttpServer())
          .post('/washing-machines')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        // Then try to update first machine with duplicate code
        const updatePayload = {
          code: 'A02',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(updatePayload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'MACHINE_CODE_EXISTS');
      });
    });

    describe('Error cases - Validation', () => {
      it('should return 422 for invalid name (empty string)', async () => {
        const payload = {
          name: '',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid name (too long)', async () => {
        const payload = {
          name: 'a'.repeat(256), // Exceeds max length of 255
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid model (empty string)', async () => {
        const payload = {
          model: '',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid capacity (zero)', async () => {
        const payload = {
          capacityKg: 0,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid capacity (negative)', async () => {
        const payload = {
          capacityKg: -1,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid machine code format', async () => {
        const payload = {
          code: 'A01!', // Invalid format - contains special character
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid store ID format', async () => {
        const payload = {
          storeId: 'invalid-uuid',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid machine program (missing required id)', async () => {
        const payload = {
          machinePrograms: [
            {
              price: 12000,
              // Missing id field
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid machine program price (zero)', async () => {
        const payload = {
          machinePrograms: [
            {
              id: uuidv4(), // Valid UUID format
              price: 0,
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid machine program price (negative)', async () => {
        const payload = {
          machinePrograms: [
            {
              id: uuidv4(), // Valid UUID format
              price: -1,
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 409 for machine in use', async () => {
        // First, update the machine status to IN_USE
        await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ status: MachineStatus.IN_USE })
          .expect(200);

        // Then try to update the machine while it's in use
        const payload = {
          name: 'Updated Name While In Use',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'MACHINE_IN_USE');
        expect(response.body).toHaveProperty(
          'message',
          'Cannot update washing machine while it is in use.',
        );
      });
    });

    describe('Error cases - Authentication', () => {
      it('should return 401 for missing authorization header', async () => {
        const payload = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .send(payload)
          .expect(401);
      });

      it('should return 401 for invalid token', async () => {
        const payload = {
          name: 'Updated Name',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', 'Bearer invalid-token')
          .send(payload)
          .expect(401);
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle empty payload', async () => {
        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({})
          .expect(200); // Should succeed with no changes

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('name', 'Washing machine Samsung');
      });

      it('should handle null values', async () => {
        const payload = {
          name: null,
          model: null,
          capacityKg: null,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200); // Should succeed without updating anything

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('name', 'Washing machine Samsung'); // Original value preserved
        expect(response.body).toHaveProperty('model', 'WD172CS'); // Original value preserved
        expect(response.body).toHaveProperty('capacityKg', 11); // Original value preserved
      });

      it('should handle undefined values', async () => {
        const payload = {
          name: undefined,
          model: undefined,
          capacityKg: undefined,
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200); // Should succeed without updating anything

        expect(response.body).toHaveProperty('id', washingMachineId);
        expect(response.body).toHaveProperty('name', 'Washing machine Samsung'); // Original value preserved
        expect(response.body).toHaveProperty('model', 'WD172CS'); // Original value preserved
        expect(response.body).toHaveProperty('capacityKg', 11); // Original value preserved
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          name: '   ',
          model: '   ',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle maximum length values', async () => {
        const payload = {
          name: 'a'.repeat(255), // Maximum length
          model: 'a'.repeat(255), // Maximum length
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty('name', 'a'.repeat(255));
        expect(response.body).toHaveProperty('model', 'a'.repeat(255));
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          name: 'Washing machine with special chars !@#$%^&*()',
          model: 'Model with special chars !@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send(payload)
          .expect(200);

        expect(response.body).toHaveProperty(
          'name',
          'Washing machine with special chars !@#$%^&*()',
        );
        expect(response.body).toHaveProperty(
          'model',
          'Model with special chars !@#$%^&*()',
        );
      });

      it('should handle different HTTP methods', async () => {
        const payload = {
          name: 'Updated Name',
        };

        const methods = ['POST', 'PATCH', 'DELETE']; // Remove GET as it might return 200

        for (const method of methods) {
          const response = await request(app.getHttpServer())
            [method.toLowerCase()](endpoint(washingMachineId))
            .set('Authorization', `Bearer ${accessToken}`)
            .send(payload);

          // Should return method not allowed (405) or not found (404) for unsupported methods
          expect([404, 405]).toContain(response.status);
        }
      });

      it('should preserve existing data when updating only specific fields', async () => {
        // First update name only
        await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ name: 'Updated Name Only' })
          .expect(200);

        // Then update model only
        const response = await request(app.getHttpServer())
          .put(endpoint(washingMachineId))
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ model: 'Updated Model Only' })
          .expect(200);

        // Should preserve the updated name from previous request
        expect(response.body).toHaveProperty('name', 'Updated Name Only');
        expect(response.body).toHaveProperty('model', 'Updated Model Only');
        expect(response.body).toHaveProperty('capacityKg', 11); // Original value preserved
      });
    });
  });
});
