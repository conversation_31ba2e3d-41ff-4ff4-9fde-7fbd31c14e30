import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import {
  WashingMachineRepositoryInterface,
  ServiceSessionRepositoryInterface,
} from '@app/shared/database/repositories';
import {
  MachineStatus,
  ServiceSessionStatus,
} from '@app/shared/database/entities';

describe('WashingMachineController (e2e) - List Washing Machines', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let washingMachineRepository: WashingMachineRepositoryInterface;
  let serviceSessionRepository: ServiceSessionRepositoryInterface;
  const endpoint = '/washing-machines';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
    washingMachineRepository = app.get<WashingMachineRepositoryInterface>(
      'WashingMachineRepositoryInterface',
    );
    serviceSessionRepository = app.get<ServiceSessionRepositoryInterface>(
      'ServiceSessionRepositoryInterface',
    );
  });

  beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
  });

  describe('GET /washing-machines', () => {
    describe('Success cases', () => {
      it('should list machines with pagination and wrapper', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({});

        // Create sample machines
        const payloads = [
          {
            code: 'L01',
            name: 'Alpha One',
            model: 'MDL-L01',
            capacityKg: 10,
            machinePrograms: [
              {
                name: 'Standard',
                code: 'STD',
                price: 7000,
                settings: { duration: 40 },
              },
            ],
          },
          {
            code: 'L02',
            name: 'Alpha Two Next',
            model: 'MDL-L02',
            capacityKg: 11,
            machinePrograms: [
              {
                name: 'Quick',
                code: 'QK',
                price: 6000,
                settings: { duration: 20 },
              },
            ],
          },
        ];

        for (const p of payloads) {
          await request(app.getHttpServer())
            .post(endpoint)
            .set('Authorization', `Bearer ${accessToken}`)
            .send(p)
            .expect(201);
        }

        const res = await request(app.getHttpServer())
          .get(`${endpoint}?skip=0&limit=10`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(res.body).toHaveProperty('code', 'OK');
        expect(res.body).toHaveProperty('message', 'Success');
        expect(res.body).toHaveProperty('data');
        expect(res.body.data).toHaveProperty('items');
        expect(Array.isArray(res.body.data.items)).toBe(true);
        expect(res.body.data).toHaveProperty('count', 2);
        expect(res.body.data).toHaveProperty(
          'total',
          res.body.data.items.length,
        );
        expect(res.body.data).toHaveProperty('currentPage', 1);
        expect(res.body.data).toHaveProperty('totalPage', 1);
      });

      it('should filter by keyword (AND tokens)', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({});

        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: 'K01',
            name: 'Alpha Two Next',
            model: 'MDL-K01',
            capacityKg: 10,
            machinePrograms: [
              {
                name: 'Standard',
                code: 'STD',
                price: 7000,
                settings: { duration: 40 },
              },
            ],
          })
          .expect(201);

        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: 'K02',
            name: 'Alpha Three',
            model: 'MDL-K02',
            capacityKg: 10,
            machinePrograms: [
              {
                name: 'Standard',
                code: 'STD',
                price: 7000,
                settings: { duration: 40 },
              },
            ],
          })
          .expect(201);

        const res = await request(app.getHttpServer())
          .get(`${endpoint}?keyword=Alpha Two&skip=0&limit=10`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        const names = res.body.data.items.map((m: any) => m.name);
        expect(names).toEqual(['Alpha Two Next']);
      });

      it('should filter by status', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({});

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: 'S01',
            name: 'Status Machine',
            model: 'MDL-S01',
            capacityKg: 9,
            machinePrograms: [
              {
                name: 'Standard',
                code: 'STD',
                price: 7000,
                settings: { duration: 40 },
              },
            ],
          })
          .expect(201);

        // Update status to maintenance using repository (real DB operation)
        await washingMachineRepository.update(created.body.id, {
          status: MachineStatus.MAINTENANCE,
        } as any);

        const res = await request(app.getHttpServer())
          .get(
            `${endpoint}?status=${MachineStatus.MAINTENANCE}&skip=0&limit=10`,
          )
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(res.body.data.items.length).toBe(1);
        expect(res.body.data.items[0]).toHaveProperty('code', 'S01');
        expect(res.body.data.items[0]).toHaveProperty(
          'status',
          MachineStatus.MAINTENANCE,
        );
      });

      it('should filter machines in use by current user', async () => {
        const { user, accessToken } = await testHelper.createAuthenticatedUser(
          {},
        );

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: 'U01',
            name: 'Using Machine',
            model: 'MDL-U01',
            capacityKg: 9,
            machinePrograms: [
              {
                name: 'Standard',
                code: 'STD',
                price: 7000,
                settings: { duration: 40 },
              },
            ],
          })
          .expect(201);

        // Create a service session for this user and machine with ongoing status
        await serviceSessionRepository.save(
          serviceSessionRepository.create({
            userId: user.id,
            machineId: created.body.id,
            status: ServiceSessionStatus.IN_USE,
          }) as any,
        );

        const res = await request(app.getHttpServer())
          .get(`${endpoint}?isUsingByMe=true&skip=0&limit=10`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(res.body.data.items.length).toBe(1);
        expect(res.body.data.items[0]).toHaveProperty('code', 'U01');
      });
    });

    describe('Error and validation cases', () => {
      it('should return 401 for missing access token', async () => {
        const res = await request(app.getHttpServer())
          .get(`${endpoint}?skip=0&limit=10`)
          .expect(401);

        expect(res.body).toHaveProperty('code', 'UNAUTHORIZED');
      });

      it('should return 422 for invalid storeId', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({});

        const res = await request(app.getHttpServer())
          .get(`${endpoint}?storeId=invalid-uuid`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(res.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid status enum', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({});

        const res = await request(app.getHttpServer())
          .get(`${endpoint}?status=invalid`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);

        expect(res.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid pagination values', async () => {
        const { accessToken } = await testHelper.createAuthenticatedUser({});

        const r1 = await request(app.getHttpServer())
          .get(`${endpoint}?skip=-1`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);
        expect(r1.body).toHaveProperty('errors');

        const r2 = await request(app.getHttpServer())
          .get(`${endpoint}?limit=0`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);
        expect(r2.body).toHaveProperty('errors');

        const r3 = await request(app.getHttpServer())
          .get(`${endpoint}?limit=101`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(422);
        expect(r3.body).toHaveProperty('errors');
      });
    });
  });
});
