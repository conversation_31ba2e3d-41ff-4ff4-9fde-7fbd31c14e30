import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';
import { WashingMachineService } from '@app/store/services/washing-machine.service';
import { ServiceSessionService } from '@app/store/services/service-session.service';
import { StoreService } from '@app/store/services/store.service';
import {
  MachineStatus,
  ServiceSessionStatus,
} from '@app/shared/database/entities';

describe('WashingMachineController (e2e) - Bulk Status Update', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let washingMachineService: WashingMachineService;
  let serviceSessionService: ServiceSessionService;
  let storeService: StoreService;
  let testHelper: UserTestHelper;
  const endpoint = '/washing-machines/status/update';

  beforeAll(async () => {
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    washingMachineService = app.get<WashingMachineService>(
      WashingMachineService,
    );
    serviceSessionService = app.get<ServiceSessionService>(
      ServiceSessionService,
    );
    storeService = app.get<StoreService>(StoreService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    await global.clearTestDatabase();
  });

  describe('POST /washing-machines/status/update', () => {
    // Helper function to create user and store
    const createUserAndStore = async () => {
      const user = await testHelper.createTestUserInDb();
      const store = await storeService.getOrCreateAnyStore();
      return { user, store };
    };

    describe('Success cases', () => {
      it('should update status for multiple washing machines successfully', async () => {
        // Create test user and store
        const { user, store } = await createUserAndStore();

        // Create multiple washing machines
        const machine1 = await washingMachineService.create(
          {
            code: 'WM001',
            name: 'Washing Machine 1',
            model: 'Model A',
            capacityKg: 8,
            machinePrograms: [
              {
                name: 'Quick Wash',
                code: 'QUICK',
                price: 50000,
                settings: {
                  duration: 30,
                  temperature: 30,
                  spinSpeed: 800,
                  washIntensity: 'normal',
                },
                features: {},
                displayOrder: 1,
              },
            ],
          },
          store.id,
        );

        const machine2 = await washingMachineService.create(
          {
            code: 'WM002',
            name: 'Washing Machine 2',
            model: 'Model B',
            capacityKg: 10,
            machinePrograms: [
              {
                name: 'Normal Wash',
                code: 'NORMAL',
                price: 60000,
                settings: {
                  duration: 45,
                  temperature: 40,
                  spinSpeed: 1000,
                  washIntensity: 'normal',
                },
                features: {},
                displayOrder: 1,
              },
            ],
          },
          store.id,
        );

        const payload = {
          machineIds: [machine1.id, machine2.id],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });

      it('should update status for single washing machine successfully', async () => {
        // Create test user and store
        const { user, store } = await createUserAndStore();

        // Create washing machine
        const machine = await washingMachineService.create(
          {
            code: 'WM003',
            name: 'Washing Machine 3',
            model: 'Model C',
            capacityKg: 12,
            machinePrograms: [
              {
                name: 'Heavy Wash',
                code: 'HEAVY',
                price: 70000,
                settings: {
                  duration: 60,
                  temperature: 60,
                  spinSpeed: 1200,
                  washIntensity: 'strong',
                },
                features: {},
                displayOrder: 1,
              },
            ],
          },
          store.id,
        );

        const payload = {
          machineIds: [machine.id],
          status: MachineStatus.OUT_OF_SERVICE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(200);

        expect(response.body).toBe(true);
      });
    });

    describe('Error cases', () => {
      it('should return 409 for empty machine list', async () => {
        const payload = {
          machineIds: [],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'EMPTY_MACHINE_LIST');
        expect(response.body).toHaveProperty(
          'message',
          'Washing machine list cannot be empty.',
        );
      });

      it('should return 404 for non-existent machines', async () => {
        const payload = {
          machineIds: ['non-existent-uuid-1', 'non-existent-uuid-2'],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
        expect(response.body).toHaveProperty(
          'message',
          'Some washing machines not found',
        );
      });

      it('should return 404 for mixed existing and non-existent machines', async () => {
        // Create test user and store
        const { user, store } = await createUserAndStore();

        // Create one washing machine
        const machine = await washingMachineService.create(
          {
            code: 'WM004',
            name: 'Washing Machine 4',
            model: 'Model D',
            capacityKg: 8,
            machinePrograms: [
              {
                name: 'Quick Wash',
                code: 'QUICK',
                price: 50000,
                settings: {
                  duration: 30,
                  temperature: 30,
                  spinSpeed: 800,
                  washIntensity: 'normal',
                },
                features: {},
                displayOrder: 1,
              },
            ],
          },
          store.id,
        );

        const payload = {
          machineIds: [machine.id, 'non-existent-uuid'],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(404);

        expect(response.body).toHaveProperty(
          'code',
          'WASHING_MACHINE_NOT_FOUND',
        );
        expect(response.body).toHaveProperty(
          'message',
          'Some washing machines not found',
        );
      });

      it('should return 409 for machines in use', async () => {
        // Create test user and store
        const { user, store } = await createUserAndStore();

        // Create washing machine
        const machine = await washingMachineService.create(
          {
            code: 'WM005',
            name: 'Washing Machine 5',
            model: 'Model E',
            capacityKg: 8,
            machinePrograms: [
              {
                name: 'Quick Wash',
                code: 'QUICK',
                price: 50000,
                settings: {
                  duration: 30,
                  temperature: 30,
                  spinSpeed: 800,
                  washIntensity: 'normal',
                },
                features: {},
                displayOrder: 1,
              },
            ],
          },
          store.id,
        );

        // Create active service session manually
        const serviceSessionRepository = app.get(
          'ServiceSessionRepositoryInterface',
        );
        await serviceSessionRepository.save({
          userId: user.id,
          machineId: machine.id,
          productId: 'test-product-id',
          status: ServiceSessionStatus.WAITING,
        });

        const payload = {
          machineIds: [machine.id],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'MACHINE_IN_USE');
        expect(response.body).toHaveProperty(
          'message',
          'Some washing machines are in use, cannot update status',
        );
      });

      it('should return 409 for machines with IN_USE status', async () => {
        // Create test user and store
        const { user, store } = await createUserAndStore();

        // Create washing machine with IN_USE status
        const machine = await washingMachineService.create(
          {
            code: 'WM006',
            name: 'Washing Machine 6',
            model: 'Model F',
            capacityKg: 8,
            machinePrograms: [
              {
                name: 'Quick Wash',
                code: 'QUICK',
                price: 50000,
                settings: {
                  duration: 30,
                  temperature: 30,
                  spinSpeed: 800,
                  washIntensity: 'normal',
                },
                features: {},
                displayOrder: 1,
              },
            ],
          },
          store.id,
        );

        // Update machine status to IN_USE
        await washingMachineService.update(machine.id, {
          status: MachineStatus.IN_USE,
        });

        const payload = {
          machineIds: [machine.id],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(409);

        expect(response.body).toHaveProperty('code', 'MACHINE_IN_USE');
        expect(response.body).toHaveProperty(
          'message',
          'Some washing machines are in use, cannot update status',
        );
      });
    });

    describe('Validation errors', () => {
      it('should return 422 for invalid machine IDs', async () => {
        const payload = {
          machineIds: ['invalid-uuid', 'also-invalid'],
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for invalid status', async () => {
        const payload = {
          machineIds: ['valid-uuid-1', 'valid-uuid-2'],
          status: 'INVALID_STATUS',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing machineIds', async () => {
        const payload = {
          status: MachineStatus.MAINTENANCE,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for missing status', async () => {
        const payload = {
          machineIds: ['valid-uuid-1', 'valid-uuid-2'],
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for empty payload', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send({})
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for null values', async () => {
        const payload = {
          machineIds: null,
          status: null,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should return 422 for undefined values', async () => {
        const payload = {
          machineIds: undefined,
          status: undefined,
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });

    describe('Edge cases and boundary conditions', () => {
      it('should handle malformed JSON', async () => {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .set('Content-Type', 'application/json')
          .send('invalid json')
          .expect(400);
      });

      it('should handle whitespace-only values', async () => {
        const payload = {
          machineIds: ['   ', '   '],
          status: '   ',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });

      it('should handle special characters in inputs', async () => {
        const payload = {
          machineIds: [
            '<EMAIL>!@#$%^&*()',
            'special chars !@#$%^&*()',
          ],
          status: 'special!@#$%^&*()',
        };

        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload)
          .expect(422);

        expect(response.body).toHaveProperty('errors');
      });
    });
  });
});
