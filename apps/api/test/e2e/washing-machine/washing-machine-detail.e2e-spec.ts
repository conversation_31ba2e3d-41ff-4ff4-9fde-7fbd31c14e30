import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('WashingMachineController (e2e) - Detail Washing Machine', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userService: UserService;
  let testHelper: UserTestHelper;
  let accessToken: string;
  const endpoint = '/washing-machines';

  beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
  });

  beforeEach(async () => {
    // REQUIRED: Always clear database before each test
    await global.clearTestDatabase();
    const user = await testHelper.createTestUserInDb();
    accessToken = authService.generateAccessToken({ userId: user.id });
  });

  describe('GET /washing-machines/:idOrCode', () => {
    describe('Success cases', () => {
      it('should get washing machine detail by code including store, programs and latest session', async () => {
        // Create a machine first
        const createPayload = {
          code: 'D01',
          name: 'Detail Machine',
          model: 'MDL-D01',
          capacityKg: 9,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
            {
              name: 'Quick',
              code: 'QUICK',
              price: 5000,
              settings: { duration: 20 },
            },
          ],
        };

        await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const res = await request(app.getHttpServer())
          .get(`${endpoint}/D01`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(res.body).toHaveProperty('id');
        expect(res.body).toHaveProperty('code', 'D01');
        expect(res.body).toHaveProperty('store');
        expect(Array.isArray(res.body.programs)).toBe(true);
        expect(res.body.programs.length).toBeGreaterThan(0);
        expect(res.body).toHaveProperty('latestServiceSession');
        expect(res.body.latestServiceSession).toBeNull();
      });

      it('should get washing machine detail by id', async () => {
        const createPayload = {
          code: 'IDX01',
          name: 'Machine By Id',
          model: 'MDL-IDX01',
          capacityKg: 10,
          machinePrograms: [
            {
              name: 'Standard',
              code: 'STANDARD',
              price: 7000,
              settings: { duration: 40 },
            },
          ],
        };

        const created = await request(app.getHttpServer())
          .post(endpoint)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createPayload)
          .expect(201);

        const id = created.body.id;

        const res = await request(app.getHttpServer())
          .get(`${endpoint}/${id}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(res.body).toHaveProperty('id', id);
        expect(res.body).toHaveProperty('code', 'IDX01');
        expect(Array.isArray(res.body.programs)).toBe(true);
        expect(res.body).toHaveProperty('latestServiceSession');
      });
    });

    describe('Error cases', () => {
      it('should return 404 if washing machine not found', async () => {
        const res = await request(app.getHttpServer())
          .get(`${endpoint}/NOT_EXIST`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(404);

        expect(res.body).toHaveProperty('code', 'WASHING_MACHINE_NOT_FOUND');
      });
    });
  });
});
