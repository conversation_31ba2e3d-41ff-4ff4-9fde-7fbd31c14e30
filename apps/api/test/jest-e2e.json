{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "transformIgnorePatterns": ["node_modules/(?!.*\\.mjs$)", "dist/", "mocks/"], "moduleNameMapper": {"^@app/shared(|/.*)$": "<rootDir>/../../../libs/shared/src/$1", "^@app/auth(|/.*)$": "<rootDir>/../../../libs/auth/src/$1", "^@app/user(|/.*)$": "<rootDir>/../../../libs/user/src/$1", "^@app/store(|/.*)$": "<rootDir>/../../../libs/store/src/$1", "^@app/aws(|/.*)$": "<rootDir>/../../../libs/aws/src/$1", "^@app/mail(|/.*)$": "<rootDir>/../../../libs/mail/src/$1", "^@app/notification(|/.*)$": "<rootDir>/../../../libs/notification/src/$1", "^@app/cache(|/.*)$": "<rootDir>/../../../libs/cache/src/$1", "^@app/payment(|/.*)$": "<rootDir>/../../../libs/payment/src/$1", "^@app/order(|/.*)$": "<rootDir>/../../../libs/order/src/$1", "^@app/system(|/.*)$": "<rootDir>/../../../libs/system/src/$1", "^file-type$": "<rootDir>/mocks/file-type.mock.js"}, "setupFilesAfterEnv": ["<rootDir>/setup/jest-e2e.setup.ts"], "forceExit": true, "detectOpenHandles": true, "testTimeout": 30000, "maxWorkers": 1, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.spec.(t|j)s", "!src/**/*.e2e-spec.(t|j)s"], "coverageDirectory": "./coverage/e2e", "coverageReporters": ["text", "lcov", "html"]}