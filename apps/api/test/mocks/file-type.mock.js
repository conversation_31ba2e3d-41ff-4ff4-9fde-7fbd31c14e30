module.exports = {
  fileTypeFromBuffer: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromFile: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromStream: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromBlob: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromArrayBuffer: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromUint8Array: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromTokenizer: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromUrl: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromPath: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromFileAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromStreamAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromBlobAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromArrayBufferAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromUint8ArrayAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromTokenizerAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromUrlAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
  fileTypeFromPathAsync: jest.fn().mockResolvedValue({
    ext: 'jpg',
    mime: 'image/jpeg',
  }),
};
