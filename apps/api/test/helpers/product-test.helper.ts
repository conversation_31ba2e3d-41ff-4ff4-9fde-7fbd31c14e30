import { INestApplication } from '@nestjs/common';
import {
  Product,
  ProductType,
  ProductCategory,
  ProductStatus,
} from '@app/shared/database/entities';
import { ProductRepositoryInterface } from '@app/shared/database/repositories';

export class ProductTestHelper {
  private productRepository: ProductRepositoryInterface;

  constructor(private readonly app: INestApplication) {
    this.productRepository = app.get<ProductRepositoryInterface>(
      'ProductRepositoryInterface',
    );
  }

  async createTestProduct(overrides: Partial<Product> = {}): Promise<Product> {
    const product = new Product();
    product.name = overrides.name || 'Test Product';
    product.slug = overrides.slug || 'test-product';
    product.type = overrides.type || ProductType.SERVICE;
    product.category = overrides.category || ProductCategory.LAUNDRY_SERVICE;
    product.pricePoints = overrides.pricePoints || 1000;
    product.status = overrides.status || ProductStatus.ACTIVE;
    product.description =
      overrides.description || 'Test product for order creation';
    product.imageUrl = overrides.imageUrl || null;

    return this.productRepository.save(product);
  }

  async createTestProductInDb(
    overrides: Partial<Product> = {},
  ): Promise<Product> {
    return this.createTestProduct(overrides);
  }

  async createMultipleTestProducts(count: number = 3): Promise<Product[]> {
    const products: Product[] = [];

    for (let i = 0; i < count; i++) {
      const product = await this.createTestProduct({
        name: `Test Product ${i + 1}`,
        slug: `test-product-${i + 1}`,
        pricePoints: 1000 + i * 500, // Different prices
      });
      products.push(product);
    }

    return products;
  }
}
