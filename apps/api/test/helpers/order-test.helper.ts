import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from './user-test.helper';
import { ProductTestHelper } from './product-test.helper';
import {
  PaymentMethod,
  Order,
  OrderStatus,
} from '@app/shared/database/entities';
import { CURRENCY_TYPE } from '@app/payment/constants';

export interface CreateOrderItemData {
  productId: string;
  quantity: number;
  metadata?: Record<string, any>;
}

export interface CreateOrderData {
  userId?: string;
  items: CreateOrderItemData[];
  paymentMethod?: PaymentMethod;
  currency?: CURRENCY_TYPE;
  description?: string;
}

export class OrderTestHelper {
  private userTestHelper: UserTestHelper;
  private productTestHelper: ProductTestHelper;
  private authService: AuthService;

  constructor(
    private readonly app: INestApplication,
    authService: AuthService,
    userService: UserService,
  ) {
    this.authService = authService;
    this.userTestHelper = new UserTestHelper(app, authService, userService);
    this.productTestHelper = new ProductTestHelper(app);
  }

  async createTestOrderWithUser(
    orderData: CreateOrderData,
    userOverrides: any = {},
  ): Promise<{ order: any; user: any; accessToken: string; product: any }> {
    // Create test user
    const user = await this.userTestHelper.createTestUserInDb(userOverrides);
    const accessToken = this.authService.generateAccessToken({
      userId: user.id,
    });

    // Create test product if not provided
    let product;
    if (orderData.items.length > 0 && !orderData.items[0].productId) {
      product = await this.productTestHelper.createTestProduct();
      orderData.items[0].productId = product.id;
    } else if (orderData.items.length > 0) {
      // If productId is provided, we assume it exists
      product = { id: orderData.items[0].productId };
    }

    // Create order via API
    const payload = {
      userId: orderData.userId || user.id,
      items: orderData.items,
      paymentMethod: orderData.paymentMethod || PaymentMethod.POINTS,
      currency: orderData.currency || CURRENCY_TYPE.VND,
      description: orderData.description || 'Test order',
    };

    const response = await request(this.app.getHttpServer())
      .post('/orders')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload)
      .expect(201);

    return {
      order: response.body,
      user,
      accessToken,
      product,
    };
  }

  async createTestOrderWithExistingUser(
    orderData: CreateOrderData,
    user: any,
    accessToken: string,
  ): Promise<{ order: any; product: any }> {
    // Create test product if not provided
    let product;
    if (orderData.items.length > 0 && !orderData.items[0].productId) {
      product = await this.productTestHelper.createTestProduct();
      orderData.items[0].productId = product.id;
    } else if (orderData.items.length > 0) {
      // If productId is provided, we assume it exists
      product = { id: orderData.items[0].productId };
    }

    // Create order via API
    const payload = {
      userId: orderData.userId || user.id,
      items: orderData.items,
      paymentMethod: orderData.paymentMethod || PaymentMethod.POINTS,
      currency: orderData.currency || CURRENCY_TYPE.VND,
      description: orderData.description || 'Test order',
    };

    const response = await request(this.app.getHttpServer())
      .post('/orders')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload)
      .expect(201);

    return {
      order: response.body,
      product,
    };
  }

  async createTestOrder(
    orderData: CreateOrderData,
    accessToken: string,
  ): Promise<any> {
    const payload = {
      userId: orderData.userId,
      items: orderData.items,
      paymentMethod: orderData.paymentMethod || PaymentMethod.POINTS,
      currency: orderData.currency || CURRENCY_TYPE.VND,
      description: orderData.description || 'Test order',
    };

    const response = await request(this.app.getHttpServer())
      .post('/orders')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload)
      .expect(201);

    return response.body;
  }

  async createTestOrderWithStatus(
    status: OrderStatus,
    userOverrides: any = {},
    orderOverrides: Partial<CreateOrderData> = {},
  ): Promise<{ order: any; user: any; accessToken: string; product: any }> {
    const { order, user, accessToken, product } =
      await this.createTestOrderWithUser(
        {
          items: [{ productId: '', quantity: 1 }],
          ...orderOverrides,
        },
        userOverrides,
      );

    // Update order status if needed
    if (status !== OrderStatus.PENDING) {
      await this.updateOrderStatus(order.id, status, accessToken);
    }

    return { order, user, accessToken, product };
  }

  async updateOrderStatus(
    orderId: string,
    status: OrderStatus,
    accessToken: string,
  ): Promise<any> {
    const response = await request(this.app.getHttpServer())
      .put(`/orders/${orderId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ status })
      .expect(200);

    return response.body;
  }

  async cancelOrder(orderId: string, accessToken: string): Promise<any> {
    const response = await request(this.app.getHttpServer())
      .put(`/orders/${orderId}/cancel`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    return response.body;
  }

  async completeOrder(orderId: string, accessToken: string): Promise<any> {
    const response = await request(this.app.getHttpServer())
      .put(`/orders/${orderId}/complete`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    return response.body;
  }

  async deleteOrder(orderId: string, accessToken: string): Promise<any> {
    const response = await request(this.app.getHttpServer())
      .delete(`/orders/${orderId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    return response.body;
  }

  async getOrder(orderId: string, accessToken: string): Promise<any> {
    const response = await request(this.app.getHttpServer())
      .get(`/orders/${orderId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    return response.body;
  }

  async getOrders(accessToken: string): Promise<any> {
    const response = await request(this.app.getHttpServer())
      .get('/orders')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    return response.body;
  }
}
