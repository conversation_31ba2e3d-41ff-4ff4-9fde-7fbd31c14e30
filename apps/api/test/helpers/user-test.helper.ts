import { faker } from '@faker-js/faker';
import {
  User,
  UserRole,
  UserStatus,
  USER_LANGUAGE,
} from '@app/shared/database/entities';
import { UserService } from '@app/user/services/user.service';
import { AuthService } from '@app/auth';
import { INestApplication } from '@nestjs/common';
import { RedisClientService } from '@app/cache/services/redis-client.service';

export interface TestUser {
  user: User;
  accessToken: string;
}

export class UserTestHelper {
  constructor(
    private app: INestApplication,
    private authService: AuthService,
    private userService: UserService,
  ) {}

  // Helper function to generate access token
  async generateAccessToken(userId: string): Promise<string> {
    return await this.authService.generateAccessToken({ userId });
  }

  // Basic user creation functions (moved from exports)
  createTestUser(overrides: Partial<User> = {}): Partial<User> {
    const email = faker.internet.email();
    return {
      id: faker.string.uuid(),
      username: email,
      email: email,
      phone: faker.phone.number(),
      password: faker.internet.password(),
      name: faker.person.fullName(),
      birthday: faker.date.past(),
      language: USER_LANGUAGE.EN,
      avatar: faker.image.avatar(),
      role: UserRole.CUSTOMER,
      status: UserStatus.ACTIVE,
      verifiedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  createAdminUser(overrides: Partial<User> = {}): Partial<User> {
    return this.createTestUser({
      role: UserRole.ADMIN,
      ...overrides,
    });
  }

  createUnverifiedUser(overrides: Partial<User> = {}): Partial<User> {
    return this.createTestUser({
      verifiedAt: null,
      ...overrides,
    });
  }

  createInactiveUser(overrides: Partial<User> = {}): Partial<User> {
    return this.createTestUser({
      status: UserStatus.INACTIVE,
      ...overrides,
    });
  }

  createBlockedUser(overrides: Partial<User> = {}): Partial<User> {
    return this.createTestUser({
      status: UserStatus.BLOCKED,
      ...overrides,
    });
  }

  // Predefined test users
  get testUsers() {
    return {
      admin: this.createAdminUser({
        role: UserRole.ADMIN,
      }),
      customer: this.createTestUser({
        role: UserRole.CUSTOMER,
      }),
      unverified: this.createUnverifiedUser({
        verifiedAt: null,
      }),
      inactive: this.createInactiveUser({
        status: UserStatus.INACTIVE,
      }),
    };
  }

  // Database creation functions (moved from exports)
  async createTestUserInDb(overrides: Partial<User> = {}): Promise<User> {
    const userData = this.createTestUser(overrides);
    return await this.userService.create(userData as any);
  }

  async createAdminUserInDb(overrides: Partial<User> = {}): Promise<User> {
    const userData = this.createAdminUser(overrides);
    return await this.userService.create(userData as any);
  }

  async createUnverifiedUserInDb(overrides: Partial<User> = {}): Promise<User> {
    const userData = this.createUnverifiedUser(overrides);
    return await this.userService.create(userData as any);
  }

  async createInactiveUserInDb(overrides: Partial<User> = {}): Promise<User> {
    const userData = this.createInactiveUser(overrides);
    return await this.userService.create(userData as any);
  }

  async createBlockedUserInDb(overrides: Partial<User> = {}): Promise<User> {
    const userData = this.createBlockedUser(overrides);
    return await this.userService.create(userData as any);
  }

  // Enhanced authenticated user creation methods
  async createAuthenticatedUser(
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    // Generate unique email to avoid duplicate constraint
    const uniqueEmail = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
    const uniquePhone = `0123456${Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')}`;

    const user = await this.createTestUserInDb({
      ...userData,
      email: userData.email || uniqueEmail,
      phone: userData.phone || uniquePhone,
    });

    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }

  async createUnverifiedUserWithAuth(
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    const user = await this.createUnverifiedUserInDb(userData);
    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }

  async createAdminUserWithAuth(
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    const user = await this.createAdminUserInDb(userData);
    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }

  async createInactiveUserWithAuth(
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    const user = await this.createInactiveUserInDb(userData);
    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }

  async createBlockedUserWithAuth(
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    const user = await this.createBlockedUserInDb(userData);
    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }

  // Utility methods
  async mockRedisVerificationCode(
    userId: string,
    code: string,
    type: 'email' | 'change-email' | 'delete' = 'email',
  ): Promise<void> {
    try {
      const redisService = this.app.get(RedisClientService);
      if (redisService) {
        const key =
          type === 'email'
            ? `user-verify-email:${userId}`
            : type === 'change-email'
              ? `user-change-email:${userId}`
              : `user-delete-${userId}`;
        await redisService.set(key, code, { EX: 300 });
      }
    } catch (error) {
      console.warn('Redis service not available for mocking:', error.message);
    }
  }

  // Advanced user creation methods
  async createMultipleUsers(
    count: number,
    userData: Partial<User> = {},
  ): Promise<TestUser[]> {
    const users: TestUser[] = [];
    for (let i = 0; i < count; i++) {
      const uniqueEmail = `test-${Date.now()}-${i}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniquePhone = `0123456${Math.floor(Math.random() * 1000)
        .toString()
        .padStart(3, '0')}`;

      const user = await this.createTestUserInDb({
        ...userData,
        email: userData.email || uniqueEmail,
        phone: userData.phone || uniquePhone,
      });

      const accessToken = await this.generateAccessToken(user.id);
      users.push({ user, accessToken });
    }
    return users;
  }

  async createUserWithRole(
    role: UserRole,
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    const user = await this.createTestUserInDb({
      ...userData,
      role,
    });
    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }

  async createUserWithStatus(
    status: UserStatus,
    userData: Partial<User> = {},
  ): Promise<TestUser> {
    const user = await this.createTestUserInDb({
      ...userData,
      status,
    });
    const accessToken = await this.generateAccessToken(user.id);
    return { user, accessToken };
  }
}
