import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import appConfig from '@app/shared/config/app.config';
import { setupApp } from './app';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await setupApp(app);
  await app.listen(appConfig().APP_PORT, () =>
    new Logger(bootstrap.name).log(
      `Application running on port ${appConfig().APP_PORT}`,
    ),
  );
}

bootstrap();
