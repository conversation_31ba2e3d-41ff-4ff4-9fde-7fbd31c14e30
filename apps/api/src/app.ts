import {
  ClassSerializerInterceptor,
  RequestMethod,
  UnprocessableEntityException,
  ValidationError,
  ValidationPipe,
  INestApplication,
} from '@nestjs/common';
import { SwaggerModule } from '@nestjs/swagger';
import appConfig from '@app/shared/config/app.config';
import openAPIConfig from '@app/shared/config/openapi.config';
import cookieParser from 'cookie-parser';
import { TrimPipe } from '@app/shared/pipes/trim.pipe';
import { AppLogger } from '@app/shared/logger/app.logger';
import {
  formatValidationErrors,
  handleUncaughtExceptions,
} from '@app/shared/exceptions';
import { ClsService } from 'nestjs-cls';

// Setup function that can be reused in tests
export async function setupApp(app: INestApplication) {
  const logger = app.get(AppLogger);
  const clsService = app.get(ClsService);

  app.useLogger(logger);

  // Cookie
  app.use(cookieParser());

  // CORS
  app.enableCors({
    origin:
      appConfig().APP_ENV == 'development' || appConfig().APP_ENV == 'localhost'
        ? true
        : appConfig().CORS,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
    exposedHeaders: ['Content-Disposition'],
  });

  // Validation
  app.useGlobalPipes(
    new TrimPipe(),
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      exceptionFactory: (validationErrors: ValidationError[]) =>
        new UnprocessableEntityException({
          message: 'Unprocessable Entity',
          errors: formatValidationErrors(validationErrors),
        }),
    }),
  );

  // app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get('Reflector')));

  // Router
  if (appConfig().APP_ROUTER_PREFIX) {
    app.setGlobalPrefix(appConfig().APP_ROUTER_PREFIX, {
      exclude: [{ path: 'health', method: RequestMethod.GET }],
    });
  }

  // OpenAPI
  if (appConfig().APP_ENV !== 'production') {
    const document = SwaggerModule.createDocument(app, openAPIConfig);
    SwaggerModule.setup(
      `${appConfig().APP_ROUTER_PREFIX ? '/' + appConfig().APP_ROUTER_PREFIX : ''}/docs`,
      app,
      document,
    );
  }

  // Starts listening for shutdown hooks
  app.enableShutdownHooks();

  // Uncaught exception handler - only in production/development
  if (process.env.NODE_ENV !== 'test') {
    handleUncaughtExceptions(logger, clsService);
  }
}
