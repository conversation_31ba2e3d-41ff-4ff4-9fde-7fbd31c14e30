import { ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';
import { Request } from 'express';

@Injectable()
export class GoogleAuthGuard extends AuthGuard(SOCIAL_PROVIDER.GOOGLE) {
  logger = new Logger(GoogleAuthGuard.name);
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // NOTICE: CONTROLLER GUARD
    this.logger.debug('===TRIGGER CONTROLLER GUARD===');
    return super.canActivate(context);
  }

  getAuthenticateOptions(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest<Request>();
    const redirectTo = request?.query?.redirectTo;

    const state = redirectTo
      ? encodeURIComponent(JSON.stringify({ redirectTo }))
      : '';

    return {
      state,
    };
  }
}
