import { ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { isEmpty } from 'lodash';

@Injectable()
export class LineAuthGuard extends AuthGuard(SOCIAL_PROVIDER.LINE) {
  logger = new Logger(LineAuthGuard.name);

  constructor(private readonly configService: ConfigService) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // NOTICE: CONTROLLER GUARD
    this.logger.debug('===TRIGGER CONTROLLER LINE GUARD===');
    return super.canActivate(context);
  }

  getAuthenticateOptions(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest<Request>();
    const redirectTo = request?.query?.redirectTo ?? '';

    if (isEmpty(redirectTo)) {
      return;
    }

    const config = this.configService.get('SOCIAL.LINE');
    const { callbackURL } = config;

    return {
      callbackURL: `${callbackURL}${callbackURL.includes('?') ? '&' : '?'}redirectTo=${redirectTo}`,
    };
  }
}
