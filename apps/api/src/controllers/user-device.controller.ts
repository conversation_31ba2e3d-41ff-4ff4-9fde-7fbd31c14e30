import {
  Body,
  Controller,
  Delete,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCookieAuth,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { UserAccessGuard } from '@app/user/guards/user-access.guard';
import { ResponseUnauthorizedDto } from '@app/shared/dto/response-unauthorized.dto';
import { ResponseUnprocessableEntityDto } from '@app/shared/dto/response-unprocessable-entity.dto';
import { UserDeviceTokenService } from '@app/user/services/user-device-token.service';
import { UserDeviceToken } from '@app/shared/database/entities';
import { ErrorResponse } from '@app/shared/types/error.type';
import { CreateUserDeviceTokenDto } from '@app/user/dto';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';

@UseGuards(JwtAccessTokenGuard, UserStatusAccessible)
@Controller('users/:userId/devices')
@ApiTags('User Devices')
@ApiBearerAuth('Access token')
@ApiCookieAuth()
@ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
export class UserDeviceController {
  constructor(
    private readonly userDeviceTokenService: UserDeviceTokenService,
  ) {}

  @Post('register')
  @HttpCode(HttpStatus.OK)
  @UseGuards(UserAccessGuard)
  @ApiOperation({
    summary: 'Register or update a device for a user',
    description:
      'If the device code exists for the user, update metadata and rotate token; otherwise create new and issue token.',
  })
  @ApiBody({
    type: CreateUserDeviceTokenDto,
    description:
      'Device information including deviceToken, deviceCode, deviceName, osVersion, appVersion, deviceType, deviceOs',
  })
  @ApiOkResponse({
    description: 'Device registered or updated successfully',
    schema: { type: 'boolean', example: true },
  })
  @ApiBadRequestResponse({
    description: 'Invalid userId format',
    schema: { properties: { code: { enum: ['BAD_REQUEST'] } } },
  })
  @ApiForbiddenResponse({
    description: 'User is not the owner of the resource',
    schema: { properties: { code: { enum: ['USER_NOT_OWNER'] } } },
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: { properties: { code: { enum: ['USER_NOT_FOUND'] } } },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  async registerDevice(
    @Param('userId') userId: string,
    @Body() payload: CreateUserDeviceTokenDto,
  ): Promise<boolean> {
    const result: UserDeviceToken =
      await this.userDeviceTokenService.createOrUpdate(userId, payload);
    return true;
  }

  @Delete(':deviceCodeOrId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(UserAccessGuard)
  @ApiOperation({ summary: 'Delete a device by id or deviceCode' })
  @ApiOkResponse({
    description: 'Deletion result (true if deleted, false if not found)',
    schema: { type: 'boolean', example: true },
  })
  @ApiBadRequestResponse({
    description: 'Invalid userId format',
    schema: { properties: { code: { enum: ['BAD_REQUEST'] } } },
  })
  @ApiForbiddenResponse({
    description: 'User is not the owner of the resource',
    schema: { properties: { code: { enum: ['USER_NOT_OWNER'] } } },
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: { properties: { code: { enum: ['USER_NOT_FOUND'] } } },
  })
  async unregisterDevice(
    @Param('userId') userId: string,
    @Param('deviceCodeOrId') deviceCodeOrId: string,
  ): Promise<boolean> {
    return await this.userDeviceTokenService.deleteByDeviceCodeOrId(
      userId,
      deviceCodeOrId,
    );
  }
}
