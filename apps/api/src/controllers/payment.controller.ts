import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  Get,
  Query,
  BadRequestException,
  <PERSON>s,
  <PERSON>ers,
  Param,
  Patch,
  UseInterceptors,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiResponse,
  ApiUnprocessableEntityResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
  ApiCookieAuth,
} from '@nestjs/swagger';
import { PaymentService } from '@app/payment/payment.service';
import { Logger } from '@nestjs/common';
import { Public } from '@app/shared/decorators/auth.decorator';
import { ConfigService } from '@nestjs/config';
import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { RequestWithUser } from '@app/shared/types';
import { CreatePaymentDto, CreatePaymentResponseDto } from '@app/payment/dto';
import {
  ResponseUnprocessableEntityDto,
  ResponseBadRequestDto,
  ResponseNotFoundDto,
} from '@app/shared/dto';
import { isMobileDevice } from '@app/shared/helpers';
import {
  PaymentGatewayType,
  PAYMENT_GATEWAY_TYPES,
} from '@app/payment/constants';
import {
  PaymentStatusResponseDto,
  CheckPaymentStatusDto,
} from '@app/payment/dto/payment-response.dto';
import { UpdatePaymentUserDto } from '@app/payment/dto/update-payment-user.dto';
import { User, UserRole } from '@app/shared/database/entities';
import { RolesGuard } from '@app/shared/guards/roles.guard';
import { Roles } from '@app/shared/decorators/roles.decorator';
import { TransactionInterceptor } from '@app/shared/interceptors/transaction.interceptor';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';

@ApiTags('Payment')
@Controller('payment')
@UseGuards(JwtAccessTokenGuard, UserStatusAccessible)
@ApiBearerAuth('Access token')
@ApiCookieAuth()
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(
    private readonly paymentService: PaymentService,
    private readonly configService: ConfigService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create payment',
    description: 'Create a new payment transaction',
  })
  @ApiBody({
    type: CreatePaymentDto,
    description: 'Payment creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Payment created successfully',
    type: CreatePaymentResponseDto,
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid request data',
    schema: {
      properties: {
        code: { enum: ['BAD_REQUEST', 'UNSUPPORTED_GATEWAY'] },
      },
    },
  })
  async createPayment(
    @Body() createPaymentDto: CreatePaymentDto,
    @Req() req: RequestWithUser,
  ): Promise<CreatePaymentResponseDto> {
    try {
      createPaymentDto.userId = createPaymentDto.userId || req.user.id;

      // Detect if request is from mobile device
      const userAgent = req.headers['user-agent'];
      const isMobile = isMobileDevice(userAgent);

      const result = await this.paymentService.createPayment(
        createPaymentDto.gatewayType,
        createPaymentDto,
        isMobile,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error creating payment: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Check payment status',
    description: 'Check the status of a payment transaction',
  })
  @ApiBody({
    type: CheckPaymentStatusDto,
    description:
      'Payment status check data including gateway and transaction ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment status retrieved successfully',
    type: PaymentStatusResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Unsupported gateway',
    schema: {
      properties: {
        code: { enum: ['UNSUPPORTED_GATEWAY'] },
      },
    },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  async checkPaymentStatus(
    @Body() payload: CheckPaymentStatusDto,
  ): Promise<PaymentStatusResponseDto> {
    return this.paymentService.checkPaymentStatus(
      payload.gateway,
      payload.transactionId,
    );
  }

  @Get('supported-gateways')
  @ApiOperation({
    summary: 'Get supported gateways',
    description: 'Get list of supported payment gateways',
  })
  @ApiResponse({
    status: 200,
    description: 'Supported gateways list',
    schema: {
      type: 'array',
      items: { type: 'string', enum: [...PAYMENT_GATEWAY_TYPES] },
    },
  })
  async getSupportedGateways(): Promise<string[]> {
    return this.paymentService.getSupportedGateways();
  }

  @Get('gateway-info/:gateway')
  @ApiOperation({
    summary: 'Get gateway information',
    description: 'Get information about a specific payment gateway',
  })
  @ApiParam({
    name: 'gateway',
    enum: PAYMENT_GATEWAY_TYPES,
    description: 'Payment gateway to get info for',
  })
  @ApiResponse({
    status: 200,
    description: 'Gateway information',
    schema: {
      type: 'object',
      properties: {
        gateway: { type: 'string', enum: PAYMENT_GATEWAY_TYPES },
        supportsDeepLink: { type: 'boolean' },
        supportsQrCode: { type: 'boolean' },
        features: {
          type: 'object',
          properties: {
            deepLink: { type: 'boolean' },
            qrCode: { type: 'boolean' },
          },
        },
      },
    },
  })
  async getGatewayInfo(@Param('gateway') gateway: PaymentGatewayType) {
    const supportsDeepLink = this.paymentService.supportsDeepLink(gateway);
    const supportsQrCode = this.paymentService.supportsQrCode(gateway);
    const config = this.paymentService.getGatewayConfig(gateway);

    return {
      gateway,
      supportsDeepLink,
      supportsQrCode,
      features: config.features,
    };
  }

  @Public()
  @Post('callback/:gateway')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle payment callback (POST)',
    description:
      'Handle payment callback/webhook from payment gateway via POST request',
  })
  @ApiParam({
    name: 'gateway',
    enum: PAYMENT_GATEWAY_TYPES,
    description: 'Payment gateway that sent the callback',
  })
  @ApiBody({
    description: 'Callback data from payment gateway',
    schema: { type: 'object', additionalProperties: true },
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        transactionId: { type: 'string' },
        status: {
          type: 'string',
          enum: ['success', 'failed', 'pending', 'cancelled'],
        },
        message: { type: 'string' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Unsupported gateway',
    schema: { properties: { code: { enum: ['UNSUPPORTED_GATEWAY'] } } },
  })
  @ApiNotFoundResponse({
    description: 'Payment transaction not found',
    schema: {
      properties: { code: { enum: ['PAYMENT_TRANSACTION_NOT_FOUND'] } },
    },
  })
  async handleCallbackPost(
    @Param('gateway') gateway: PaymentGatewayType,
    @Body() payload: Record<string, any>,
  ) {
    return this.processCallback(gateway, payload);
  }

  @Public()
  @Get('callback/:gateway')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle payment callback (GET)',
    description:
      'Handle payment callback/webhook from payment gateway via GET request',
  })
  @ApiParam({
    name: 'gateway',
    enum: PAYMENT_GATEWAY_TYPES,
    description: 'Payment gateway that sent the callback',
  })
  @ApiQuery({
    name: 'query parameters',
    description: 'Callback data from payment gateway as query parameters',
    schema: { type: 'object', additionalProperties: true },
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        transactionId: { type: 'string' },
        status: {
          type: 'string',
          enum: ['success', 'failed', 'pending', 'cancelled'],
        },
        message: { type: 'string' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Unsupported gateway',
    schema: { properties: { code: { enum: ['UNSUPPORTED_GATEWAY'] } } },
  })
  @ApiNotFoundResponse({
    description: 'Payment transaction not found',
    schema: {
      properties: { code: { enum: ['PAYMENT_TRANSACTION_NOT_FOUND'] } },
    },
  })
  async handleCallbackGet(
    @Param('gateway') gateway: PaymentGatewayType,
    @Query() query: Record<string, any>,
  ) {
    return this.processCallback(gateway, query);
  }

  private async processCallback(
    gateway: PaymentGatewayType,
    payload: Record<string, any>,
  ) {
    try {
      const verifiedData = await this.paymentService.handleCallback(
        gateway,
        payload,
      );

      return {
        success: true,
        transactionId: verifiedData.transactionId,
        status: verifiedData.status,
        message: `Payment ${verifiedData.status} for transaction ${verifiedData.transactionId}`,
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  @Post('cash/confirm/:transactionId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Confirm cash payment',
    description:
      'Confirm that customer has paid at the cash counter (for staff use)',
  })
  @ApiParam({ name: 'transactionId', description: 'Transaction ID to confirm' })
  @ApiBody({
    description: 'Payment confirmation data',
    schema: {
      type: 'object',
      properties: {
        amount: { type: 'number', description: 'Amount paid' },
        staffId: {
          type: 'string',
          description: 'Staff ID who confirmed payment',
        },
        notes: { type: 'string', description: 'Additional notes (optional)' },
      },
      required: ['amount', 'staffId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Payment confirmed successfully',
    schema: {
      type: 'object',
      properties: {
        transactionId: { type: 'string' },
        status: { type: 'string', enum: ['success'] },
        message: { type: 'string' },
      },
    },
  })
  async confirmCashPayment(
    @Param('transactionId') transactionId: string,
    @Body() body: { amount: number; staffId: string; notes?: string },
  ) {
    try {
      const { amount, staffId, notes } = body;

      const result = await this.paymentService.confirmCashPayment(
        transactionId,
        amount,
        staffId,
        notes,
      );

      return {
        transactionId: result.transactionId,
        status: result.status,
        message: 'Payment confirmed at cash counter',
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  @Post('cash/cancel/:transactionId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cancel cash payment',
    description: 'Cancel cash payment if customer does not pay (for staff use)',
  })
  @ApiParam({ name: 'transactionId', description: 'Transaction ID to cancel' })
  @ApiBody({
    description: 'Payment cancellation data',
    schema: {
      type: 'object',
      properties: {
        staffId: {
          type: 'string',
          description: 'Staff ID who cancelled payment',
        },
        reason: {
          type: 'string',
          description: 'Reason for cancellation (optional)',
        },
      },
      required: ['staffId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Payment cancelled successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        transactionId: { type: 'string' },
        status: { type: 'string', enum: ['cancelled'] },
        message: { type: 'string' },
      },
    },
  })
  async cancelCashPayment(
    @Param('transactionId') transactionId: string,
    @Body() body: { staffId: string; reason?: string },
  ) {
    try {
      const { staffId, reason } = body;

      const result = await this.paymentService.cancelCashPayment(
        transactionId,
        staffId,
        reason,
      );

      return {
        success: true,
        transactionId: result.transactionId,
        status: result.status,
        message: 'Payment cancelled at cash counter',
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  @Get('callback/success')
  @HttpCode(HttpStatus.OK)
  @Public()
  @ApiOperation({
    summary: 'Payment success callback',
    description:
      'Handle payment success callback from payment gateway. Supports both API response and mobile app deeplink redirect.',
  })
  @ApiQuery({
    name: 'orderId',
    description: 'Order ID from payment',
    required: true,
    type: 'string',
  })
  @ApiQuery({
    name: 'transactionId',
    description: 'Transaction ID from payment gateway',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'gateway',
    description: 'Payment gateway type',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'redirect',
    description: 'Redirect to mobile app deeplink if true',
    required: false,
    type: 'boolean',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment success callback processed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        orderId: { type: 'string' },
        transactionId: { type: 'string' },
        status: { type: 'string', enum: ['success'] },
      },
    },
  })
  @ApiResponse({
    status: 302,
    description: 'Redirect to mobile app deeplink',
  })
  @ApiBadRequestResponse({
    description: 'Invalid request parameters',
    schema: {
      properties: {
        code: { enum: ['BAD_REQUEST'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order or transaction not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND', 'TRANSACTION_NOT_FOUND'] },
      },
    },
  })
  async paymentSuccessCallback(
    @Query('orderId') orderId: string,
    @Query('transactionId') transactionId?: string,
    @Query('gateway') gateway?: string,
    @Query('redirect') redirect?: string,
    @Res({ passthrough: true }) res?: any,
  ) {
    try {
      // Validate orderId
      if (!orderId || orderId.trim() === '') {
        throw new BadRequestException({
          code: 'BAD_REQUEST',
          message: 'orderId is required',
        });
      }

      // Log the callback for debugging
      this.logger.log(
        `Payment success callback received - OrderId: ${orderId}, TransactionId: ${transactionId}, Gateway: ${gateway}, Redirect: ${redirect}`,
      );

      // Check if we should redirect to mobile app deeplink
      if (redirect === 'true') {
        const mobileAppConfig = this.configService.get('MOBILE_APP');
        if (mobileAppConfig) {
          // Build deeplink URL with additional parameters
          let deeplinkUrl = `${mobileAppConfig.SUCCESS_DEEPLINK_TEMPLATE}${orderId}`;
          if (transactionId) deeplinkUrl += `&transactionId=${transactionId}`;
          if (gateway) deeplinkUrl += `&gateway=${gateway}`;

          this.logger.log(`Redirecting to mobile app deeplink: ${deeplinkUrl}`);
          return res.redirect(302, deeplinkUrl);
        }
      }

      // Here you can add logic to:
      // 1. Verify the payment with the gateway
      // 2. Update order status
      // 3. Send notifications
      // 4. Update user balance if needed

      return {
        success: true,
        message: 'Payment success callback processed successfully',
        orderId,
        transactionId,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error processing payment success callback: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('callback/cancel')
  @HttpCode(HttpStatus.OK)
  @Public()
  @ApiOperation({
    summary: 'Payment cancel callback',
    description:
      'Handle payment cancellation callback from payment gateway. Supports both API response and mobile app deeplink redirect.',
  })
  @ApiQuery({
    name: 'orderId',
    description: 'Order ID from payment',
    required: true,
    type: 'string',
  })
  @ApiQuery({
    name: 'transactionId',
    description: 'Transaction ID from payment gateway',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'gateway',
    description: 'Payment gateway type',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'reason',
    description: 'Reason for cancellation',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'redirect',
    description: 'Redirect to mobile app deeplink if true',
    required: false,
    type: 'boolean',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment cancel callback processed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        orderId: { type: 'string' },
        transactionId: { type: 'string' },
        status: { type: 'string', enum: ['cancelled'] },
        reason: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 302,
    description: 'Redirect to mobile app deeplink',
  })
  @ApiBadRequestResponse({
    description: 'Invalid request parameters',
    schema: {
      properties: {
        code: { enum: ['BAD_REQUEST'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order or transaction not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND', 'TRANSACTION_NOT_FOUND'] },
      },
    },
  })
  async paymentCancelCallback(
    @Query('orderId') orderId: string,
    @Query('transactionId') transactionId?: string,
    @Query('gateway') gateway?: string,
    @Query('reason') reason?: string,
    @Query('redirect') redirect?: string,
    @Res({ passthrough: true }) res?: any,
  ) {
    try {
      // Validate orderId
      if (!orderId || orderId.trim() === '') {
        throw new BadRequestException({
          code: 'BAD_REQUEST',
          message: 'orderId is required',
        });
      }

      // Log the callback for debugging
      this.logger.log(
        `Payment cancel callback received - OrderId: ${orderId}, TransactionId: ${transactionId}, Gateway: ${gateway}, Reason: ${reason}, Redirect: ${redirect}`,
      );

      // Check if we should redirect to mobile app deeplink
      if (redirect === 'true') {
        const mobileAppConfig = this.configService.get('MOBILE_APP');
        if (mobileAppConfig) {
          // Build deeplink URL with additional parameters
          let deeplinkUrl = `${mobileAppConfig.CANCEL_DEEPLINK_TEMPLATE}${orderId}`;
          if (transactionId) deeplinkUrl += `&transactionId=${transactionId}`;
          if (gateway) deeplinkUrl += `&gateway=${gateway}`;
          if (reason) deeplinkUrl += `&reason=${encodeURIComponent(reason)}`;

          this.logger.log(`Redirecting to mobile app deeplink: ${deeplinkUrl}`);
          return res.redirect(302, deeplinkUrl);
        }
      }

      // Here you can add logic to:
      // 1. Update order status to cancelled
      // 2. Release any reserved resources
      // 3. Send notifications
      // 4. Log the cancellation reason

      return {
        success: true,
        message: 'Payment cancel callback processed successfully',
        orderId,
        transactionId,
        status: 'cancelled',
        reason: reason || 'User cancelled payment',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error processing payment cancel callback: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Patch('user/:userId/points')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update user points',
    description: 'Update user points',
  })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ type: UpdatePaymentUserDto })
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @UseGuards(RolesGuard)
  @ApiBearerAuth('Access token')
  @ApiCookieAuth()
  @ApiResponse({
    status: 200,
    description: 'User points updated successfully',
    schema: { type: 'boolean', example: true },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: { properties: { code: { enum: ['UNAUTHORIZED'] } } },
  })
  @ApiForbiddenResponse({
    description: 'Forbidden - requires admin role',
    schema: { properties: { code: { enum: ['FORBIDDEN'] } } },
  })
  @UseInterceptors(TransactionInterceptor())
  async updateUserPoints(
    @Param('userId') userId: string,
    @Body() payload: UpdatePaymentUserDto,
    @Req() req: RequestWithUser,
  ) {
    return this.paymentService.updatePaymentUser(
      userId,
      payload,
      req.user as User,
    );
  }
}
