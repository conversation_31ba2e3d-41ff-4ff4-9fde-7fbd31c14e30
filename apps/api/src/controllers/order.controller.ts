import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Req,
  NotFoundException,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiBearerAuth,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { OrderService } from '@app/order/order.service';
import {
  CreateOrderDto,
  UpdateOrderDto,
  OrderResponseDto,
  SearchOrdersDto,
} from '@app/order/dto';
import { OrderStatus } from '@app/shared/database/entities';
import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import {
  ResponseUnauthorizedDto,
  ResponseUnprocessableEntityDto,
} from '@app/shared/dto';
import { RequestWithUser } from '@app/shared/types';
import { Order } from '@app/shared/database/entities';
import { ErrorResponse } from '@app/shared/types/error.type';
import {
  ApiDocsPagination,
  ApiResponsePagination,
} from '@app/shared/decorators/openapi.decorator';
import { Public } from '@app/shared/decorators/auth.decorator';
import { ResponseInterceptor } from '@app/shared/interceptors/response.interceptor';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';

@ApiTags('Orders')
@Controller('orders')
@UseGuards(JwtAccessTokenGuard, UserStatusAccessible)
@ApiBearerAuth()
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new order',
    description: 'Create a new order with items and payment method',
  })
  @ApiBody({
    type: CreateOrderDto,
    description: 'Order creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Order created successfully',
    type: OrderResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  async createOrder(
    @Req() req: RequestWithUser,
    @Body() createOrderDto: CreateOrderDto,
  ): Promise<Order> {
    const userId = req.user.id;
    const orderData = { ...createOrderDto, userId };
    return await this.orderService.createOrder(orderData);
  }

  @Get('/admin')
  @UseInterceptors(new ResponseInterceptor(OrderResponseDto))
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get orders with filters, sorting, and pagination',
    description:
      'Admin endpoint to get orders with advanced filtering and multi-field sorting. ' +
      'Filters: userId, keyword, washingMachineName, storeName, userName, status, paymentMethod, date range. ' +
      'Sorting: Supports multiple sort fields with custom directions. Default: createdAt DESC.',
  })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    type: [OrderResponseDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiDocsPagination()
  @ApiResponsePagination(OrderResponseDto)
  async list(@Query() query: SearchOrdersDto) {
    const { items, count } = await this.orderService.list(query);
    return {
      count,
      items: await this.orderService.processOrders(items),
    };
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get user orders',
    description: 'Get all orders for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'User orders retrieved successfully',
    type: [Order],
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  async getUserOrders(@Req() req: RequestWithUser): Promise<Order[]> {
    const userId = req.user.id;
    return this.orderService.getOrdersByUserId(userId);
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get order by ID',
    description: 'Get a specific order by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Order retrieved successfully',
    type: Order,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND'] },
      },
    },
  })
  async getOrderById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ): Promise<Order> {
    const userId = req.user.id;
    const order = await this.orderService.getOrderById(id, userId);

    if (!order) {
      throw new NotFoundException({
        code: 'ORDER_NOT_FOUND',
        data: { orderId: id },
      } as ErrorResponse);
    }

    return order;
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update order',
    description: 'Update an existing order',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    type: UpdateOrderDto,
    description: 'Order update data',
  })
  @ApiResponse({
    status: 200,
    description: 'Order updated successfully',
    type: Order,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND'] },
      },
    },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  async updateOrder(
    @Param('id') id: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<Order> {
    return this.orderService.updateOrder(id, updateOrderDto);
  }

  @Put(':id/cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cancel order',
    description: 'Cancel a pending order',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Order cancelled successfully',
    type: Order,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND'] },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Cannot cancel order with current status',
    schema: {
      properties: {
        code: { enum: ['INVALID_ORDER_STATUS'] },
      },
    },
  })
  async cancelOrder(@Param('id') id: string): Promise<Order> {
    return this.orderService.cancelOrder(id);
  }

  @Put(':id/complete')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Complete order',
    description: 'Mark a paid order as completed',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Order completed successfully',
    type: Order,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND'] },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Cannot complete order with current status',
    schema: {
      properties: {
        code: { enum: ['INVALID_ORDER_STATUS'] },
      },
    },
  })
  async completeOrder(@Param('id') id: string): Promise<Order> {
    return this.orderService.completeOrder(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete order',
    description: 'Delete an order and its items',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Order deleted successfully',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Order not found',
    schema: {
      properties: {
        code: { enum: ['ORDER_NOT_FOUND'] },
      },
    },
  })
  async deleteOrder(@Param('id') id: string): Promise<{ success: boolean }> {
    const deleted = await this.orderService.deleteOrder(id);
    return { success: deleted };
  }

  @Get('admin/completed')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get completed orders (Admin)',
    description: 'Get all completed orders for admin management',
  })
  @ApiResponse({
    status: 200,
    description: 'Completed orders retrieved successfully',
    type: [Order],
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  async getCompletedOrders(): Promise<Order[]> {
    return this.orderService.getCompletedOrders();
  }
}
