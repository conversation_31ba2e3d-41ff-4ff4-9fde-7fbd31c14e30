import {
  Controller,
  Get,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UseGuards,
  HttpCode,
  HttpStatus,
  Post,
  Put,
  Req,
  Inject,
  NotFoundException,
  Query,
  Headers,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiExtraModels,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiBody,
  ApiCookieAuth,
  ApiForbiddenResponse,
  ApiConflictResponse,
  ApiUnprocessableEntityResponse,
  ApiNotFoundResponse,
  ApiTooManyRequestsResponse,
  ApiParam,
  ApiOkResponse,
} from '@nestjs/swagger';
import {
  UpdateUserDto,
  UserDto,
  ChangeEmailDto,
  SendChangeEmailCodeDto,
  UpdatePasswordDto,
  VerifyCodeDto,
  DeleteUserDto,
  UpdateUserAdminDto,
} from '@app/user/dto';
import { UserService } from '@app/user/services/user.service';
import { ResponseUnauthorizedDto } from '@app/shared/dto/response-unauthorized.dto';
import { ResponseErrorDto } from '@app/shared/dto/response-error.dto';
import { RequestWithUser } from '@app/shared/types/requests.type';
import { UserAccessGuard } from '@app/user/guards/user-access.guard';
import { ResponseUnprocessableEntityDto } from '@app/shared/dto/response-unprocessable-entity.dto';
import { UserRepositoryInterface } from '@app/shared/database/repositories';
import { ReadOnlyInterceptor } from '@app/shared/interceptors/read-only.interceptor';
import { DB_SERVICE_NAME } from '@app/shared/database/constants';
import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { Throttle } from '@nestjs/throttler';
import { ErrorResponse } from '@app/shared/types/error.type';
import { RolesGuard } from '@app/shared/guards/roles.guard';
import { Roles } from '@app/shared/decorators/roles.decorator';
import { UserRole } from '@app/shared/database/entities';
import { CreateUserDto } from '@app/user/dto/create-user.dto';
import {
  User,
  USER_LANGUAGE,
  UserStatus,
} from '@app/shared/database/entities/user.entity';
import { SearchUserDto } from '@app/user/dto/search-user.dto';
import { AdminChangePasswordDto } from '@app/user/dto/admin-change-password.dto';
import {
  ApiDocsPagination,
  ApiResponsePagination,
} from '@app/shared/decorators/openapi.decorator';
import { PaginationResponse } from '@app/shared/types/common.type';
import { USER_EVENT_TYPE } from '@app/user/constants';
import { UserDeletedEvent } from '@app/user/events/user.event';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';
import { Public } from '@app/shared/decorators/auth.decorator';

@UseGuards(JwtAccessTokenGuard, UserStatusAccessible)
@Controller('users')
@ApiTags('User')
@ApiBearerAuth('Access token')
@ApiCookieAuth()
@ApiExtraModels(UserDto)
@ApiUnauthorizedResponse({
  type: ResponseUnauthorizedDto,
})
@ApiInternalServerErrorResponse({
  type: ResponseErrorDto,
})
export class UserController {
  constructor(
    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @Get('me')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get current user details',
    description: 'Get detailed information of the currently authenticated user',
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.BACKEND))
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  async me(@Req() req: RequestWithUser): Promise<UserDto> {
    return await this.userService.processUser(
      await this.userRepository.findOneById(req.user.id),
    );
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @UseGuards(RolesGuard)
  @ApiOperation({
    summary: 'Get users with filtering and pagination',
    description:
      'Get a paginated list of users with optional filtering by roles, statuses, and keyword search. Admin only.',
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.BACKEND))
  @ApiDocsPagination()
  @ApiResponsePagination(UserDto)
  @ApiForbiddenResponse({
    description:
      'User not authorized to access user data (Admin role required)',
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  async list(
    @Query() dto: SearchUserDto,
  ): Promise<PaginationResponse<UserDto>> {
    const result = await this.userService.getList(dto);

    // Transform User entities to UserDto
    const userDtos = await this.userService.processUsers(result.items);

    return {
      items: userDtos,
      count: result.count,
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get user details by ID',
    description: 'Get detailed information of a user by their ID',
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.BACKEND))
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  async get(@Param('id') id: string): Promise<UserDto> {
    const user = await this.userRepository.findOneById(id);
    if (!user) {
      throw new NotFoundException({ code: 'USER_NOT_FOUND' } as ErrorResponse);
    }
    return await this.userService.processUser(user);
  }

  @Patch(':id')
  @HttpCode(HttpStatus.OK)
  @UseGuards(UserAccessGuard)
  @ApiOperation({
    summary: 'Update user information',
    description:
      'Update user profile information. Only the user can update their own profile.',
  })
  @ApiBody({
    type: UpdateUserDto,
    description: 'User information to update',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'User not authorized to update this profile',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_OWNER'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Email or phone already exists',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMAIL_EXISTED'] },
          },
        },
        {
          properties: {
            code: { enum: ['PHONE_EXISTED'] },
          },
        },
        {
          properties: {
            code: { enum: ['USER_IN_USE'] },
          },
        },
      ],
    },
  })
  async update(
    @Param('id') id: string,
    @Body() payload: UpdateUserDto,
  ): Promise<boolean> {
    return await this.userService.update(id, payload);
  }

  @Patch('admin/:id')
  @HttpCode(HttpStatus.OK)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @UseGuards(RolesGuard)
  @ApiOperation({
    summary: 'Update user information (Admin)',
    description:
      'Update user profile information. Only the admin can update admin information.',
  })
  @ApiBody({
    type: UpdateUserAdminDto,
    description: 'User information to update',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Email or phone already exists',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMAIL_EXISTED'] },
          },
        },
        {
          properties: {
            code: { enum: ['PHONE_EXISTED'] },
          },
        },
      ],
    },
  })
  async updateAdmin(
    @Param('id') id: string,
    @Body() payload: UpdateUserAdminDto,
    @Req() req: RequestWithUser,
  ): Promise<boolean> {
    const user = await this.userRepository.findOneById(id);
    const requestUser = req.user;
    if (
      requestUser.role === UserRole.STAFF &&
      user.role !== UserRole.CUSTOMER
    ) {
      throw new ForbiddenException({
        code: 'FORBIDDEN',
      } as ErrorResponse);
    }

    // Convert UpdateUserAdminDto to UpdateUserDto and call update (will fire events)
    const updateUserPayload = {
      name: payload.name,
      email: payload.email,
      phone: payload.phone,
      address: payload.address,
      birthday: payload.birthday,
      language: payload.language,
      avatar: payload.avatar,
      isAllowedNotify: payload.isAllowedNotify,
      role: payload.role,
      status: payload.status,
      ...(payload.email ? { username: payload.email } : {}),
    } as UpdateUserDto;

    return await this.userService.update(id, updateUserPayload);
  }

  @Public()
  @Post(':id/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify user email with code',
    description:
      'Verify user email address using the verification code sent to their email',
  })
  @ApiBody({
    type: VerifyCodeDto,
    description: 'Verification code sent to user email',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Email already verified or invalid verification code',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMAIL_ALREADY_VERIFIED'] },
          },
        },
        {
          properties: {
            code: { enum: ['INVALID_VERIFY_EMAIL_CODE'] },
          },
        },
      ],
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  @Throttle({ sendCode: { ttl: 300, limit: 5 } })
  async verify(
    @Param('id') id: string,
    @Body() payload: VerifyCodeDto,
    @Headers('X-Language') language: USER_LANGUAGE,
  ): Promise<boolean> {
    return await this.userService.verifyEmail(
      id,
      payload.code,
      language ?? USER_LANGUAGE.EN,
    );
  }

  @Put(':id/change-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Change user password',
    description:
      'Change user password. User must provide old password and new password.',
  })
  @ApiBody({
    type: UpdatePasswordDto,
    description: 'Old and new password',
  })
  @UseGuards(UserAccessGuard)
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'User not authorized to change this password',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_OWNER'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Invalid old password',
    schema: {
      properties: {
        code: { enum: ['INVALID_PASSWORD'] },
      },
    },
  })
  async changePassword(
    @Param('id') id: string,
    @Body() payload: UpdatePasswordDto,
  ): Promise<boolean> {
    return await this.userService.updatePassword(
      id,
      payload.oldPassword,
      payload.newPassword,
    );
  }

  @Post(':id/change-email/send-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send verification code to new email',
    description:
      'Step 1: Send verification code to the new email address for email change process',
  })
  @ApiBody({
    type: SendChangeEmailCodeDto,
    description: 'New email address to send verification code',
  })
  @UseGuards(UserAccessGuard)
  @Throttle({ sendCode: { ttl: 300, limit: 3 } })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'User not authorized to change email',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_OWNER'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Email already exists',
    schema: {
      properties: {
        code: { enum: ['EMAIL_EXISTED'] },
      },
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  async sendChangeEmailCode(
    @Param('id') id: string,
    @Body() payload: SendChangeEmailCodeDto,
  ): Promise<boolean> {
    return await this.userService.sendChangeEmailCode(id, payload.newEmail);
  }

  @Post(':id/change-email/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify and change email with code',
    description: 'Step 2: Verify the code and change email address',
  })
  @ApiBody({
    type: ChangeEmailDto,
    description: 'New email and verification code',
  })
  @UseGuards(UserAccessGuard)
  @Throttle({ sendCode: { ttl: 300, limit: 5 } })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'User not authorized to change email',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_OWNER'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Email already exists or invalid verification code',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMAIL_EXISTED'] },
          },
        },
        {
          properties: {
            code: { enum: ['INVALID_VERIFICATION_CODE'] },
          },
        },
      ],
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  async verifyAndChangeEmail(
    @Param('id') id: string,
    @Body() payload: ChangeEmailDto,
  ): Promise<boolean> {
    return await this.userService.verifyAndChangeEmail(id, payload);
  }

  @Post(':id/delete-account/send-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send verification code to delete account',
    description:
      'Step 1: Send verification code to user email for account deletion process',
  })
  @UseGuards(UserAccessGuard)
  @Throttle({ deleteAccount: { ttl: 300, limit: 2 } })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'User not authorized to delete account',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_OWNER'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'User email not found',
    schema: {
      properties: {
        code: { enum: ['USER_EMAIL_NOT_FOUND'] },
      },
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  async sendDeleteAccountCode(@Param('id') id: string): Promise<boolean> {
    return await this.userService.sendDeleteAccountCode(id);
  }

  @Post(':id/delete-account/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify and delete account with code',
    description: 'Step 2: Verify the code and delete user account permanently',
  })
  @ApiBody({
    type: VerifyCodeDto,
    description: 'Verification code sent to user email',
  })
  @UseGuards(UserAccessGuard)
  @Throttle({ deleteAccount: { ttl: 300, limit: 3 } })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found or invalid verification code',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
          },
        },
        {
          properties: {
            code: { enum: ['INVALID_VERIFICATION_CODE'] },
          },
        },
      ],
    },
  })
  @ApiForbiddenResponse({
    description: 'User not authorized to delete account',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_OWNER'] },
      },
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  async verifyAndDeleteAccount(
    @Param('id') id: string,
    @Body() payload: VerifyCodeDto,
  ): Promise<boolean> {
    return await this.userService.verifyAndDeleteAccount(id, payload.code);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete user (Self or Admin)',
    description:
      'Allows a user to delete their own account, or an admin to delete any user, directly without verification code. This is a permanent action. User will be soft deleted and all related data will be removed.',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID to delete',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'User deleted successfully',
    type: Boolean,
    example: true,
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid UUID format',
    type: ResponseUnprocessableEntityDto,
  })
  @Roles(UserRole.ADMIN)
  @UseGuards(UserAccessGuard)
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'Forbidden: self or admin required',
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiConflictResponse({
    description: 'User is in use',
    schema: {
      properties: {
        code: { enum: ['USER_IN_USE'] },
      },
    },
  })
  async deleteUser(@Param() params: DeleteUserDto): Promise<boolean> {
    const user = await this.userService.getUserById(params.id);
    if (!user) {
      throw new NotFoundException({ code: 'USER_NOT_FOUND' } as ErrorResponse);
    }
    if (user.role === UserRole.CUSTOMER) {
      await this.userService.update(params.id, {
        status: UserStatus.INACTIVE,
      } as UpdateUserDto);
    } else {
      await this.userService.deleteUser(params.id, true);
    }

    this.eventEmitter.emit(USER_EVENT_TYPE.DELETED, new UserDeletedEvent(user));

    return true;
  }

  @Put('admin/:id/change-password')
  @HttpCode(HttpStatus.OK)
  @Roles(UserRole.ADMIN)
  @UseGuards(RolesGuard)
  @ApiOperation({
    summary: 'Admin change user password',
    description:
      'Admin can change any user password. Requires current password verification for security. Sends email notification to user.',
  })
  @ApiBody({
    type: AdminChangePasswordDto,
    description: 'New password for the user',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiConflictResponse({
    description: 'Invalid current password',
    schema: {
      properties: {
        code: { enum: ['INVALID_PASSWORD'] },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'Admin role required',
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  async adminChangePassword(
    @Param('id') id: string,
    @Body() payload: AdminChangePasswordDto,
  ): Promise<boolean> {
    return await this.userService.adminChangePassword(
      id,
      payload.currentPassword,
      payload.newPassword,
    );
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create new account',
    description:
      'Create a new account with all required information. Only existing admins can create new admin accounts.',
  })
  @ApiBody({
    type: CreateUserDto,
    description:
      'Account creation data including name, email, phone, password, role and optional fields',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data or validation errors',
    type: ResponseUnprocessableEntityDto,
  })
  @Roles(UserRole.ADMIN)
  @UseGuards(RolesGuard)
  @ApiConflictResponse({
    description: 'Email or phone already exists',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMAIL_EXISTED'] },
            message: { example: 'Email already exists' },
          },
        },
        {
          properties: {
            code: { enum: ['PHONE_EXISTED'] },
            message: { example: 'Phone number already exists' },
          },
        },
      ],
    },
  })
  async createUser(@Body() payload: CreateUserDto): Promise<UserDto> {
    const user = await this.userService.create(
      {
        ...payload,
        status: UserStatus.ACTIVE,
        verifiedAt: new Date(),
      } as User,
      true,
    );

    return user as UserDto;
  }
}
