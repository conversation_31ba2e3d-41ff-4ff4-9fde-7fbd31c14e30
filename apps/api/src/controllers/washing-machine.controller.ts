import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Query,
  UseGuards,
  UseInterceptors,
  Req,
} from '@nestjs/common';
import { TransactionInterceptor } from '@app/shared/interceptors/transaction.interceptor';
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBody,
  ApiConflictResponse,
  ApiUnprocessableEntityResponse,
  ApiNotFoundResponse,
  ApiExtraModels,
  ApiUnauthorizedResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { DEFAULT_WASHING_MACHINE_PROGRAMS } from '@app/store/constants';
import {
  WashingMachineCreateDto,
  WashingMachineUpdateDto,
  WashingMachineDto,
  SearchWashingMachinesQueryDto,
  WashingMachineUpdateBulkStatusDto,
} from '@app/store/dto';
import { StoreService } from '@app/store/services/store.service';
import { WashingMachineService } from '@app/store/services/washing-machine.service';
import {
  ApiDocsPagination,
  ApiResponsePagination,
} from '@app/shared/decorators/openapi.decorator';
import { ResponseInterceptor } from '@app/shared/interceptors/response.interceptor';
import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { RequestWithUser } from '@app/shared/types/requests.type';
import { ResponseOkDto } from '@app/shared/dto/response-ok.dto';
import { ResponseUnprocessableEntityDto } from '@app/shared/dto/response-unprocessable-entity.dto';
import { ResponseUnauthorizedDto } from '@app/shared/dto/response-unauthorized.dto';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';

@ApiTags('Washing Machine')
@ApiExtraModels(ResponseOkDto, WashingMachineDto)
@UseGuards(JwtAccessTokenGuard, UserStatusAccessible)
@Controller('washing-machines')
export class WashingMachineController {
  constructor(
    private readonly storeService: StoreService,
    private readonly washingMachineService: WashingMachineService,
  ) {}

  @Get('default-programs')
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get default programs for each washing machine model',
    description:
      'Return an object of default programs for each washing machine model. Each program has code, weight, and duration. FE will map the label.',
  })
  @ApiOkResponse({
    description: 'Default programs for each washing machine model',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'WASH_AND_DRY_STANDARD' },
            weight: { type: 'number', example: 11 },
            duration: { type: 'number', example: 60 },
          },
        },
      },
    },
  })
  getDefaultPrograms() {
    return DEFAULT_WASHING_MACHINE_PROGRAMS;
  }

  @Get('')
  @UseInterceptors(new ResponseInterceptor(WashingMachineDto))
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List washing machines with filters and pagination',
    description:
      'Filters: storeId, status[], keyword (split by spaces and AND with ILIKE). If isUsingByMe=true, only include machines with ongoing sessions by current user. Sorting priority: available, completed, in_use (remaining time asc), maintenance, out_of_service.',
  })
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid query parameters',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiDocsPagination()
  @ApiResponsePagination(WashingMachineDto)
  async list(
    @Query() query: SearchWashingMachinesQueryDto,
    @Req() req: RequestWithUser,
  ) {
    const userId = req.user.id;
    const { items, count } = await this.washingMachineService.list(
      query,
      userId,
    );
    return {
      count,
      items: await this.washingMachineService.processWashingMachines(items, {
        userId,
      }),
    };
  }

  @Get(':idOrCode')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get washing machine details by id or code',
    description:
      'Return washing machine details including store info, programs with product price, and the latest service session status.',
  })
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid query parameters',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiOkResponse({
    description: 'Washing machine detail',
    type: WashingMachineDto,
  })
  @ApiNotFoundResponse({
    description: 'Washing machine not found',
    schema: {
      properties: {
        code: { enum: ['WASHING_MACHINE_NOT_FOUND'] },
        message: { example: 'Washing machine not found' },
      },
    },
  })
  async getWashingMachineDetail(
    @Param('idOrCode') idOrCode: string,
    @Req() req: RequestWithUser,
  ): Promise<WashingMachineDto> {
    const machine = await this.washingMachineService.findByIdOrCode(idOrCode);
    if (!machine) {
      throw new NotFoundException({
        code: 'WASHING_MACHINE_NOT_FOUND',
        message: 'Washing machine not found',
      });
    }
    return await this.washingMachineService.processWashingMachine(machine, {
      userId: req.user.id,
    });
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new washing machine',
    description:
      'Create a new washing machine with default programs. Automatically create products and machine programs.',
  })
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @ApiBody({
    type: WashingMachineCreateDto,
    description: 'Washing machine information and programs',
  })
  @ApiOkResponse({
    description: 'Washing machine created successfully',
    type: WashingMachineDto,
  })
  @ApiConflictResponse({
    description: 'Washing machine code already exists',
    schema: {
      properties: {
        code: { enum: ['MACHINE_CODE_EXISTS'] },
        message: {
          example:
            'Washing machine code already exists. Please enter a different code.',
        },
      },
    },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
  })
  @UseInterceptors(TransactionInterceptor())
  async createWashingMachine(@Body() payload: WashingMachineCreateDto) {
    const store = await this.storeService.getOrCreateAnyStore(payload.storeId);
    return this.washingMachineService.create(payload, store.id);
  }

  @Put(':idOrCode')
  @UseInterceptors(TransactionInterceptor())
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update washing machine information',
    description:
      'Update washing machine basic information (code, name, storeId, model, type, capacityKg) and/or machine program prices. Only existing program prices can be updated.',
  })
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @ApiBody({
    type: WashingMachineUpdateDto,
    description: 'Washing machine information to update',
  })
  @ApiOkResponse({
    description: 'Washing machine updated successfully',
    type: WashingMachineDto,
  })
  @ApiNotFoundResponse({
    description: 'Washing machine not found or machine program not found',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['WASHING_MACHINE_NOT_FOUND'] },
            message: { example: 'Washing machine not found' },
          },
        },
        {
          properties: {
            code: { enum: ['MACHINE_PROGRAM_NOT_FOUND'] },
            message: { example: 'Machine program with id xxx not found' },
          },
        },
      ],
    },
  })
  @ApiConflictResponse({
    description: 'Machine code already exists or machine is in use',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['MACHINE_CODE_EXISTS'] },
            message: { example: 'Washing machine code already exists.' },
          },
        },
        {
          properties: {
            code: { enum: ['MACHINE_IN_USE'] },
            message: {
              example: 'Cannot update washing machine while it is in use.',
            },
          },
        },
      ],
    },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  async updateWashingMachine(
    @Param('idOrCode') idOrCode: string,
    @Body() payload: WashingMachineUpdateDto,
  ): Promise<WashingMachineDto> {
    const washingMachine = await this.washingMachineService.update(
      idOrCode,
      payload,
    );
    return await this.washingMachineService.processWashingMachine(
      washingMachine,
    );
  }

  @Delete(':idOrCode')
  @UseInterceptors(TransactionInterceptor())
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete washing machine',
    description:
      'Soft delete washing machine. Cannot delete if machine is currently in use.',
  })
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @ApiOkResponse({
    description: 'Washing machine deleted successfully',
    schema: {
      type: 'boolean',
      example: true,
    },
  })
  @ApiNotFoundResponse({
    description: 'Washing machine not found',
    schema: {
      properties: {
        code: { enum: ['WASHING_MACHINE_NOT_FOUND'] },
        message: { example: 'Washing machine not found' },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Cannot delete washing machine that is currently in use',
    schema: {
      properties: {
        code: { enum: ['MACHINE_IN_USE'] },
        message: {
          example: 'Cannot delete washing machine that is currently in use',
        },
      },
    },
  })
  async deleteWashingMachine(
    @Param('idOrCode') idOrCode: string,
  ): Promise<boolean> {
    return await this.washingMachineService.delete(idOrCode);
  }

  @Post('status/update')
  @UseInterceptors(TransactionInterceptor())
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update status for multiple washing machines',
    description:
      'Update status for multiple washing machines at once. Use washing machine ids.',
  })
  @ApiBearerAuth('Access token')
  @ApiUnauthorizedResponse({ type: ResponseUnauthorizedDto })
  @ApiBody({
    type: WashingMachineUpdateBulkStatusDto,
    description: 'Information to update status for multiple washing machines',
  })
  @ApiOkResponse({
    description: 'Update status successfully',
    schema: {
      type: 'boolean',
      example: true,
    },
  })
  @ApiNotFoundResponse({
    description: 'Some washing machines not found',
    schema: {
      properties: {
        code: { enum: ['WASHING_MACHINE_NOT_FOUND'] },
        message: { example: 'Some washing machines not found' },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Empty machine list or machine is in use',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMPTY_MACHINE_LIST'] },
            message: { example: 'Washing machine list cannot be empty.' },
          },
        },
        {
          properties: {
            code: { enum: ['MACHINE_IN_USE'] },
            message: {
              example: 'Some washing machines are in use, cannot update status',
            },
          },
        },
      ],
    },
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  async bulkStatus(
    @Body() payload: WashingMachineUpdateBulkStatusDto,
  ): Promise<boolean> {
    return await this.washingMachineService.bulkStatus(payload);
  }
}
