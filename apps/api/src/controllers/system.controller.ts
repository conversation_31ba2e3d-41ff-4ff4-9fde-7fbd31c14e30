import {
  Controller,
  Get,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Query,
  ParseFilePipeBuilder,
  UploadedFile,
} from '@nestjs/common';
import { UseInterceptors } from '@nestjs/common';
import { TransactionInterceptor } from '@app/shared/interceptors/transaction.interceptor';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiConsumes,
} from '@nestjs/swagger';

import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { RolesGuard } from '@app/shared/guards/roles.guard';
import { Roles } from '@app/shared/decorators/roles.decorator';
import { SystemConfiguration, UserRole } from '@app/shared/database/entities';
import { ResponseBadRequestDto } from '@app/shared/dto/response-bad-request.dto';
import { SystemService } from '@app/system/system.service';
import { ListSystemSettingsDto } from '@app/system/dto/list-system-settings.dto';
import { SystemSettingResponseDto } from '@app/system/dto/system-setting-response.dto';
import {
  SystemSettingItemDto,
  UpdateSystemSettingsDto,
} from '@app/system/dto/update-system-settings.dto';
import { SettingKeysResponseDto } from '@app/system/dto/setting-keys-response.dto';
import {
  SYSTEM_SETTING_KEY,
  SYSTEM_SETTING_KEY_SCHEMA,
} from '@app/system/const/system.constant';
import { S3Service } from '@app/aws';
import { IMAGE_PATH } from '@app/shared/constants';
import { FileInterceptor } from '@nestjs/platform-express';
import appConfig from '@app/shared/config/app.config';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';
import { Public } from '@app/shared/decorators/auth.decorator';

@Controller('system')
@ApiTags('System')
@ApiBearerAuth()
@UseGuards(JwtAccessTokenGuard, RolesGuard, UserStatusAccessible)
export class SystemController {
  constructor(
    private readonly systemService: SystemService,
    private readonly s3Service: S3Service,
  ) {}

  @Public()
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List system configurations',
    description: 'Get list of system configurations with optional filtering',
  })
  @ApiResponse({
    status: 200,
    type: [SystemSettingResponseDto],
    description: 'List of system configurations',
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  async listSettings(
    @Query() query: ListSystemSettingsDto,
  ): Promise<SystemConfiguration[]> {
    return this.systemService.processSettings(
      await this.systemService.list(query),
    );
  }

  @Public()
  @Get('keys')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get available system setting keys',
    description:
      'Get list of available system setting keys and their schemas for frontend synchronization',
  })
  @ApiResponse({
    status: 200,
    type: SettingKeysResponseDto,
    description: 'Available system setting keys and schemas',
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  async getSettingKeys(): Promise<SettingKeysResponseDto> {
    return {
      keys: SYSTEM_SETTING_KEY,
      schemas: SYSTEM_SETTING_KEY_SCHEMA,
    };
  }

  @Post()
  @HttpCode(HttpStatus.OK)
  // @Roles(UserRole.ADMIN)
  // @UseGuards(RolesGuard)
  @UseInterceptors(TransactionInterceptor())
  @UseInterceptors(FileInterceptor('logo'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update system configurations',
  })
  @ApiBody({
    type: UpdateSystemSettingsDto,
    description: 'Array of settings to update',
  })
  @ApiResponse({
    status: 200,
    type: [SystemSettingResponseDto],
    description: 'Array of updated system configurations',
  })
  @ApiResponse({
    status: 422,
    type: ResponseBadRequestDto,
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  async updateSettings(
    @Body() body: UpdateSystemSettingsDto,
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: 'jpeg|png|jpg|pjpeg|gif|jgif', // 5MB
        })
        .addMaxSizeValidator({
          maxSize: 5242880, // 5MB
        })
        .build({
          fileIsRequired: false,
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    logo?: Express.Multer.File,
  ) {
    if (logo) {
      const resultUpload = await this.s3Service.saveImage(
        logo,
        IMAGE_PATH.SYSTEM.COMPANY_LOGO,
      );
      if (!body?.settings?.length) {
        body.settings = [];
      }
      body.settings.push({
        key: SYSTEM_SETTING_KEY.COMPANY_LOGO,
        value: resultUpload.key,
        type: 'url',
        category: 'system',
        description: 'Company logo',
      } as SystemSettingItemDto);
    }

    return this.systemService.processSettings(
      await this.systemService.updateSettings(body),
    );
  }
}
