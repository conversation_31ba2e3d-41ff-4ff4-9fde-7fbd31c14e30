import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Query,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';

import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { RolesGuard } from '@app/shared/guards/roles.guard';
import { Roles } from '@app/shared/decorators/roles.decorator';
import { FaqQuestion, UserRole } from '@app/shared/database/entities';
import { ResponseBadRequestDto } from '@app/shared/dto/response-bad-request.dto';
import { FaqService } from '@app/system/services';
import {
  CreateFaqDto,
  UpdateFaqDto,
  SearchFaqDto,
  FaqResponseDto,
} from '@app/system/dto/faq';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';

@Controller('faq')
@ApiTags('FAQ')
@ApiBearerAuth()
@UseGuards(JwtAccessTokenGuard, RolesGuard, UserStatusAccessible)
export class FaqController {
  constructor(private readonly faqService: FaqService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List FAQ questions',
    description: 'Get list of FAQ questions with optional filtering',
  })
  @ApiResponse({
    status: 200,
    type: [FaqResponseDto],
    description: 'List of FAQ questions',
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  async list(@Query() query: SearchFaqDto): Promise<FaqQuestion[]> {
    const faqs = await this.faqService.list(query);
    return this.faqService.processFaqs(faqs);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create new FAQ question',
    description: 'Create a new FAQ question with multiple language variants',
  })
  @ApiBody({
    type: CreateFaqDto,
    description: 'FAQ question data with language variants',
  })
  @ApiResponse({
    status: 201,
    type: FaqResponseDto,
    description: 'Created FAQ question',
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  @ApiResponse({
    status: 422,
    description: 'Validation error',
    type: ResponseBadRequestDto,
  })
  async create(@Body() dto: CreateFaqDto): Promise<FaqQuestion> {
    const masterFaq = await this.faqService.create(dto);
    return this.faqService.processFaq(masterFaq);
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update FAQ question',
    description: 'Update an existing FAQ question and its language variants',
  })
  @ApiParam({
    name: 'id',
    description: 'FAQ question ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: UpdateFaqDto,
    description: 'Updated FAQ question data',
  })
  @ApiResponse({
    status: 200,
    type: FaqResponseDto,
    description: 'Updated FAQ question',
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  @ApiResponse({
    status: 404,
    schema: {
      properties: {
        code: { enum: ['FAQ_NOT_FOUND'] },
      },
    },
  })
  @ApiResponse({
    status: 422,
    description: 'Validation error',
    type: ResponseBadRequestDto,
  })
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateFaqDto,
  ): Promise<FaqQuestion> {
    const updatedMasterFaq = await this.faqService.update({ ...dto, id });
    return this.faqService.processFaq(updatedMasterFaq);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete FAQ question',
    description: 'Delete an FAQ question and all its language variants',
  })
  @ApiParam({
    name: 'id',
    description: 'FAQ question ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'FAQ question deleted successfully',
    schema: {
      type: 'boolean',
      example: true,
    },
  })
  @ApiResponse({
    status: 401,
    schema: {
      properties: {
        code: { enum: ['UNAUTHORIZED'] },
      },
    },
  })
  @ApiResponse({
    status: 403,
    schema: {
      properties: {
        code: { enum: ['FORBIDDEN'] },
      },
    },
  })
  @ApiResponse({
    status: 404,
    schema: {
      properties: {
        code: { enum: ['FAQ_NOT_FOUND'] },
      },
    },
  })
  async delete(@Param('id') id: string): Promise<boolean> {
    await this.faqService.delete(id);
    return true;
  }
}
