import { AuthService } from '@app/auth';
import {
  ForgotPasswordDto,
  ResetPasswordDto,
  RetrieveAuthDto,
  VerifyForgotPasswordDto,
} from '@app/auth/dto';
import { FacebookVerificationDto } from '@app/auth/dto/facebook-verification.dto';
import { GoogleVerificationDto } from '@app/auth/dto/google-verification.dto';
import { LineVerificationDto } from '@app/auth/dto/line-verification.dto';
import { LoginDto } from '@app/auth/dto/login.dto';
import { RegisterDto } from '@app/auth/dto/register.dto';
import { User, USER_LANGUAGE, UserRole } from '@app/shared/database/entities';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';
import { Roles } from '@app/shared/decorators/roles.decorator';

import {
  ResponseBadRequestDto,
  ResponseErrorDto,
  ResponseForbiddenDto,
  ResponseUnprocessableEntityDto,
} from '@app/shared/dto';
import { RolesGuard } from '@app/shared/guards/roles.guard';
import { UserStatusAccessible } from '@app/shared/guards/user-status.guard';
import { SignedUrlGuard } from '@app/shared/guards/signed-url.guard';
import { ErrorResponse } from '@app/shared/types';
import { RequestWithUser } from '@app/shared/types/requests.type';
import { UserDto } from '@app/user/dto';
import { UserService } from '@app/user/services/user.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiConflictResponse,
  ApiExtraModels,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiTooManyRequestsResponse,
  ApiUnauthorizedResponse,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { plainToInstance } from 'class-transformer';
import { Response } from 'express';
import { FacebookAuthGuard } from '../guards/facebook.guard';
import { GoogleAuthGuard } from '../guards/google.guard';
import { JwtAccessTokenGuard } from '../guards/jwt-access-token.guard';
import { JwtRefreshTokenGuard } from '../guards/jwt-refresh-token.guard';
import { LineAuthGuard } from '../guards/line.guard';
import { LocalAuthGuard } from '../guards/local.guard';
import { UserDeviceTokenService } from '@app/user/services/user-device-token.service';

@ApiTags('Auth')
@ApiExtraModels(RetrieveAuthDto, UserDto)
@Controller('auth')
@ApiInternalServerErrorResponse({
  type: ResponseErrorDto,
})
export class AuthController {
  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly userDeviceTokenService: UserDeviceTokenService,
  ) {}

  @Roles(UserRole.CUSTOMER)
  @UseGuards(LocalAuthGuard, UserStatusAccessible, RolesGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Login using email and password',
    description:
      'Authenticate user with email/phone and password. Returns access token and user information.',
  })
  @ApiBody({
    type: LoginDto,
    description: 'User credentials (username can be email, phone, or username)',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or invalid password',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['INVALID_PASSWORD'] },
            message: { example: 'Invalid password' },
          },
        },
        {
          properties: {
            message: { example: 'Validation failed' },
            error: { example: 'Bad Request' },
          },
        },
      ],
    },
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
        message: { example: 'User not found' },
      },
    },
  })
  async login(
    @Req() request: RequestWithUser,
    @Headers('X-Language') language: USER_LANGUAGE,
  ): Promise<RetrieveAuthDto> {
    if (language && request?.user && language !== request?.user?.language) {
      await this.userService.update(request?.user?.id, {
        language,
      });
    }
    return this._signIn(request);
  }

  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @UseGuards(LocalAuthGuard, RolesGuard, UserStatusAccessible)
  @Post('admin/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Admin/Staff login using email and password',
    description:
      'Authenticate admin or staff with email and password. Returns access token and user information.',
  })
  @ApiBody({
    type: LoginDto,
    description: 'Admin/Staff credentials (email and password)',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or invalid password',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['INVALID_PASSWORD'] },
            message: { example: 'Invalid password' },
          },
        },
        {
          properties: {
            message: { example: 'Validation failed' },
            error: { example: 'Bad Request' },
          },
        },
      ],
    },
  })
  @ApiNotFoundResponse({
    description: 'Admin/Staff not found',
    schema: {
      properties: {
        code: { enum: ['USER_NOT_FOUND'] },
        message: { example: 'User not found' },
      },
    },
  })
  adminLogin(@Req() request: RequestWithUser): RetrieveAuthDto {
    return this._signIn(request);
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register using email and password',
    description:
      'Create a new user account with email, phone, password and other details. Returns access token and user information.',
  })
  @ApiBody({
    type: RegisterDto,
    description:
      'User registration data including email, phone, password, name and optional fields',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data or validation errors',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiConflictResponse({
    description: 'Email or phone already exists',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['EMAIL_EXISTED'] },
            message: { example: 'Email already exists' },
          },
        },
        {
          properties: {
            code: { enum: ['PHONE_EXISTED'] },
            message: { example: 'Phone number already exists' },
          },
        },
      ],
    },
  })
  async register(
    @Req() request: RequestWithUser,
    @Body() payload: RegisterDto,
  ): Promise<RetrieveAuthDto> {
    const user = await this.userService.create({
      ...payload,
    } as User);

    request.user = <User>{
      ...user,
    };

    return this._signIn(request);
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtRefreshTokenGuard)
  @ApiOperation({
    summary: 'Refresh access token',
    description:
      'Refreshes the access token using a valid refresh token from cookies. Returns new access token and optional redirect URL.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access - invalid or missing refresh token',
    type: ResponseErrorDto,
  })
  @ApiForbiddenResponse({
    description: 'Forbidden - refresh token expired or invalid',
    type: ResponseForbiddenDto,
  })
  refreshAccessToken(@Req() request: RequestWithUser): {
    accessToken: string;
    redirectTo?: string;
  } {
    const { user } = request;
    const accessToken = this.authService.generateAccessToken({
      userId: user.id,
    });
    const { accessTokenCookie } = this.authService.generateAuthCookie(
      accessToken,
      '',
    );

    request.res.setHeader('Set-Cookie', [accessTokenCookie]);

    const redirectTo = request.session.redirectTo ?? '';
    request.session.user = { id: user.id, username: user.username } as User;
    if (redirectTo) {
      request.session.redirectTo = '';
    }

    return {
      accessToken,
      redirectTo,
    };
  }

  @Get('/google')
  @HttpCode(HttpStatus.OK)
  @UseGuards(GoogleAuthGuard)
  @ApiOperation({
    summary: 'Google OAuth login',
    description:
      'Redirects user to Google OAuth for authentication. This endpoint initiates the Google OAuth flow.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: ResponseErrorDto,
  })
  async google(): Promise<boolean> {
    return true;
  }

  @Get('/google/callback')
  @UseGuards(GoogleAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Google OAuth callback',
    description:
      'Handles the callback from Google OAuth after successful authentication. Redirects user to success URL with authentication tokens.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access or invalid OAuth state',
    type: ResponseErrorDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during OAuth callback processing',
    type: ResponseErrorDto,
  })
  googleCallback(@Req() request: RequestWithUser, @Res() response: Response) {
    const result = this._signIn(
      request,
      this.configService.get('FRONTEND_APP.AUTH_SUCCESS_URL'),
    );

    if (result.redirectTo) {
      response.redirect(result.redirectTo);
    }

    return result;
  }

  @Post('google/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify Google user with access token',
    description:
      'Verifies a Google access token and authenticates the user. Creates a new user account if the user does not exist.',
  })
  @ApiBody({
    type: GoogleVerificationDto,
    description: 'Google access token for user verification',
  })
  @ApiBadRequestResponse({
    description: 'Invalid Google access token or verification failed',
    type: ResponseBadRequestDto,
  })
  @ApiConflictResponse({
    description: 'User with same email already exists',
    schema: {
      properties: {
        code: { enum: ['EMAIL_EXISTED'] },
        message: { example: 'Email already exists' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during Google verification',
    type: ResponseErrorDto,
  })
  async googleVerification(
    @Body() verificationDto: GoogleVerificationDto,
    @Req() request: RequestWithUser,
    @Headers('X-Language') language: USER_LANGUAGE,
  ) {
    const member = await this.authService.verifyGoogleMember(verificationDto);

    const user = await this.userService.getAuthenticatedViaSocial({
      provider: SOCIAL_PROVIDER.GOOGLE,
      socialId: member?.sub ?? null,
      email: member?.email ?? null,
      name: member?.name ?? null,
      avatar: member?.picture ?? null,
      userId: null,
      language: language ?? USER_LANGUAGE.EN,
      isSkipCreateUser: false,
    });

    request.user = user;

    return this._signIn(request);
  }

  @Get('line')
  @UseGuards(LineAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Line OAuth login',
    description:
      'Redirects user to Line OAuth for authentication. This endpoint initiates the Line OAuth flow.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: ResponseErrorDto,
  })
  async line(): Promise<boolean> {
    return true;
  }

  @Get('line/callback')
  @UseGuards(LineAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Line OAuth callback',
    description:
      'Handles the callback from Line OAuth after successful authentication. Redirects user to success URL with authentication tokens.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access or invalid OAuth state',
    type: ResponseErrorDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during OAuth callback processing',
    type: ResponseErrorDto,
  })
  lineLoginCallback(
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    let redirectTo = '';
    if (request?.user?.isSocialLink) {
      redirectTo = `${this.configService.get(
        'FRONTEND_APP.AUTH_SUCCESS_URL',
      )}?code=LINE_LINKED_SUCCESS`;
    } else {
      redirectTo = this.configService.get('FRONTEND_APP.AUTH_SUCCESS_URL');
    }
    const result = this._signIn(request, redirectTo);

    if (result.redirectTo) {
      response.redirect(result.redirectTo);
    }

    return result;
  }

  @Post('line/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify Line user with access token',
    description:
      'Verifies a Line access token and authenticates the user. Creates a new user account if the user does not exist.',
  })
  @ApiBody({
    type: LineVerificationDto,
    description: 'Line access token for user verification',
  })
  @ApiBadRequestResponse({
    description: 'Invalid Line access token or verification failed',
    type: ResponseBadRequestDto,
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during Line verification',
    type: ResponseErrorDto,
  })
  async lineVerification(
    @Body() verificationDto: LineVerificationDto,
    @Req() request: RequestWithUser,
    @Headers('X-Language') language: USER_LANGUAGE,
  ) {
    const member = await this.authService.verifyLineMember(verificationDto);

    const user = await this.userService.getAuthenticatedViaSocial({
      provider: SOCIAL_PROVIDER.LINE,
      socialId: member?.sub ?? member?.userId,
      email: null,
      name: member?.name ?? member?.displayName,
      avatar: member?.picture ?? member?.pictureUrl,
      userId: null,
      language: language ?? USER_LANGUAGE.EN,
      isSkipCreateUser: !verificationDto?.accessToken,
    });

    request.user = user;

    return this._signIn(request);
  }

  @Get('line/link')
  @UseGuards(JwtAccessTokenGuard, LineAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Link Line account to existing user',
    description:
      'Links a Line account to the currently authenticated user. Requires valid JWT access token.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access - invalid or missing access token',
    type: ResponseErrorDto,
  })
  @ApiForbiddenResponse({
    description: 'Forbidden - user not authenticated',
    type: ResponseForbiddenDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during Line account linking',
    type: ResponseErrorDto,
  })
  linkLine() {
    return true;
  }

  @Delete('line/unlink')
  @UseGuards(JwtAccessTokenGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Unlink Line account from user',
    description:
      'Unlinks the Line account from the currently authenticated user. Requires valid JWT access token.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access - invalid or missing access token',
    type: ResponseErrorDto,
  })
  @ApiForbiddenResponse({
    description: 'Forbidden - user not authenticated',
    type: ResponseForbiddenDto,
  })
  @ApiNotFoundResponse({
    description: 'Line account not found or not linked',
    schema: {
      properties: {
        code: { enum: ['LINE_ACCOUNT_NOT_FOUND'] },
        message: { example: 'Line account not found' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during Line account unlinking',
    type: ResponseErrorDto,
  })
  async unlinkLine(@Req() request: RequestWithUser): Promise<boolean> {
    return await this.userService.unlinkLine(request.user.id);
  }

  @Get('/facebook')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Facebook OAuth login',
    description:
      'Redirects user to Facebook OAuth for authentication. This endpoint initiates the Facebook OAuth flow.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: ResponseErrorDto,
  })
  @UseGuards(FacebookAuthGuard)
  facebook(): boolean {
    return true;
  }

  @Get('/facebook/callback')
  @UseGuards(FacebookAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Facebook OAuth callback',
    description:
      'Handles the callback from Facebook OAuth after successful authentication. Redirects user to success URL with authentication tokens.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access or invalid OAuth state',
    type: ResponseErrorDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during OAuth callback processing',
    type: ResponseErrorDto,
  })
  facebookCallback(@Req() request: RequestWithUser, @Res() response: Response) {
    const result = this._signIn(
      request,
      this.configService.get('FRONTEND_APP.AUTH_SUCCESS_URL'),
    );

    if (result.redirectTo) {
      response.redirect(result.redirectTo);
    }

    return result;
  }

  @Post('facebook/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify Facebook user with access token',
    description:
      'Verifies a Facebook access token and authenticates the user. Creates a new user account if the user does not exist.',
  })
  @ApiBody({
    type: FacebookVerificationDto,
    description: 'Facebook access token for user verification',
  })
  @ApiBadRequestResponse({
    description: 'Invalid Facebook access token or verification failed',
    type: ResponseBadRequestDto,
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid input data',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during Facebook verification',
    type: ResponseErrorDto,
  })
  async facebookVerification(
    @Body() verificationDto: FacebookVerificationDto,
    @Req() request: RequestWithUser,
    @Headers('X-Language') language: USER_LANGUAGE,
  ) {
    const member = await this.authService.verifyFacebookMember(verificationDto);

    const user = await this.userService.getAuthenticatedViaSocial({
      provider: SOCIAL_PROVIDER.FACEBOOK,
      socialId: member?.id ?? null,
      email: null,
      name: member?.name ?? null,
      avatar: member?.picture ?? null,
      userId: null,
      language: language ?? USER_LANGUAGE.EN,
      isSkipCreateUser: false,
    });

    request.user = user;

    return this._signIn(request);
  }

  @Post('forgot-password/send-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send mail forgot password',
    description:
      'Send verification code to user email for password reset. The code is valid for 5 minutes.',
  })
  @ApiBody({
    type: ForgotPasswordDto,
    description: 'User email for password reset',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
    type: ResponseBadRequestDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found, account not activated, or account blocked',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
            message: { example: 'User not found' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_NOT_ACTIVATED'] },
            message: { example: 'User account is not activated' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_BLOCKED'] },
            message: { example: 'User account is blocked' },
          },
        },
      ],
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error',
    type: ResponseErrorDto,
  })
  @Throttle({ sendCode: { ttl: 300, limit: 3 } })
  async sendForgotPasswordCode(
    @Body() payload: ForgotPasswordDto,
    @Headers('X-Language') language: USER_LANGUAGE,
  ): Promise<boolean> {
    const { email } = payload;
    await this.userService.forgotPassword(email, language);
    return true;
  }

  @Post('forgot-password/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify forgot password code',
    description:
      'Verify the 6-digit code sent to user email for password reset. Returns signed URL data for password reset.',
  })
  @ApiBody({
    type: VerifyForgotPasswordDto,
    description: 'Email and 6-digit verification code for password reset',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or validation errors',
    type: ResponseBadRequestDto,
  })
  @ApiNotFoundResponse({
    description:
      'User not found, account not activated, account blocked, or invalid verification code',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
            message: { example: 'User not found' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_NOT_ACTIVATED'] },
            message: { example: 'User account is not activated' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_BLOCKED'] },
            message: { example: 'User account is blocked' },
          },
        },
        {
          properties: {
            code: { enum: ['INVALID_VERIFICATION_CODE'] },
            message: { example: 'Invalid verification code' },
          },
        },
      ],
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error',
    type: ResponseErrorDto,
  })
  @Throttle({ sendCode: { ttl: 300, limit: 5 } })
  async verifyForgotPasswordCode(
    @Body() payload: VerifyForgotPasswordDto,
  ): Promise<{ signature: string; expires: number }> {
    const { email, code } = payload;
    return await this.userService.verifyForgotPasswordCode(email, code);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reset password with signed URL',
    description:
      'Resets user password using a signed URL with email, signature, and expiration time. Requires valid signed URL parameters.',
  })
  @ApiQuery({
    name: 'email',
    required: true,
    description: 'User email address',
  })
  @ApiQuery({
    name: 'signature',
    required: true,
    description: 'Signed URL signature for verification',
  })
  @ApiQuery({
    name: 'expires',
    required: true,
    description: 'URL expiration timestamp',
  })
  @ApiBody({
    type: ResetPasswordDto,
    description: 'New password for the user account',
  })
  @ApiBadRequestResponse({
    description: 'Invalid signed URL or expired signature',
    type: ResponseBadRequestDto,
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid password format or validation errors',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found, account not activated, or account blocked',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
            message: { example: 'User not found' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_NOT_ACTIVATED'] },
            message: { example: 'User account is not activated' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_BLOCKED'] },
            message: { example: 'User account is blocked' },
          },
        },
      ],
    },
  })
  @ApiForbiddenResponse({
    description: 'Invalid or expired signed URL',
    type: ResponseForbiddenDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during password reset',
    type: ResponseErrorDto,
  })
  @UseGuards(SignedUrlGuard)
  async resetPassword(
    @Body() payload: ResetPasswordDto,
    @Query('email') email: string,
    @Query('signature') signature: string,
    @Query('expires') expires: string,
  ): Promise<boolean> {
    await this.userService.resetPassword(email, payload.newPassword);
    return true;
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAccessTokenGuard)
  @ApiOperation({
    summary: 'Logout user',
    description:
      'Logs out the currently authenticated user by clearing authentication tokens, adding them to blacklist, and destroying the session. Both access token and refresh token (if present) will be blacklisted to prevent reuse.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access - invalid or missing access token',
    type: ResponseErrorDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during logout process',
    type: ResponseErrorDto,
  })
  async logout(
    @Req() request: RequestWithUser,
    @Headers('X-Device-Code') deviceCode?: string,
  ): Promise<boolean> {
    if (deviceCode) {
      await this.userDeviceTokenService.deleteByDeviceCodeOrId(
        request.user.id,
        deviceCode,
      );
    }
    // Get access token and refresh token from request
    const accessToken = this.authService.getJwtAccessTokenFromRequest(request);
    const refreshToken =
      this.authService.getJwtRefreshTokenFromRequest(request);

    const { accessTokenCookie, refreshTokenCookie } =
      await this.authService.logout(accessToken, refreshToken);

    // Destroy cookie
    request.res.setHeader('Set-Cookie', [
      accessTokenCookie,
      refreshTokenCookie,
    ]);
    // Destroy session
    if (request.session ?? '') {
      request.session.destroy(() => {
        // request.res.clearCookie('connect.sid');
      });
    }

    return true;
  }

  @Post('admin/forgot-password/send-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send mail forgot password for Admin/Staff',
    description:
      'Send verification code to admin or staff email for password reset. The code is valid for 5 minutes. Only works for ADMIN and STAFF users.',
  })
  @ApiBody({
    type: ForgotPasswordDto,
    description: 'Admin/Staff email for password reset',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
    type: ResponseBadRequestDto,
  })
  @ApiNotFoundResponse({
    description:
      'Admin/Staff not found, account not activated, or account blocked',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
            message: { example: 'User not found' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_NOT_ACTIVATED'] },
            message: { example: 'User account is not activated' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_BLOCKED'] },
            message: { example: 'User account is blocked' },
          },
        },
      ],
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error',
    type: ResponseErrorDto,
  })
  @Throttle({ sendCode: { ttl: 300, limit: 3 } })
  async sendAdminForgotPasswordCode(
    @Body() payload: ForgotPasswordDto,
    @Headers('X-Language') language: USER_LANGUAGE,
  ): Promise<boolean> {
    const { email } = payload;

    // Check if user exists and is admin/staff before calling forgotPassword
    const user = await this.userService.getVerifiedUserByEmail(email);
    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STAFF) {
      throw new NotFoundException({
        code: 'USER_NOT_FOUND',
      } as ErrorResponse);
    }

    await this.userService.forgotPassword(email, language);
    return true;
  }

  @Post('admin/forgot-password/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify forgot password code for Admin/Staff',
    description:
      'Verify the 6-digit code sent to admin or staff email for password reset. Returns signed URL data for password reset. Only works for ADMIN and STAFF users.',
  })
  @ApiBody({
    type: VerifyForgotPasswordDto,
    description:
      'Admin/Staff email and 6-digit verification code for password reset',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or validation errors',
    type: ResponseBadRequestDto,
  })
  @ApiNotFoundResponse({
    description:
      'Admin/Staff not found, account not activated, account blocked, or invalid verification code',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
            message: { example: 'User not found' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_NOT_ACTIVATED'] },
            message: { example: 'User account is not activated' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_BLOCKED'] },
            message: { example: 'User account is blocked' },
          },
        },
        {
          properties: {
            code: { enum: ['INVALID_VERIFICATION_CODE'] },
            message: { example: 'Invalid verification code' },
          },
        },
      ],
    },
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again after 5 minutes.',
    schema: {
      properties: {
        message: { example: 'Too many requests. Please try again later.' },
        statusCode: { type: 'number', example: 429 },
        error: { example: 'Too Many Requests' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error',
    type: ResponseErrorDto,
  })
  @Throttle({ sendCode: { ttl: 300, limit: 5 } })
  async verifyAdminForgotPasswordCode(
    @Body() payload: VerifyForgotPasswordDto,
  ): Promise<{ signature: string; expires: number }> {
    const { email, code } = payload;

    // Check if user exists and is admin/staff before calling verifyForgotPasswordCode
    const user = await this.userService.getVerifiedUserByEmail(email);
    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STAFF) {
      throw new NotFoundException({
        code: 'USER_NOT_FOUND',
      } as ErrorResponse);
    }

    return await this.userService.verifyForgotPasswordCode(email, code);
  }

  @Post('admin/reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reset password for Admin/Staff with signed URL',
    description:
      'Resets admin or staff password using a signed URL with email, signature, and expiration time. Only works for ADMIN and STAFF users. Requires valid signed URL parameters.',
  })
  @ApiQuery({
    name: 'email',
    required: true,
    description: 'Admin/Staff email address',
  })
  @ApiQuery({
    name: 'signature',
    required: true,
    description: 'Signed URL signature for verification',
  })
  @ApiQuery({
    name: 'expires',
    required: true,
    description: 'URL expiration timestamp',
  })
  @ApiBody({
    type: ResetPasswordDto,
    description: 'New password for the admin/staff account',
  })
  @ApiBadRequestResponse({
    description: 'Invalid signed URL or expired signature',
    type: ResponseBadRequestDto,
  })
  @ApiUnprocessableEntityResponse({
    description: 'Invalid password format or validation errors',
    type: ResponseUnprocessableEntityDto,
  })
  @ApiNotFoundResponse({
    description:
      'Admin/Staff not found, account not activated, or account blocked',
    schema: {
      oneOf: [
        {
          properties: {
            code: { enum: ['USER_NOT_FOUND'] },
            message: { example: 'User not found' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_NOT_ACTIVATED'] },
            message: { example: 'User account is not activated' },
          },
        },
        {
          properties: {
            code: { enum: ['USER_BLOCKED'] },
            message: { example: 'User account is blocked' },
          },
        },
      ],
    },
  })
  @ApiForbiddenResponse({
    description: 'Invalid or expired signed URL, or user is not admin/staff',
    type: ResponseForbiddenDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during password reset',
    type: ResponseErrorDto,
  })
  @UseGuards(SignedUrlGuard)
  async resetAdminPassword(
    @Body() payload: ResetPasswordDto,
    @Query('email') email: string,
    @Query('signature') signature: string,
    @Query('expires') expires: string,
  ): Promise<boolean> {
    // Check if user exists and is admin/staff before calling resetPassword
    const user = await this.userService.getVerifiedUserByEmail(email);
    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STAFF) {
      throw new NotFoundException({
        code: 'USER_NOT_FOUND',
      } as ErrorResponse);
    }

    await this.userService.resetPassword(email, payload.newPassword);
    return true;
  }

  private _signIn(
    request: RequestWithUser,
    redirectTo?: string,
  ): RetrieveAuthDto {
    const { user, session } = request;
    const {
      accessToken,
      refreshToken,
      accessTokenCookie,
      refreshTokenCookie,
      accessTokenExpiresIn,
      refreshTokenExpiresIn,
    } = this.authService.signIn(user.id);

    if (session?.redirectTo) {
      if (redirectTo) {
        redirectTo += `${redirectTo.includes('?') ? '&' : '?'}redirectTo=${encodeURIComponent(session.redirectTo)}`;
      } else {
        redirectTo = session.redirectTo;
      }
    } else if (user?.redirectTo) {
      if (redirectTo) {
        redirectTo += `${redirectTo.includes('?') ? '&' : '?'}redirectTo=${encodeURIComponent(user.redirectTo)}`;
      }
    }

    // Update user was logged into session and destroy redirect to value
    request.session.user = { id: user.id, username: user.username } as User;
    request.session.redirectTo = '';

    redirectTo = ''; // Clear redirectTo value

    // Update user was logged into cookie
    request.res.setHeader('Set-Cookie', [
      accessTokenCookie,
      refreshTokenCookie,
    ]);

    return plainToInstance(RetrieveAuthDto, {
      user,
      accessToken,
      refreshToken,
      redirectTo,
      accessTokenExpiresIn,
      refreshTokenExpiresIn,
    });
  }
}
