import {
  USER_NOTIFICATION_STATUS,
  USER_NOTIFICATION_TYPE,
} from '@app/shared/database/entities';
import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateUserNotificationsTable1703489103841
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'user_notifications',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isUnique: true,
            generationStrategy: 'uuid',
            default: `uuid_generate_v4()`,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isUnique: false,
            isNullable: false,
          },
          {
            name: 'device_token',
            type: 'varchar(255)',
            isUnique: false,
            isNullable: false,
          },
          {
            name: 'title',
            type: 'varchar(255)',
            isNullable: false,
          },
          {
            name: 'content',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '30',
            default: `'${USER_NOTIFICATION_TYPE.NOTIFICATION}'`,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '30',
            default: `'${USER_NOTIFICATION_STATUS.UNSENT}'`,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: null,
            isNullable: true,
          },
        ],
      }),
    );

    await queryRunner.createIndex(
      'user_notifications',
      new TableIndex({
        name: 'idx_user_notifications_user_id',
        columnNames: ['user_id'],
      }),
    );

    await queryRunner.createIndex(
      'user_notifications',
      new TableIndex({
        name: 'idx_user_notifications_status',
        columnNames: ['status'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('user_notifications');
  }
}
