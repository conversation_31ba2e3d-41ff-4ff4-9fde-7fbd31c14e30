import { BONUS_POINTS_RATIO } from '@app/shared/constants';
import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddBonusPointRatioToOrders1757472557404
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'orders',
      new TableColumn({
        name: 'bonus_point_ratio',
        type: 'float',
        default: BONUS_POINTS_RATIO,
        isNullable: false,
        comment:
          'Bonus point ratio used to calculate bonus points for this order',
      }),
    );

    // Update bonus_point_ratio for all existing orders
    // Reverse formula: bonus_point_ratio = bonus_points / total_amount (rounded to 2 decimal places)
    await queryRunner.query(`
      UPDATE orders 
      SET bonus_point_ratio = CAST(ROUND((bonus_points::float / total_amount::float) * 100) AS FLOAT) / 100
      WHERE bonus_points > 0 AND total_amount > 0;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('orders', 'bonus_point_ratio');
  }
}
