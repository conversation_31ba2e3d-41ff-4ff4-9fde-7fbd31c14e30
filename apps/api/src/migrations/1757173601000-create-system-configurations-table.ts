import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';
import { SYSTEM_SETTING_KEY } from '@app/system/const/system.constant';

export class CreateSystemConfigurationsTable1757173601000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'system_configurations',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isUnique: true,
            generationStrategy: 'uuid',
            default: `uuid_generate_v4()`,
          },
          {
            name: 'key',
            type: 'varchar',
            length: '255',
            isUnique: true,
            isNullable: false,
            comment: 'Configuration key (unique)',
          },
          {
            name: 'value',
            type: 'text',
            isNullable: false,
            comment: 'Configuration value',
          },
          {
            name: 'type',
            type: 'varchar',
            length: '50',
            isNullable: false,
            default: "'string'",
            comment: 'Value type: string, number, boolean, json',
          },
          {
            name: 'category',
            type: 'varchar',
            length: '100',
            isNullable: false,
            default: "'system'",
            comment: 'Configuration category: notification, session, system',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
            comment: 'Configuration description',
          },
          {
            name: 'is_active',
            type: 'boolean',
            isNullable: false,
            default: true,
            comment: 'Configuration active status',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
            comment: 'Configuration creation time',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            comment: 'Configuration update time',
          },
        ],
      }),
    );

    // Create indexes for better performance
    await queryRunner.createIndex(
      'system_configurations',
      new TableIndex({
        name: 'idx_system_configurations_key',
        columnNames: ['key'],
      }),
    );

    await queryRunner.createIndex(
      'system_configurations',
      new TableIndex({
        name: 'idx_system_configurations_category',
        columnNames: ['category'],
      }),
    );

    await queryRunner.createIndex(
      'system_configurations',
      new TableIndex({
        name: 'idx_system_configurations_active',
        columnNames: ['is_active'],
      }),
    );

    await queryRunner.createIndex(
      'system_configurations',
      new TableIndex({
        name: 'idx_system_configurations_category_active',
        columnNames: ['category', 'is_active'],
      }),
    );

    // Insert some default system configurations
    await queryRunner.query(`
      INSERT INTO system_configurations (key, value, type, category, description, is_active) VALUES
      ('${SYSTEM_SETTING_KEY.BONUS_POINT_RATIO}', '0.02', 'number', 'payment', 'Bonus point ratio (0.02 = 2%)', true),
      ('${SYSTEM_SETTING_KEY.EXCHANGE_RATE_VND}', '1000', 'number', 'payment', 'Exchange rate for VND currency', true)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('system_configurations');
  }
}
