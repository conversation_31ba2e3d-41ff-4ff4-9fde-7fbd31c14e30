import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddBonusPointToOrders1756354848185 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'orders',
      new TableColumn({
        name: 'bonus_points',
        type: 'int',
        default: 0,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('orders', 'bonus_points');
  }
}
