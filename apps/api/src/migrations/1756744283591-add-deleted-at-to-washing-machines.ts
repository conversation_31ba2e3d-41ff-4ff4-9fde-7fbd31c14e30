import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddUpdateAtAndDeletedAtToWashingMachines1756744283591
  implements MigrationInterface
{
  name = 'AddUpdateAtAndDeletedAtToWashingMachines1756744283591';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add columns to washing_machines table
    await queryRunner.addColumn(
      'washing_machines',
      new TableColumn({
        name: 'updated_at',
        type: 'timestamp',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      'washing_machines',
      new TableColumn({
        name: 'deleted_at',
        type: 'timestamp',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      'machine_programs',
      new TableColumn({
        name: 'deleted_at',
        type: 'timestamp',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      'products',
      new TableColumn({
        name: 'deleted_at',
        type: 'timestamp',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('products', 'deleted_at');

    await queryRunner.dropColumn('machine_programs', 'deleted_at');

    await queryRunner.dropColumn('washing_machines', 'deleted_at');

    await queryRunner.dropColumn('washing_machines', 'updated_at');
  }
}
