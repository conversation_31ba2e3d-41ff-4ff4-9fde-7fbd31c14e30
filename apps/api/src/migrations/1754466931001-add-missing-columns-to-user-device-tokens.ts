import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddMissingColumnsToUserDeviceTokens1754466931001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('user_device_tokens', [
      new TableColumn({
        name: 'device_os',
        type: 'varchar',
        length: '50',
        isNullable: true,
      }),
      new TableColumn({
        name: 'device_code',
        type: 'varchar',
        length: '50',
        isNullable: true,
      }),
      new TableColumn({
        name: 'device_type',
        type: 'varchar',
        length: '50',
        isNullable: true,
      }),
      new TableColumn({
        name: 'device_name',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'app_version',
        type: 'varchar',
        length: '50',
        isNullable: true,
      }),
      new TableColumn({
        name: 'os_version',
        type: 'varchar',
        length: '50',
        isNullable: true,
      }),
      new TableColumn({
        name: 'is_active',
        type: 'boolean',
        isNullable: false,
        default: true,
      }),
      new TableColumn({
        name: 'last_active_at',
        type: 'timestamp',
        isNullable: true,
      }),
      new TableColumn({
        name: 'updated_at',
        type: 'timestamp',
        isNullable: true,
        default: null,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('user_device_tokens', 'device_os');
    await queryRunner.dropColumn('user_device_tokens', 'device_code');
    await queryRunner.dropColumn('user_device_tokens', 'updated_at');
    await queryRunner.dropColumn('user_device_tokens', 'last_active_at');
    await queryRunner.dropColumn('user_device_tokens', 'is_active');
    await queryRunner.dropColumn('user_device_tokens', 'os_version');
    await queryRunner.dropColumn('user_device_tokens', 'app_version');
    await queryRunner.dropColumn('user_device_tokens', 'device_name');
    await queryRunner.dropColumn('user_device_tokens', 'device_type');
  }
}
