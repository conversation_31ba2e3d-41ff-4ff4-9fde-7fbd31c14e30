import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableIndex,
} from 'typeorm';

export class UpdateGatewayTransactionIdLength1755071400000
  implements MigrationInterface
{
  private readonly tableName = 'payment_transactions';
  private readonly columnName = 'gateway_transaction_id';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      this.tableName,
      this.columnName,
      new TableColumn({
        name: this.columnName,
        type: 'varchar',
        length: '100',
        isNullable: true,
        isUnique: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      this.tableName,
      this.columnName,
      new TableColumn({
        name: this.columnName,
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    );
  }
}
