import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddIsAllowedNotifyIntoUsersTable1754466932002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn('users', 'is_allowed_notify');
    if (!hasColumn) {
      await queryRunner.addColumn(
        'users',
        new TableColumn({
          name: 'is_allowed_notify',
          type: 'boolean',
          isNullable: false,
          default: true,
        }),
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn('users', 'is_allowed_notify');
    if (hasColumn) {
      await queryRunner.dropColumn('users', 'is_allowed_notify');
    }
  }
}
