import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRemindedAtToServiceSessions1755071500000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn(
      'service_sessions',
      'reminded_at',
    );
    if (!hasColumn) {
      await queryRunner.addColumn(
        'service_sessions',
        new TableColumn({
          name: 'reminded_at',
          type: 'timestamp',
          isNullable: true,
        }),
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn(
      'service_sessions',
      'reminded_at',
    );
    if (hasColumn) {
      await queryRunner.dropColumn('service_sessions', 'reminded_at');
    }
  }
}
