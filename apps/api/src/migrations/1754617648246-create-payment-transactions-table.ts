import { PaymentMethod } from '@app/shared/database/entities/order.entity';
import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreatePaymentTransactionsTable1754617648246
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'payment_transactions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'order_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '50',
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'point',
            type: 'int',
            isNullable: false,
            default: 0,
          },
          {
            name: 'exchange_rate',
            type: 'float',
            isNullable: false,
          },
          {
            name: 'currency',
            type: 'varchar',
            length: '10',
            isNullable: false,
            default: `'VND'`,
          },
          {
            name: 'payment_method',
            type: 'varchar',
            length: '50',
            default: `'${PaymentMethod.CASH}'`,
            isNullable: false,
          },
          {
            name: 'gateway_transaction_id',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: `'pending'`,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'gateway_response',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
    );

    await queryRunner.createIndices('payment_transactions', [
      new TableIndex({
        name: 'IDX_payment_transactions_user_id',
        columnNames: ['user_id'],
      }),
      new TableIndex({
        name: 'IDX_payment_transactions_order_id',
        columnNames: ['order_id'],
      }),
      new TableIndex({
        name: 'IDX_payment_transactions_status',
        columnNames: ['status'],
      }),
      new TableIndex({
        name: 'IDX_payment_transactions_created_at',
        columnNames: ['created_at'],
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('payment_transactions');
  }
}
