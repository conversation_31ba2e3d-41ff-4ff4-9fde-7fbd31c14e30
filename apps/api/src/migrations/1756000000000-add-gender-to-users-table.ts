import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddGenderToUsersTable1756000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn('users', 'gender');
    if (!hasColumn) {
      await queryRunner.addColumn(
        'users',
        new TableColumn({
          name: 'gender',
          type: 'varchar',
          length: '30',
          isNullable: true,
        }),
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn('users', 'gender');
    if (hasColumn) {
      await queryRunner.dropColumn('users', 'gender');
    }
  }
}
