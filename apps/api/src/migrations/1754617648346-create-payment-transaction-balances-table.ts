import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreatePaymentTransactionBalancesTable1754617648346
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'payment_transaction_balances',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'transaction_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'balance_before',
            type: 'int',
            isNullable: false,
            default: 0,
          },
          {
            name: 'balance_after',
            type: 'int',
            isNullable: false,
            default: 0,
          },
          {
            name: 'amount_change',
            type: 'int',
            isNullable: false,
            default: 0,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
    );

    await queryRunner.createIndices('payment_transaction_balances', [
      new TableIndex({
        name: 'IDX_ptb_user_id',
        columnNames: ['user_id'],
      }),
      new TableIndex({
        name: 'IDX_ptb_transaction_id',
        columnNames: ['transaction_id'],
      }),
      new TableIndex({
        name: 'IDX_ptb_created_at',
        columnNames: ['created_at'],
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('payment_transaction_balances');
  }
}
