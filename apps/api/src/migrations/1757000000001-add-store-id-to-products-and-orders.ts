import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableIndex,
} from 'typeorm';

export class AddStoreIdToProducts1757000000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add store_id to products table only
    await queryRunner.addColumn(
      'products',
      new TableColumn({
        name: 'store_id',
        type: 'uuid',
        isNullable: true,
      }),
    );

    // Add index for products.store_id for better performance
    await queryRunner.createIndex(
      'products',
      new TableIndex({
        name: 'IDX_PRODUCTS_STORE_ID',
        columnNames: ['store_id'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index first
    await queryRunner.dropIndex('products', 'IDX_PRODUCTS_STORE_ID');

    // Drop column
    await queryRunner.dropColumn('products', 'store_id');
  }
}
