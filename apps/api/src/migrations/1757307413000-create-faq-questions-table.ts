import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateFaqQuestionsTable1757307413000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'faq_questions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'gen_random_uuid()',
          },
          {
            name: 'parent_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '50',
            isNullable: true,
            comment: 'Question type: faq, help, tutorial',
          },
          {
            name: 'slug',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment: 'URL-friendly identifier (unique per master record)',
          },
          {
            name: 'language',
            type: 'varchar',
            length: '10',
            isNullable: true,
            comment:
              'Language code (NULL for master record, en/vi/ja for language records)',
          },
          {
            name: 'title',
            type: 'varchar',
            length: '500',
            isNullable: true,
            comment:
              'Question title (NULL for master record, filled for language records)',
          },
          {
            name: 'content',
            type: 'text',
            isNullable: true,
            comment:
              'Question content/answer (NULL for master record, filled for language records)',
          },
          {
            name: 'display_order',
            type: 'integer',
            default: 0,
            comment: 'Display order for sorting',
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
            comment: 'Question active status',
          },
          {
            name: 'is_expanded',
            type: 'boolean',
            default: false,
            comment: 'Default expanded state',
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
            comment: 'Additional question metadata',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: 'Question creation time',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            comment: 'Question update time',
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
            comment: 'Question deletion time',
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_parent_id',
        columnNames: ['parent_id'],
      }),
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_language',
        columnNames: ['language'],
      }),
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_type',
        columnNames: ['type'],
      }),
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_slug',
        columnNames: ['slug'],
      }),
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_display_order',
        columnNames: ['display_order'],
      }),
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_is_active',
        columnNames: ['is_active'],
      }),
    );

    await queryRunner.createIndex(
      'faq_questions',
      new TableIndex({
        name: 'idx_faq_questions_language_active',
        columnNames: ['language', 'is_active'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('faq_questions');
  }
}
