import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserDeviceTokenLength1757998492244
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update device_code column length from 50 to 255
    await queryRunner.query(
      `ALTER TABLE "user_device_tokens" ALTER COLUMN "device_code" TYPE varchar(255)`,
    );

    // Update device_type column length from 50 to 255
    await queryRunner.query(
      `ALTER TABLE "user_device_tokens" ALTER COLUMN "device_type" TYPE varchar(255)`,
    );

    // Update app_version column length from 50 to 255
    await queryRunner.query(
      `ALTER TABLE "user_device_tokens" ALTER COLUMN "app_version" TYPE varchar(255)`,
    );

    // Update os_version column length from 50 to 255
    await queryRunner.query(
      `ALTER TABLE "user_device_tokens" ALTER COLUMN "os_version" TYPE varchar(255)`,
    );

    // Update device_os column length from 50 to 255
    await queryRunner.query(
      `ALTER TABLE "user_device_tokens" ALTER COLUMN "device_os" TYPE varchar(255)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(``);
  }
}
