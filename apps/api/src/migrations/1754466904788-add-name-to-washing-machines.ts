import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddNameToWashingMachines1754466904788
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'washing_machines',
      new TableColumn({
        name: 'name',
        type: 'varchar',
        length: '255',
        isNullable: false,
        default: "'Washing machine'",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('washing_machines', 'name');
  }
}
