import { OrderStatus, PaymentMethod } from '@app/shared/database/entities';
import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateOrdersTable1754617369284 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'orders',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'code',
            type: 'varchar',
            length: '50',
            isUnique: true,
          },
          { name: 'user_id', type: 'uuid', isNullable: false },
          { name: 'total_points', type: 'int', isNullable: false },
          { name: 'total_amount', type: 'int', isNullable: false },
          {
            name: 'currency',
            type: 'varchar',
            length: '10',
            default: "'VND'",
            isNullable: false,
          },
          {
            name: 'exchange_rate',
            type: 'float',
            default: 1000.0,
            isNullable: false,
          },
          {
            name: 'payment_method',
            type: 'varchar',
            length: '20',
            default: `'${PaymentMethod.CASH}'`,
            isNullable: false,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            default: `'${OrderStatus.PENDING}'`,
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
    );

    // Indexes instead of foreign keys
    await queryRunner.createIndices('orders', [
      new TableIndex({ name: 'IDX_orders_user_id', columnNames: ['user_id'] }),
      new TableIndex({ name: 'IDX_orders_status', columnNames: ['status'] }),
      new TableIndex({
        name: 'IDX_orders_created_at',
        columnNames: ['created_at'],
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('orders');
  }
}
