import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateServiceSessionsTable1754466930000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'service_sessions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isUnique: true,
            generationStrategy: 'uuid',
            default: `uuid_generate_v4()`,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'order_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'order_item_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'machine_id',
            type: 'uuid',
            isNullable: true, // Optional for service items
          },
          {
            name: 'machine_program_id',
            type: 'uuid',
            isNullable: true, // Optional for non-service items
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            default: `'waiting'`,
            isNullable: false,
          },
          {
            name: 'started_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'estimated_end_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'completed_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
            isNullable: false,
          },
        ],
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'service_sessions',
      new TableIndex({
        name: 'idx_service_sessions_user_id',
        columnNames: ['user_id'],
      }),
    );

    await queryRunner.createIndex(
      'service_sessions',
      new TableIndex({
        name: 'idx_service_sessions_order_id',
        columnNames: ['order_id'],
      }),
    );

    await queryRunner.createIndex(
      'service_sessions',
      new TableIndex({
        name: 'idx_service_sessions_order_item_id',
        columnNames: ['order_item_id'],
      }),
    );

    await queryRunner.createIndex(
      'service_sessions',
      new TableIndex({
        name: 'idx_service_sessions_machine_id',
        columnNames: ['machine_id'],
      }),
    );

    await queryRunner.createIndex(
      'service_sessions',
      new TableIndex({
        name: 'idx_service_sessions_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'service_sessions',
      new TableIndex({
        name: 'idx_service_sessions_created_at',
        columnNames: ['created_at'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop table
    await queryRunner.dropTable('service_sessions');
  }
}
