import {
  USER_LANGUAGE,
  UserRole,
  UserStatus,
} from '@app/shared/database/entities';
import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateUserTable1701758370852 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isUnique: true,
            generationStrategy: 'uuid',
            default: `uuid_generate_v4()`,
          },
          {
            name: 'username',
            type: 'varchar',
            length: '254',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '254',
            isNullable: false,
          },
          {
            name: 'email',
            type: 'varchar',
            length: '254',
            isUnique: true,
            isNullable: true,
          },
          {
            name: 'phone',
            type: 'varchar',
            length: '254',
            isUnique: true,
            isNullable: true,
          },
          {
            name: 'password',
            type: 'varchar',
            length: '128',
          },
          {
            name: 'avatar',
            type: 'varchar',
            length: '254',
            isNullable: true,
          },
          {
            name: 'role',
            type: 'varchar',
            length: '30',
            default: `'${UserRole.CUSTOMER}'`,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '30',
            default: `'${UserStatus.INACTIVE}'`,
          },
          {
            name: 'language',
            type: 'varchar',
            length: '30',
            default: `'${USER_LANGUAGE.EN}'`,
          },
          {
            name: 'birthday',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'verified_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: null,
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            default: null,
            isNullable: true,
          },
        ],
      }),
    );

    await queryRunner.createIndex(
      'users',
      new TableIndex({
        name: 'idx_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'users',
      new TableIndex({
        name: 'idx_role',
        columnNames: ['role'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('users');
  }
}
