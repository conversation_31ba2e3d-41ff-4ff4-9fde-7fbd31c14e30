import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';

export class CreateTableUserSocials1717468350360 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'user_socials',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isUnique: true,
            generationStrategy: 'uuid',
            default: `uuid_generate_v4()`,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'social_id',
            type: 'varchar(64)',
            isNullable: false,
          },
          {
            name: 'provider',
            type: 'varchar',
            length: '30',
            default: `'${SOCIAL_PROVIDER.FACEBOOK}'`,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
          },
        ],
      }),
    );

    await queryRunner.createIndex(
      'user_socials',
      new TableIndex({
        name: 'idx_user_socials_user_id',
        columnNames: ['user_id'],
      }),
    );

    await queryRunner.createIndex(
      'user_socials',
      new TableIndex({
        name: 'idx_user_socials_provider',
        columnNames: ['provider'],
      }),
    );

    await queryRunner.createIndex(
      'user_socials',
      new TableIndex({
        name: 'idx_user_socials_social_id',
        columnNames: ['social_id'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('user_socials');
  }
}
