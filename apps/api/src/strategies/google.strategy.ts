import { Request } from 'express';
import { Injectable, NotFoundException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { Profile, Strategy, VerifyCallback } from 'passport-google-oauth20';
import { parseStateQueryParam } from '@app/shared/helpers/url.helper';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';
import { UserService } from '@app/user/services/user.service';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class GoogleStrategy extends PassportStrategy(
  Strategy,
  SOCIAL_PROVIDER.GOOGLE,
) {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
  ) {
    const config = configService.get('SOCIAL.GOOGLE');
    super({
      ...config,
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: VerifyCallback,
  ): Promise<any> {
    const { id, emails, name, photos = [] } = profile;
    const user = await this.userService.getAuthenticatedViaSocial({
      provider: SOCIAL_PROVIDER.GOOGLE,
      socialId: id,
      email: emails[0]?.value ?? null,
      name: name.givenName + ' ' + name.familyName,
      avatar: photos[0]?.value,
      isSkipCreateUser: false,
    });
    if (!user) {
      throw new NotFoundException({
        code: 'SOCIAL_ACCOUNT_NOT_FOUND',
      } as ErrorResponse);
    }

    return done(null, {
      ...user,
      ...parseStateQueryParam((req?.query?.state ?? '') as string),
    });
  }
}
