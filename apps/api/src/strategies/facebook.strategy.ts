import { Injectable, NotFoundException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Profile, Strategy } from 'passport-facebook';
import { ConfigService } from '@nestjs/config';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';
import { Request } from 'express';
import { parseStateQueryParam } from '@app/shared/helpers/url.helper';
import { UserService } from '@app/user/services/user.service';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class FacebookStrategy extends PassportStrategy(
  Strategy,
  SOCIAL_PROVIDER.FACEBOOK,
) {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
  ) {
    const config = configService.get('SOCIAL.FACEBOOK');
    super({
      ...config,
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: (error: any, user?: any, info?: any) => void,
  ) {
    const { name, id } = profile;
    const user = await this.userService.getAuthenticatedViaSocial({
      provider: SOCIAL_PROVIDER.FACEBOOK,
      socialId: id,
      name: name.givenName + ' ' + name.familyName,
      email: null,
      avatar: null,
      isSkipCreateUser: false,
    });
    if (!user) {
      throw new NotFoundException({
        code: 'SOCIAL_ACCOUNT_NOT_FOUND',
      } as ErrorResponse);
    }

    return done(null, {
      ...user,
      ...parseStateQueryParam((req?.query?.state ?? '') as string),
    });
  }
}
