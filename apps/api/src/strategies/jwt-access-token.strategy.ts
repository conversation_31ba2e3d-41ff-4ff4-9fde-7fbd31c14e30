import {
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { TokenPayload } from '../../../../libs/auth/src/interfaces/token.interface';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../../../../libs/auth/src/auth.service';
import { getUserIdFromRequestHeader } from '@app/shared/helpers/request.helper';
import { UserRepositoryInterface } from '@app/shared/database/repositories';

@Injectable()
export class JwtAccessTokenStrategy extends PassportStrategy(Strategy) {
  logger = new Logger(JwtAccessTokenStrategy.name);
  constructor(
    protected readonly configService: ConfigService,
    protected readonly authService: AuthService,
    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors(
        authService.getJwtAccessTokenFromExtractors(),
      ),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT.ACCESS_TOKEN_SECRET'),
      passReqToCallback: true,
    });
  }

  async authenticate(request: Request, options?: any): Promise<void> {
    // Bypass strategy when API Gateway Authorizer is enabled
    const userId = getUserIdFromRequestHeader(request);
    this.logger.debug(`x-amz-apigw-auth-user: ${userId}`);
    if (userId) {
      try {
        const user = await this.getUserById(userId as string);
        if (!user) {
          return this.fail(
            new UnauthorizedException('User not found or inactive'),
            401,
          );
        }
        return this.success(user);
      } catch (error) {
        return this.error(error);
      }
    }
    return super.authenticate(request, options);
  }

  async validate(request: Request, payload: TokenPayload) {
    this.logger.debug(`payload: ${JSON.stringify(payload)}`);

    // Get the token from the request to check blacklist
    const token = this.authService.getJwtAccessTokenFromRequest(request);

    if (token) {
      // Check if token is blacklisted
      const isBlacklisted = await this.authService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        this.logger.warn(`Token is blacklisted: ${token.substring(0, 20)}...`);
        throw new UnauthorizedException('Token has been revoked');
      }
    }

    try {
      const user = await this.getUserById(payload.userId);
      if (!user) {
        throw new UnauthorizedException('User not found or inactive');
      }
      return user;
    } catch (error) {
      this.logger.error(`Error validating token: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  private async getUserById(userId: string) {
    return await this.userRepository.findOneById(userId);
  }
}
