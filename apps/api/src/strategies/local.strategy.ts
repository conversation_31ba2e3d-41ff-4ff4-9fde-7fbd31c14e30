import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { Injectable, NotFoundException } from '@nestjs/common';
import { UserService } from '@app/user/services/user.service';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({ usernameField: 'username' });
  }

  async validate(username: string, password: string) {
    const user = await this.userService.getAuthenticated(username, password);
    if (!user) {
      throw new NotFoundException({ code: 'USER_NOT_FOUND' } as ErrorResponse);
    }
    return user;
  }
}
