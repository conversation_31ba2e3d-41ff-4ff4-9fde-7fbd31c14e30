import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, Profile, VerifyCallback } from 'passport-line';
import { ConfigService } from '@nestjs/config';
import { SOCIAL_PROVIDER } from '@app/shared/database/entities/user-social.entity';
import { Request } from 'express';
import { UserRepositoryInterface } from '@app/shared/database/repositories';
import { AppLogger } from '@app/shared/logger/app.logger';
import { isEmpty } from 'lodash';
import { UserService } from '@app/user/services/user.service';
import { AuthService } from '@app/auth';
import { ErrorResponse } from '@app/shared/types';

@Injectable()
export class LineStrategy extends PassportStrategy(
  Strategy,
  SOCIAL_PROVIDER.LINE,
) {
  constructor(
    private readonly logger: AppLogger,
    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
    private readonly userService: UserService,
  ) {
    const config = configService.get('SOCIAL.LINE');
    super({
      ...config,
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: VerifyCallback,
  ): Promise<any> {
    const token = this.authService.getJwtAccessTokenFromRequest(req);
    let userId = token ? this.authService.getUserIdFromAccessToken(token) : '';
    const isSocialLink = !!userId;
    this.logger.debug(`current token: ${token}`);
    this.logger.debug(`current is social link: ${isSocialLink}`);
    if (userId) {
      if (!(await this.userRepository.exists({ id: userId }))) {
        userId = '';
      }
    }
    this.logger.debug(`current user id: ${userId}`);
    const { id, displayName, pictureUrl, emails = [] } = profile;

    const user = await this.userService.getAuthenticatedViaSocial({
      provider: SOCIAL_PROVIDER.LINE,
      socialId: id,
      email: emails[0]?.value ?? null,
      name: displayName,
      avatar: pictureUrl,
      userId,
      isSkipCreateUser: false,
    });
    if (!user) {
      throw new NotFoundException({
        code: 'SOCIAL_ACCOUNT_NOT_FOUND',
      } as ErrorResponse);
    }

    const redirectTo = req?.query?.redirectTo ?? '';

    return done(null, {
      ...user,
      isSocialLink,
      ...(!isEmpty(redirectTo)
        ? { redirectTo: decodeURIComponent(redirectTo as string) }
        : {}),
    });
  }
}
