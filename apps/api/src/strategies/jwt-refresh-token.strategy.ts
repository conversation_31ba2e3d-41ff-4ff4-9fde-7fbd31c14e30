import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { TokenPayload } from '@app/auth/interfaces/token.interface';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '@app/auth';
import { UserService } from '@app/user/services/user.service';
import { Request } from 'express';

@Injectable()
export class JwtRefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'refresh_token',
) {
  private readonly logger = new Logger(JwtRefreshTokenStrategy.name);

  constructor(
    protected readonly configService: ConfigService,
    protected readonly authService: AuthService,
    protected readonly userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors(
        authService.getJwtRefreshTokenFromExtractors(),
      ),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT.REFRESH_TOKEN_SECRET'),
      passReqToCallback: true,
    });
  }

  async validate(request: Request, payload: TokenPayload) {
    this.logger.debug(`Refresh token payload: ${JSON.stringify(payload)}`);

    // Get the refresh token from the request to check blacklist
    const refreshToken =
      this.authService.getJwtRefreshTokenFromRequest(request);

    if (refreshToken) {
      // Check if refresh token is blacklisted
      const isBlacklisted =
        await this.authService.isRefreshTokenBlacklisted(refreshToken);
      if (isBlacklisted) {
        this.logger.warn(
          `Refresh token is blacklisted: ${refreshToken.substring(0, 20)}...`,
        );
        throw new UnauthorizedException('Refresh token has been revoked');
      }
    }

    try {
      const user = await this.userService.getByJwtToken(payload.userId);
      this.logger.debug(`User found: ${user.id}`);
      return user;
    } catch (error) {
      this.logger.debug(
        `User not found for userId: ${payload.userId}, error: ${error.message}`,
      );
      // If user not found, return null to trigger 401
      return null;
    }
  }
}
