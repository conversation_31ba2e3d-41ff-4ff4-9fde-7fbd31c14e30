import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { AllExceptionsFilter } from '@app/shared/exceptions';
import { BootstrapModule } from '@app/shared/bootstrap/bootstrap.module';
import { AuthModule } from '@app/auth/auth.module';
import { SessionModule } from '@app/shared/session/session.module';
import { LoggingInterceptor } from '@app/shared/interceptors';
import { ClsModule } from 'nestjs-cls';
import { v4 } from 'uuid';
import { AuthController } from './controllers/auth.controller';
import { AppController } from './app.controller';
import { DatabaseModule } from '@app/shared/database/database.module';
import { UserModule } from '@app/user/user.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserController } from './controllers/user.controller';
import { PaymentController } from './controllers/payment.controller';
import { JwtRefreshTokenStrategy } from './strategies/jwt-refresh-token.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { FacebookStrategy } from './strategies/facebook.strategy';
import { GoogleStrategy } from './strategies/google.strategy';
import { LineStrategy } from './strategies/line.strategy';
import { JwtAccessTokenStrategy } from './strategies/jwt-access-token.strategy';
import { AppThrottlerModule } from '@app/shared/throttler';
import { StoreModule } from '@app/store/store.module';
import { WashingMachineController } from './controllers/washing-machine.controller';
import { OrderModule } from '@app/order/order.module';
import { PaymentModule } from '@app/payment/payment.module';
import { OrderController } from './controllers/order.controller';
import { UserDeviceController } from './controllers/user-device.controller';
import { SystemModule } from '@app/system/system.module';
import { SystemController } from './controllers/system.controller';
import { FaqController } from './controllers/faq.controller';
import { AwsModule } from '@app/aws';

@Module({
  imports: [
    // A continuation-local storage module
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        setup(cls) {
          cls.set('logId', v4());
        },
      },
    }),
    BootstrapModule,
    DatabaseModule,
    SessionModule,
    EventEmitterModule.forRoot(),
    AuthModule,
    UserModule,
    StoreModule,
    PaymentModule,
    OrderModule,
    SystemModule,
    AwsModule,
    AppThrottlerModule,
  ],
  controllers: [
    AppController,
    AuthController,
    UserController,
    WashingMachineController,
    PaymentController,
    OrderController,
    UserDeviceController,
    SystemController,
    FaqController,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    LocalStrategy,
    FacebookStrategy,
    GoogleStrategy,
    LineStrategy,
    JwtAccessTokenStrategy,
    JwtRefreshTokenStrategy,
  ],
})
export class AppModule {}
