import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import appConfig from '@app/shared/config/app.config';
import { AppLogger } from '@app/shared/logger/app.logger';
import { ScheduleService } from './services/schedule.service';
import { ClsService } from 'nestjs-cls';
import { handleUncaughtExceptions } from '@app/shared/exceptions';
import { CronExpression } from '@nestjs/schedule';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  const logger = app.get(AppLogger);
  const clsService = app.get(ClsService);

  // Logger
  app.useLogger(logger);

  const scheduleService: ScheduleService = app
    .select(AppModule)
    .get(ScheduleService, {
      strict: true,
    });

  scheduleService.scheduleNotifyUserEndingSession(
    appConfig().CRONJOB.REMIND_ENDING_SESSION as CronExpression,
  );

  scheduleService.scheduleNotifyUserCompleteSession(
    appConfig().CRONJOB.REMIND_COMPLETED_SESSION as CronExpression,
  );

  scheduleService.scheduleMarkDoneSessions(
    appConfig().CRONJOB.MARK_DONE_SESSION as CronExpression,
  );

  scheduleService.scheduleRemindAfterCompletedSession(
    appConfig().CRONJOB.REMIND_AFTER_COMPLETED_SESSION as CronExpression,
  );

  scheduleService.scheduleCancelOrder(
    appConfig().CRONJOB.CANCEL_ORDER as CronExpression,
  );

  // Starts listening for shutdown hooks
  app.enableShutdownHooks();

  // await all hook init (onModuleInit, onApplicationBootstrap, v.v) success
  await app.init();

  handleUncaughtExceptions(logger, clsService);
}
bootstrap();
