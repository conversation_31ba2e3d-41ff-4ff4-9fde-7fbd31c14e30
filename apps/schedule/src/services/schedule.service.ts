import { Inject, Injectable } from '@nestjs/common';
import { AppLogger } from '@app/shared/logger/app.logger';
import { v4 } from 'uuid';
import { ClsService } from 'nestjs-cls';
import { CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { <PERSON>ronJob } from 'cron';
import { CHUNK_SIZE, TIME_ZONE } from '@app/shared/constants';
import { ServiceSessionService } from '@app/store/services/service-session.service';
import { map, uniq, uniqBy } from 'lodash';
import {
  NotificationService,
  SendNotificationPayload,
} from '@app/notification';
import {
  ServiceSession,
  ServiceSessionRepositoryInterface,
  User,
  USER_LANGUAGE,
  USER_NOTIFICATION_STATUS,
  USER_NOTIFICATION_TYPE,
  UserDeviceTokenRepositoryInterface,
  UserNotification,
  UserNotificationRepositoryInterface,
  UserRepositoryInterface,
  WashingMachineRepositoryInterface,
} from '@app/shared/database';
import { In } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import {
  getLocalizedNotificationMessage,
  NotificationType,
} from '@app/shared/helpers';
import { OrderService } from '@app/order';
import moment from 'moment';

@Injectable()
export class ScheduleService {
  constructor(
    @Inject('ServiceSessionRepositoryInterface')
    private readonly serviceSessionRepository: ServiceSessionRepositoryInterface,
    @Inject('UserDeviceTokenRepositoryInterface')
    private readonly userDeviceTokenRepository: UserDeviceTokenRepositoryInterface,
    @Inject('UserNotificationRepositoryInterface')
    private readonly userNotificationRepository: UserNotificationRepositoryInterface,
    @Inject('WashingMachineRepositoryInterface')
    private readonly washingMachineRepository: WashingMachineRepositoryInterface,
    private readonly logger: AppLogger,
    private readonly clsService: ClsService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly serviceSessionService: ServiceSessionService,
    private readonly orderService: OrderService,
    private readonly notificationService: NotificationService,
    private readonly configService: ConfigService,
  ) {}

  private async runWithLogIdContext(
    jobName: string,
    fn: () => Promise<void> | void,
  ) {
    const logId = v4();
    await this.clsService.run(async () => {
      this.clsService.set('logId', logId);
      this.logger.log(`job ${jobName} started`);
      try {
        await fn();
        this.logger.log(`job ${jobName} completed`);
      } catch (err) {
        this.logger.error(`job ${jobName} failed: ${err.message}`);
      }
    });
  }

  scheduleNotifyUserEndingSession(cronExpression: CronExpression) {
    const name: string = 'schedule-notify-user-ending-session';
    const job = new CronJob(
      `${cronExpression}`,
      async () => {
        this.runWithLogIdContext(name, async () => {
          this.notifyUserEndingSession();
        });
      },
      null,
      true,
      TIME_ZONE.ICT,
    );

    this.schedulerRegistry.addCronJob(`${name}`, job);
    job.start();
  }

  scheduleNotifyUserCompleteSession(cronExpression: CronExpression) {
    const name: string = 'schedule-notify-user-complete-session';
    const job = new CronJob(
      `${cronExpression}`,
      async () => {
        this.runWithLogIdContext(name, async () => {
          this.notifyUserCompleteSession();
        });
      },
      null,
      true,
      TIME_ZONE.ICT,
    );

    this.schedulerRegistry.addCronJob(`${name}`, job);
    job.start();
  }

  scheduleMarkDoneSessions(cronExpression: CronExpression) {
    const name: string = 'schedule-mark-done-sessions';
    const job = new CronJob(
      `${cronExpression}`,
      async () => {
        this.runWithLogIdContext(name, async () => {
          this.markDoneSessions();
        });
      },
      null,
      true,
      TIME_ZONE.ICT,
    );

    this.schedulerRegistry.addCronJob(`${name}`, job);
    job.start();
  }

  scheduleRemindAfterCompletedSession(cronExpression: CronExpression) {
    const name: string = 'schedule-remind-after-completed-session';
    const job = new CronJob(
      `${cronExpression}`,
      async () => {
        this.runWithLogIdContext(name, async () => {
          this.remindAfterCompletedSession();
        });
      },
      null,
      true,
      TIME_ZONE.ICT,
    );

    this.schedulerRegistry.addCronJob(`${name}`, job);
    job.start();
  }

  scheduleCancelOrder(cronExpression: CronExpression) {
    const name: string = 'schedule-cancel-order';
    const job = new CronJob(
      `${cronExpression}`,
      async () => {
        this.runWithLogIdContext(name, async () => {
          this.cancelOrders();
        });
      },
      null,
      true,
      TIME_ZONE.ICT,
    );

    this.schedulerRegistry.addCronJob(`${name}`, job);
    job.start();
  }

  /**
   * Notify user that the session is ending soon
   * Runs every 1 second for 60 seconds total, then breaks to restart job
   * @returns void
   */
  async notifyUserEndingSession() {
    const endingSessionMinutes = this.configService.get(
      'CRONJOB.ENDING_SESSION_MINUTES',
    );
    const limit = CHUNK_SIZE;
    const intervalMs =
      this.configService.get(
        'CRONJOB.REMIND_ENDING_SESSION_INTERVAL_SECONDS',
        1,
      ) * 1000; // Default 1 second interval
    const maxDurationMs = 60000; // 60 seconds total
    const startTime = Date.now();

    this.logger.log(
      `Starting continuous notification check - running every ${intervalMs}ms for 60s`,
    );

    while (Date.now() - startTime < maxDurationMs) {
      const iterationStart = Date.now();

      const endingServiceSessions =
        await this.serviceSessionService.getServiceSessionsEndingIn({
          minutesBefore: endingSessionMinutes,
          limit,
        });

      if (endingServiceSessions.length > 0) {
        this.logger.log(
          `number of sessions ending soon: ${endingServiceSessions.length}`,
        );

        const { userNotifications, messages } =
          await this.buildNotificationPayloadForSessions(
            endingServiceSessions,
            NotificationType.SESSION_ENDING_SOON,
            { minutes: endingSessionMinutes },
          );

        if (userNotifications.length > 0) {
          await this.userNotificationRepository.save(userNotifications);
        }

        if (messages.length > 0) {
          this.notificationService.sendToManyPersonalized(messages);
        }

        await this.serviceSessionRepository.updateBy(
          { id: In(map(endingServiceSessions, 'id')) },
          { remindedAt: new Date() },
        );
      }

      // Calculate remaining time in this 60-second window
      const remainingTime = maxDurationMs - (Date.now() - startTime);
      if (remainingTime <= 0) {
        break;
      }

      // Calculate how long this iteration took
      const iterationDuration = Date.now() - iterationStart;

      // Wait for the remaining interval time, but don't exceed the total duration
      const actualDelay = Math.min(
        intervalMs - iterationDuration,
        remainingTime,
      );
      if (actualDelay > 0) {
        await new Promise((resolve) => setTimeout(resolve, actualDelay));
      }
    }

    this.logger.log(
      'Completed 60-second notification cycle - job will restart',
    );
  }

  async notifyUserCompleteSession() {
    const limit = CHUNK_SIZE;
    const intervalMs =
      this.configService.get(
        'CRONJOB.REMIND_COMPLETED_SESSION_INTERVAL_SECONDS',
        1,
      ) * 1000; // Default 1 second interval
    const maxDurationMs = 60000; // 60 seconds total
    const startTime = Date.now();

    this.logger.log(
      `Starting continuous notification check for completed sessions - running every ${intervalMs}ms for 60s`,
    );

    while (Date.now() - startTime < maxDurationMs) {
      const iterationStart = Date.now();

      const completedBefore = moment()
        .subtract(
          this.configService.get(
            'CRONJOB.REMIND_COMPLETED_SESSION_BUFFER_SECONDS',
          ),
          'seconds',
        )
        .toDate();

      const completedSessions =
        await this.serviceSessionService.getServiceSessionsCompleted({
          limit,
          completedBefore,
        });

      if (completedSessions.length > 0) {
        this.logger.log(
          `number of sessions completed: ${completedSessions.length}`,
        );

        const { userNotifications, messages } =
          await this.buildNotificationPayloadForSessions(
            completedSessions,
            NotificationType.SESSION_COMPLETED,
            {
              minutes: this.configService.get(
                'CRONJOB.REMIND_AFTER_COMPLETED_SESSION_MINUTES',
              ),
            },
          );

        await this.serviceSessionService.markAsCompleted(completedSessions);

        await this.orderService.completeOrders(
          uniq(map(completedSessions, 'orderId')),
        );

        if (userNotifications.length > 0) {
          await this.userNotificationRepository.save(userNotifications);
        }

        if (messages.length > 0) {
          this.notificationService.sendToManyPersonalized(messages);
        }
      }

      // Calculate remaining time in this 60-second window
      const remainingTime = maxDurationMs - (Date.now() - startTime);
      if (remainingTime <= 0) {
        break;
      }

      // Calculate how long this iteration took
      const iterationDuration = Date.now() - iterationStart;

      // Wait for the remaining interval time, but don't exceed the total duration
      const actualDelay = Math.min(
        intervalMs - iterationDuration,
        remainingTime,
      );
      if (actualDelay > 0) {
        await new Promise((resolve) => setTimeout(resolve, actualDelay));
      }
    }

    this.logger.log(
      'Completed 60-second notification cycle for completed sessions - job will restart',
    );
  }

  async markDoneSessions() {
    const limit = CHUNK_SIZE;
    const outdatedMinutes = this.configService.get(
      'CRONJOB.MARK_DONE_SESSION_OUTDATED_MINUTES',
    );

    for (;;) {
      const outdatedCompletedSessions =
        await this.serviceSessionService.getOutdatedCompletedSessions({
          completedBefore: moment()
            .subtract(outdatedMinutes, 'minutes')
            .toDate(),
          limit,
        });

      if (outdatedCompletedSessions.length === 0) {
        break;
      }

      await this.serviceSessionService.markAsDone(outdatedCompletedSessions);

      const { userNotifications, messages } =
        await this.buildNotificationPayloadForSessions(
          outdatedCompletedSessions,
          NotificationType.SESSION_DONE,
          {
            minutes: outdatedMinutes,
          },
        );

      if (userNotifications.length > 0) {
        await this.userNotificationRepository.save(userNotifications);
      }

      if (messages.length > 0) {
        this.notificationService.sendToManyPersonalized(messages);
      }
    }
  }

  async remindAfterCompletedSession() {
    const limit = CHUNK_SIZE;
    const outdatedMinutes = this.configService.get(
      'CRONJOB.REMIND_AFTER_COMPLETED_SESSION_MINUTES',
    );
    const bufferSeconds = this.configService.get(
      'CRONJOB.REMIND_AFTER_COMPLETED_SESSION_BUFFER_SECONDS',
    );

    const outdatedCompletedSessions =
      await this.serviceSessionService.getOutdatedCompletedSessions({
        completedBefore: moment()
          .subtract(outdatedMinutes, 'minutes')
          .add(bufferSeconds, 'seconds')
          .toDate(),
        completedAfter: moment()
          .subtract(outdatedMinutes, 'minutes')
          .subtract(bufferSeconds, 'seconds')
          .toDate(),
        limit,
      });

    if (outdatedCompletedSessions.length === 0) {
      return;
    }

    const { userNotifications, messages } =
      await this.buildNotificationPayloadForSessions(
        outdatedCompletedSessions,
        NotificationType.SESSION_AFTER_COMPLETED,
        {
          minutes: outdatedMinutes,
        },
      );

    await this.userNotificationRepository.save(userNotifications);

    if (messages.length > 0) {
      this.notificationService.sendToManyPersonalized(messages);
    }

    await this.serviceSessionRepository.updateBy(
      { id: In(map(outdatedCompletedSessions, 'id')) },
      { remindedAt: new Date() },
    );
  }

  async buildNotificationPayloadForSessions(
    sessions: ServiceSession[],
    code: NotificationType,
    notificationData?: Record<string, any>,
  ) {
    const userNotifications: UserNotification[] = [];

    const userIds = uniq(map(sessions, 'userId'));

    const userDeviceTokens = await this.userDeviceTokenRepository
      .createQueryBuilder('udt')
      .innerJoin('udt.user', 'u')
      .where('udt.is_active = :isActive', { isActive: true })
      .andWhere('u.is_allowed_notify = :isAllowedNotify', {
        isAllowedNotify: true,
      })
      .andWhere('u.id IN (:...userIds)', { userIds })
      .select(['udt', 'u'])
      .getMany();

    if (userDeviceTokens.length === 0) {
      this.logger.log(
        `no user device tokens found for user ids: ${userIds.join(', ')}`,
      );

      return {
        userNotifications,
        messages: [],
      };
    }

    const washingMachines = await this.washingMachineRepository.findBy({
      id: In(uniq(map(sessions, 'machineId'))),
    });

    const messages: (SendNotificationPayload & { to: string })[] = [];

    uniqBy(sessions, 'id').map((session) => {
      const userDeviceTokensByUser = userDeviceTokens.filter(
        (token) => token.userId === session.userId,
      );

      const washingMachine = washingMachines.find(
        (machine) => machine.id === session.machineId,
      );

      for (const userDeviceToken of uniqBy(
        userDeviceTokensByUser,
        'deviceToken',
      )) {
        const { title, body } = getLocalizedNotificationMessage(
          code,
          userDeviceToken?.user?.language || USER_LANGUAGE.EN,
          {
            washingMachineName: washingMachine?.name || '',
            completedAt: moment(
              session?.completedAt || session?.estimatedEndTime,
            ).format('HH:mm'),
            ...(notificationData || {}),
          },
        );

        userNotifications.push(
          this.userNotificationRepository.create({
            userId: session.userId,
            deviceToken: userDeviceToken.deviceToken,
            title,
            content: body,
            type: USER_NOTIFICATION_TYPE.NOTIFICATION,
            status: USER_NOTIFICATION_STATUS.SENT,
          }),
        );

        messages.push({
          to: userDeviceToken.deviceToken,
          title,
          body,
        });
      }
    });

    return {
      userNotifications,
      messages,
    };
  }

  async cancelOrders() {
    const limit = CHUNK_SIZE;
    const outdatedMinutes = this.configService.get(
      'CRONJOB.CANCEL_ORDER_MINUTES',
    );

    const pendingOrders = await this.orderService.getPendingOrders({
      createdBefore: moment().subtract(outdatedMinutes, 'minutes').toDate(),
      limit,
    });

    if (pendingOrders.length === 0) {
      return;
    }

    for (const order of pendingOrders) {
      await this.orderService.cancelOrder(order.id);
    }
  }
}
