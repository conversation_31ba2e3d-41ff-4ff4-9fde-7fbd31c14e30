import { Module } from '@nestjs/common';
import { BootstrapModule } from '@app/shared/bootstrap/bootstrap.module';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from '@app/shared/exceptions';
import { ScheduleModule } from '@nestjs/schedule';
import { ClsModule } from 'nestjs-cls';
import { ScheduleService } from './services/schedule.service';
import { MailModule } from '@app/mail';
import { HttpModule } from '@nestjs/axios';
import { AwsModule } from '@app/aws';
import { StoreModule } from '@app/store/store.module';
import { DatabaseModule } from '@app/shared/database';
import { NotificationModule } from '@app/notification';
import { OrderModule } from '@app/order';

@Module({
  imports: [
    ClsModule.forRoot({
      global: true,
    }),
    MailModule,
    HttpModule,
    AwsModule,
    BootstrapModule,
    DatabaseModule,
    ScheduleModule.forRoot(),
    StoreModule,
    NotificationModule,
    OrderModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    ScheduleService,
  ],
  exports: [ScheduleService],
})
export class AppModule {}
