# app
APP_ENV=localhost
APP_NAME=
APP_KEY=LiF0iCQhVsW9UFHrJ3nIIqB4TSBmGuIS
APP_URL=http://localhost:9000
APP_PORT=9000
APP_ROUTER_PREFIX=api
APP_LOG_LEVEL=

CORS_ALLOWED_ORIGINS=http://localhost:3000
COOKIE_DOMAIN=localhost

# limit rate
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# auth
JWT_ACCESS_TOKEN_SECRET=xOpjuJjEMevigBt36VBWAUQ3BFZiriVk
JWT_ACCESS_TOKEN_EXPIRATION_TIME=300
JWT_REFRESH_TOKEN_SECRET=04pGd4j5s0yHO5us4wCRkEir5Q5SQaBC
JWT_REFRESH_TOKEN_EXPIRATION_TIME=25200
JWT_ACCESS_COOKIES_NAME=Local-Authentication
JWT_REFRESH_COOKIES_NAME=Local-Refresh

# User
CODE_EMAIL_VERIFY_EXPIRE_MINUTES=60
CODE_FORGOT_PASSWORD_EXPIRE_MINUTES=60
CODE_CHANGE_EMAIL_EXPIRE_MINUTES=30
CODE_DELETE_USER_MINUTES=30
BLOCK_EMAIL_PLUS_SYMBOL=false

# database
DB_WRITE_HOST=postgres
DB_READ_HOST=postgres
DB_PORT=5432
DB_NAME=postgres
DB_USER=upostgres
DB_PASSWORD=postgres
DB_LOGGING=false
DB_MAX_QUERY_EXECUTION_TIME=-1

# cache
REDIS_HOST=redis
REDIS_PORT=6379
CACHE_TTL=86400000
CACHE_MAX=1000

# AWS
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET_GALLERY=
AWS_PRESIGNED_URL_EXPIRE_MINUTES=15
AWS_BUCKET_GALLERY_ENDPOINT=
AWS_USE_ACCELERATE_ENDPOINT=true

# google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=http://localhost:9000/auth/google/callback

#facebook key
FB_APP_ID=
FB_APP_SECRET=
FB_CALLBACK_URL=http://localhost:9000/auth/facebook/callback

# Line
LINE_CHANNEL_ID=
LINE_CHANNEL_SECRET=
LINE_CHANNEL_CALLBACK_URL=http://localhost:9000/auth/line/callback
LINE_AUTH_API_URL=https://api.line.me/oauth2/v2.1
LINE_MESSAGE_API_URL=https://api.line.me/v2/bot/message
LINE_MESSAGE_CHANNEL_TOKEN=
LINE_MINIAPP_ID=
LINE_MINIAPP_ID_REVIEW=

# Frontend
FRONTEND_URL=http://localhost:3000
FRONTEND_COMMUNITY_TOP_URI=community
FRONTEND_INVITE_URI=invite
FRONTEND_AUTH_LOGIN_URI=auth/login
FRONTEND_AUTH_SUCCESS_URI=auth/success

# Mail
MAIL_SENDER=<EMAIL>

# MoMoPay
MOMOPAY_PARTNER_CODE=
MOMOPAY_ACCESS_KEY=
MOMOPAY_SECRET_KEY=
MOMOPAY_PAYMENT_URL=https://test-payment.momo.vn/v2/gateway/api/create
MOMOPAY_STATUS_URL=https://test-payment.momo.vn/v2/gateway/api/queryStatus
MOMOPAY_CALLBACK_URL=http://localhost:9000/api/payment/callback/momopay

# ZaloPay (Sandbox credentials included for testing)
ZALOPAY_APP_ID=554
ZALOPAY_KEY1=8NdU5pG5R2spGHGhyO99HN1OhD8IQJBn
ZALOPAY_KEY2=uUfsWgfLkRLzq6W2uNXTCxrfxs51auny
ZALOPAY_PAYMENT_URL=https://sb-openapi.zalopay.vn/v2/create
ZALOPAY_STATUS_URL=https://sb-openapi.zalopay.vn/v2/query
ZALOPAY_CALLBACK_URL=http://localhost:9000/api/payment/callback/zalopay

# VNPay
VNPAY_TMN_CODE=
VNPAY_HASH_SECRET=
VNPAY_PAYMENT_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_STATUS_URL=https://sandbox.vnpayment.vn/merchant_webapi/merchant.html
VNPAY_CALLBACK_URL=http://localhost:9000/api/payment/callback/vnpay

# Notification
EXPO_ACCESS_TOKEN=

REMIND_ENDING_SESSION="* * * * *"
ENDING_SESSION_MINUTES=5
ENDING_SESSION_BUFFER_SECONDS=2
REMIND_ENDING_SESSION_INTERVAL_SECONDS=1

REMIND_COMPLETED_SESSION="* * * * *"
REMIND_COMPLETED_SESSION_BUFFER_SECONDS=3
REMIND_COMPLETED_SESSION_INTERVAL_SECONDS=1

MARK_DONE_SESSION="* * * * *"
MARK_DONE_SESSION_OUTDATED_MINUTES=20

REMIND_AFTER_COMPLETED_SESSION="* * * * *"
REMIND_AFTER_COMPLETED_SESSION_MINUTES=10
REMIND_AFTER_COMPLETED_SESSION_BUFFER_SECONDS=30

CANCEL_ORDER="* * * * *"
CANCEL_ORDER_MINUTES=15