ARG APP_SERVICE=api
ARG APP_PORT=3000
ARG NODEJS_VERSION=22.16-alpine
FROM node:${NODEJS_VERSION} AS nodejs

# Stage 1: Install dependencies and build the app
FROM nodejs AS builder
WORKDIR /app

COPY ./package.json ./
COPY ./yarn.lock ./
RUN yarn install --ignore-engines --production=false --frozen-lockfile

COPY . .
ARG APP_SERVICE
ENV APP_SERVICE=${APP_SERVICE}
RUN yarn build ${APP_SERVICE}
# Remove dev dependencies
RUN yarn install --ignore-engines --production=true

# Stage 2: Prepare the final image
FROM nodejs
WORKDIR /app
EXPOSE ${APP_PORT}

COPY --from=builder /app/apps ./apps
COPY --from=builder /app/scripts ./scripts
COPY --from=builder /app/nest-cli*.json /app/tsconfig.*  ./
COPY --from=builder /app/package.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist

# Ensure the script has execute permissions
RUN chmod +x /app/scripts/*

ENTRYPOINT ["/bin/sh", "./scripts/start-service.sh ${APP_SERVICE}"]