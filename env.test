# Test Environment Configuration
NODE_ENV=test
APP_LOG_LEVEL=error
APP_ROUTER_PREFIX=

# database
DB_WRITE_HOST=postgres
DB_READ_HOST=postgres
DB_PORT=5432
DB_NAME=test
DB_USER=upostgres
DB_PASSWORD=postgres
DB_LOGGING=false

# cache
REDIS_HOST=redis
REDIS_PORT=6379
CACHE_TTL=86400000
CACHE_MAX=1000

# Test specific configurations
JWT_ACCESS_TOKEN_SECRET=test_jwt_secret_key_for_testing_only
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_SECRET=test_jwt_refresh_secret_key_for_testing_only
JWT_REFRESH_TOKEN_EXPIRATION_TIME=25200
JWT_ACCESS_COOKIES_NAME=Test-Authentication
JWT_REFRESH_COOKIES_NAME=Test-Refresh

# Disable external services for testing
GOOGLE_CLIENT_ID=test_google_client_id
GOOGLE_CLIENT_SECRET=test_google_client_secret
FACEBOOK_CLIENT_ID=test_facebook_client_id
FACEBOOK_CLIENT_SECRET=test_facebook_client_secret
LINE_CHANNEL_ID=test_line_channel_id
LINE_CHANNEL_SECRET=test_line_channel_secret

# AWS test configuration
AWS_ACCESS_KEY_ID=test_access_key
AWS_SECRET_ACCESS_KEY=test_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=test-bucket
AWS_SES_REGION=us-east-1

# limit rate
THROTTLE_TTL=600
THROTTLE_LIMIT=100

# User validation
BLOCK_EMAIL_PLUS_SYMBOL=false

# MoMoPay
MOMOPAY_PARTNER_CODE=
MOMOPAY_ACCESS_KEY=
MOMOPAY_SECRET_KEY=
MOMOPAY_PAYMENT_URL=https://test-payment.momo.vn/v2/gateway/api/create
MOMOPAY_STATUS_URL=https://test-payment.momo.vn/v2/gateway/api/queryStatus
MOMOPAY_CALLBACK_URL=http://localhost:9000/api/payment/callback/momopay

# ZaloPay (Sandbox credentials included for testing)
ZALOPAY_APP_ID=554
ZALOPAY_KEY1=8NdU5pG5R2spGHGhyO99HN1OhD8IQJBn
ZALOPAY_KEY2=uUfsWgfLkRLzq6W2uNXTCxrfxs51auny
ZALOPAY_PAYMENT_URL=https://sb-openapi.zalopay.vn/v2/create
ZALOPAY_STATUS_URL=https://sb-openapi.zalopay.vn/v2/query
ZALOPAY_CALLBACK_URL=http://localhost:9000/api/payment/callback/zalopay

# VNPay
VNPAY_TMN_CODE=
VNPAY_HASH_SECRET=
VNPAY_PAYMENT_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_STATUS_URL=https://sandbox.vnpayment.vn/merchant_webapi/merchant.html
VNPAY_CALLBACK_URL=http://localhost:9000/api/payment/callback/vnpay
