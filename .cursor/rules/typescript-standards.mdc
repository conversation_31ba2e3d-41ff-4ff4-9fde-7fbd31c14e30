---
description: 
globs: ["**/*.ts", "**/*.tsx"]
alwaysApply: true
---
# TypeScript Coding Standards

## General Guidelines

### Type Safety

### Interface vs Type Guidelines
- **Use Type for data structures**: Prefer `type` over `interface` for simple data shapes
- **Use Interface for contracts**: Use `interface` when you need to extend/implement or declaration merging
- **Type for unions/intersections**: Use `type` for union types, intersection types, and utility types
- **Interface for OOP**: Use `interface` for class contracts and object-oriented patterns
- **Consistency**: Be consistent within the same codebase

**When to use Type:**
```typescript
// ✅ Data structures
type User = {
  id: string;
  name: string;
  email: string;
};

// ✅ Union types
type PaymentStatus = 'pending' | 'success' | 'failed';

// ✅ Function types
type PaymentHandler = (data: PaymentData) => Promise<void>;

// ✅ Utility types
type PartialUser = Partial<User>;
```

**When to use Interface:**
```typescript
// ✅ Class contracts
interface PaymentGateway {
  createPayment(): Promise<void>;
  verifyCallback(): Promise<boolean>;
}

// ✅ Extending
interface Animal {
  name: string;
}
interface Dog extends Animal {
  breed: string;
}

// ✅ Declaration merging
interface Config {
  apiUrl: string;
}
interface Config {
  timeout: number;
}
```
- Always use strict TypeScript configuration
- Prefer explicit types over implicit ones
- Use interfaces for object shapes
- Use enums for constants
- Avoid `any` type, use `unknown` when needed

### Code Style
- Use consistent indentation (2 spaces)
- Use semicolons at the end of statements
- Use single quotes for strings
- Use trailing commas in objects and arrays
- Use meaningful variable and function names

### Import/Export
- Use named exports over default exports
- Group imports: external libraries, internal modules, relative imports
- Use absolute imports when possible
- Remove unused imports

### Error Handling
- Use proper error types
- Implement custom error classes
- Use try-catch blocks appropriately
- Return proper error responses

## Code Examples

### Interface Definition
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateUserRequest {
  email: string;
  name: string;
  password: string;
}

interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}
```

### Enum Usage
```typescript
enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

enum ErrorCode {
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  INVALID_EMAIL = 'INVALID_EMAIL',
  UNAUTHORIZED = 'UNAUTHORIZED',
}
```

### Type Guards
```typescript
function isUser(obj: unknown): obj is User {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'email' in obj &&
    'name' in obj
  );
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

### Generic Types
```typescript
interface Repository<T> {
  findById(id: string): Promise<T | null>;
  create(data: Partial<T>): Promise<T>;
  update(id: string, data: Partial<T>): Promise<T>;
  delete(id: string): Promise<boolean>;
}

class UserRepository implements Repository<User> {
  async findById(id: string): Promise<User | null> {
    // Implementation
  }
  
  async create(data: Partial<User>): Promise<User> {
    // Implementation
  }
  
  async update(id: string, data: Partial<User>): Promise<User> {
    // Implementation
  }
  
  async delete(id: string): Promise<boolean> {
    // Implementation
  }
}
```

### Error Handling
```typescript
class CustomError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = this.constructor.name;
  }
}

class UserNotFoundError extends CustomError {
  constructor(userId: string) {
    super(`User with id ${userId} not found`, 'USER_NOT_FOUND', 404);
  }
}

async function findUser(id: string): Promise<User> {
  try {
    const user = await userRepository.findById(id);
    if (!user) {
      throw new UserNotFoundError(id);
    }
    return user;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError('Internal server error', 'INTERNAL_ERROR');
  }
}
```

### Utility Types
```typescript
// Make all properties optional
type PartialUser = Partial<User>;

// Make all properties required
type RequiredUser = Required<User>;

// Pick specific properties
type UserBasicInfo = Pick<User, 'id' | 'name' | 'email'>;

// Omit specific properties
type UserWithoutPassword = Omit<User, 'password'>;

// Extract return type
type UserRepositoryReturn = ReturnType<UserRepository['findById']>;

// Extract parameter types
type CreateUserParams = Parameters<UserRepository['create']>[0];
```

### Async/Await Best Practices
```typescript
// Good: Proper error handling
async function createUser(userData: CreateUserRequest): Promise<User> {
  try {
    const hashedPassword = await hashPassword(userData.password);
    const user = await userRepository.create({
      ...userData,
      password: hashedPassword,
    });
    return user;
  } catch (error) {
    if (error instanceof ValidationError) {
      throw new CustomError('Invalid user data', 'VALIDATION_ERROR', 400);
    }
    throw error;
  }
}

// Good: Parallel execution
async function getUserData(userId: string): Promise<UserData> {
  const [user, profile, settings] = await Promise.all([
    userRepository.findById(userId),
    profileRepository.findByUserId(userId),
    settingsRepository.findByUserId(userId),
  ]);
  
  return { user, profile, settings };
}
```

## Naming Conventions

### Variables and Functions
```typescript
// Good
const userRepository = new UserRepository();
const isValidEmail = (email: string): boolean => { /* ... */ };
const createUser = async (userData: CreateUserRequest): Promise<User> => { /* ... */ };

// Bad
const repo = new UserRepository();
const checkEmail = (email: string): boolean => { /* ... */ };
const addUser = async (data: CreateUserRequest): Promise<User> => { /* ... */ };
```

### Interfaces and Types
```typescript
// Good
interface UserService {
  createUser(data: CreateUserRequest): Promise<User>;
  updateUser(id: string, data: UpdateUserRequest): Promise<User>;
}

type UserStatus = 'active' | 'inactive' | 'suspended';

// Bad
interface Service {
  create(data: any): Promise<any>;
  update(id: string, data: any): Promise<any>;
}

type Status = string;
```

### Constants
```typescript
// Good
const MAX_LOGIN_ATTEMPTS = 3;
const PASSWORD_MIN_LENGTH = 8;
const API_BASE_URL = 'https://api.example.com';

// Bad
const maxAttempts = 3;
const minLength = 8;
const baseUrl = 'https://api.example.com';
```

## Best Practices Checklist

### ✅ Type Safety
- [ ] Use strict TypeScript configuration
- [ ] Avoid `any` type
- [ ] Use proper interfaces and types
- [ ] Implement type guards
- [ ] Use generic types appropriately

### ✅ Code Quality
- [ ] Follow consistent naming conventions
- [ ] Use meaningful variable names
- [ ] Write self-documenting code
- [ ] Keep functions small and focused
- [ ] Use proper error handling

### ✅ Performance
- [ ] Use async/await properly
- [ ] Implement proper error boundaries
- [ ] Use appropriate data structures
- [ ] Avoid unnecessary type assertions
- [ ] Use utility types effectively

### ✅ Maintainability
- [ ] Write clean, readable code
- [ ] Use proper imports/exports
- [ ] Follow DRY principle
- [ ] Use constants for magic numbers
- [ ] Document complex logic

### Interface vs Type Guidelines

- **Use Type for data structures**: Prefer `type` over `interface` for simple data shapes
- **Use Interface for contracts**: Use `interface` when you need to extend/implement or declaration merging
- **Type for unions/intersections**: Use `type` for union types, intersection types, and utility types
- **Interface for OOP**: Use `interface` for class contracts and object-oriented patterns
- **Consistency**: Be consistent within the same codebase

**When to use Type:**
```typescript
// ✅ Data structures
type User = {
  id: string;
  name: string;
  email: string;
};

// ✅ Union types
type PaymentStatus = 'pending' | 'success' | 'failed';

// ✅ Function types
type PaymentHandler = (data: PaymentData) => Promise<void>;

// ✅ Utility types
type PartialUser = Partial<User>;
```

**When to use Interface:**
```typescript
// ✅ Class contracts
interface PaymentGateway {
  createPayment(): Promise<void>;
  verifyCallback(): Promise<boolean>;
}

// ✅ Extending
interface Animal {
  name: string;
}
interface Dog extends Animal {
  breed: string;
}

// ✅ Declaration merging
interface Config {
  apiUrl: string;
}
interface Config {
  timeout: number;
}
```
