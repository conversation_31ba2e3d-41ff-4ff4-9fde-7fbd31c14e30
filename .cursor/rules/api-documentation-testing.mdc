---
description: API Documentation & E2E Testing Rules for NestJS
globs: ["**/*.ts", "**/*.spec.ts", "**/*.e2e-spec.ts"]
alwaysApply: true
---
# API Documentation & E2E Testing Rules

## E2E Testing Core Principles

### 1. Real Business Logic Testing
**Rule**: NEVER mock core business services (AuthService, UserService, etc.)
- ✅ **DO**: Use real AuthService, UserService implementations
- ❌ **DON'T**: Mock AuthService, UserService in e2e tests
- **Reason**: E2E tests must verify the complete flow including business logic

### 2. Real Database Operations
**Rule**: Test with actual database operations
- ✅ **DO**: Use real database connections and operations
- ✅ **DO**: Test actual CRUD operations
- ✅ **DO**: Verify data persistence and retrieval
- ❌ **DON'T**: Mock database operations
- **Reason**: Database integration is a critical part of the application flow

### 3. Real Validation Testing
**Rule**: Use actual validation pipes and decorators
- ✅ **DO**: Test real validation decorators (@IsEmail, @IsString, etc.)
- ✅ **DO**: Verify validation error responses
- ✅ **DO**: Test custom validation rules
- ❌ **DON'T**: Mock validation logic
- **Reason**: Validation is part of the API contract and must be tested

### 4. External Dependencies Mocking
**Rule**: ONLY mock external services and APIs
- ✅ **DO**: Mock external HTTP calls (Google, Facebook, Line APIs)
- ✅ **DO**: Mock AWS services (S3, SES, Chime)
- ✅ **DO**: Mock email services
- ❌ **DON'T**: Mock internal business logic
- **Reason**: External services should not affect test reliability

### 5. Performance and Reliability
**Rule**: Ensure tests are fast and reliable
- ✅ **DO**: Use shared test application for better performance
- ✅ **DO**: Use test database with proper cleanup
- ✅ **DO**: Mock external API calls to avoid network dependencies
- ✅ **DO**: Use proper test isolation
- ❌ **DON'T**: Depend on external services for test execution
- **Reason**: Tests should be deterministic and fast

### 6. Shared Test Application
**Rule**: Use shared test app for optimal performance
- ✅ **DO**: Use `global.getSharedTestApp()` in `beforeAll()`
- ✅ **DO**: Use `global.clearTestDatabase()` in `beforeEach()`
- ✅ **DO**: Use `global.clearThrottlingData()` for rate-limited endpoints
- ❌ **DON'T**: Use `initTestApp()` (deprecated for performance reasons)
- **Reason**: Shared app reduces test execution time by 40%

## Workflow for Writing New Controller Functions

### Step 1: Analyze Service Method
When writing a new controller function, ALWAYS read and analyze the service method first:
- Identify all possible `throw new Exception()` cases
- Identify guards/interceptors that might throw errors
- Identify validation errors

### Step 2: Write Complete API Documentation
Use Swagger decorators to describe all possible error responses based on actual service exceptions:
- `@ApiOperation` with summary and description
- `@ApiBody` with type and description
- **Validation Errors**: Use appropriate decorator based on actual validation behavior:
  - `@ApiBadRequestResponse` (400) - For basic validation failures
  - `@ApiUnprocessableEntityResponse` (422) - For detailed validation errors (class-validator)
- **Business Logic Errors**: Use based on actual exception types:
  - `@ApiNotFoundResponse` (404) - For `NotFoundException`
  - `@ApiConflictResponse` (409) - For `ConflictException`
  - `@ApiForbiddenResponse` (403) - For `ForbiddenException`
  - `@ApiUnauthorizedResponse` (401) - For `UnauthorizedException`
- **Rate Limiting**: `@ApiTooManyRequestsResponse` (429) - If endpoint uses throttling

### Step 3: Write E2E Tests Based on API Documentation
From API docs, write test cases for all scenarios with correct status codes:
- **Success test case** - Test happy path with valid data
- **Test case for each error code** described in API docs with correct status codes:
  - Test validation errors (422 for class-validator, 400 for basic validation)
  - Test business logic errors (404, 409, 403, 401 based on actual exceptions)
  - Test rate limiting behavior (429) if endpoint uses throttling
- **Test edge cases and boundary conditions**:
  - Malformed JSON (400)
  - Empty payload (422)
  - Null/undefined values (422)
  - Whitespace-only values (422)
  - Maximum length values (422)
  - Special characters in inputs

**IMPORTANT**: Follow E2E Testing Core Principles above - NEVER mock core business services!

## Required Template for Controller Function

```typescript
@Post('endpoint-name')
@HttpCode(HttpStatus.OK)
@ApiOperation({
  summary: 'Brief description',
  description: 'Detailed description of what this endpoint does',
})
@ApiBody({
  type: YourDto,
  description: 'Description of request body',
})
@ApiUnprocessableEntityResponse({
  description: 'Invalid input data',
  type: ResponseUnprocessableEntityDto,
})
@ApiNotFoundResponse({
  description: 'Resource not found',
  schema: {
    properties: {
      code: { enum: ['EXPECTED_ERROR_CODE'] }
    }
  }
})
@ApiConflictResponse({
  description: 'Resource conflict',
  schema: {
    properties: {
      code: { enum: ['EXPECTED_ERROR_CODE'] }
    }
  }
})
@ApiUnauthorizedResponse({
  description: 'Unauthorized access',
  schema: {
    properties: {
      code: { enum: ['UNAUTHORIZED'] }
    }
  }
})
async endpointName(@Body() payload: YourDto): Promise<ResponseType> {
  // Implementation
}
```

## Required Template for E2E Test

```typescript
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '@app/user/services/user.service';
import { UserTestHelper } from '../../helpers/user-test.helper';

describe('ControllerName (e2e) - Endpoint Description', () => {
    let app: INestApplication;
    let authService: AuthService;
    let userService: UserService;
    let testHelper: UserTestHelper;
    const endpoint = '/your-endpoint';

    beforeAll(async () => {
        // Use shared test app for better performance
        app = await global.getSharedTestApp();
        authService = app.get<AuthService>(AuthService);
        userService = app.get<UserService>(UserService);
        testHelper = new UserTestHelper(app, authService, userService);
    });

    beforeEach(async () => {
        await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
        await global.clearThrottlingData(); // OPTIONAL: Only if testing rate-limited endpoints
    });

    afterEach(async () => {
        await global.clearThrottlingData(); // OPTIONAL: Only if testing rate-limited endpoints
    });

    describe('POST /your-endpoint', () => {
        describe('Success cases', () => {
            it('should perform action successfully', async () => {
                const payload = { /* valid payload */ };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(200);

                expect(response.body).toMatchObject(/* expected response */); // OPTIONAL: Check expected response
            });
        });

        describe('Error cases', () => {
            it('should return 422 for validation errors', async () => {
                const payload = { /* invalid payload */ };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(422);

                expect(response.body).toHaveProperty('errors');
            });

            it('should return 404 for not found', async () => {
                const payload = { /* payload that causes not found */ };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(404);

                expect(response.body).toHaveProperty('code', 'EXPECTED_ERROR_CODE');
            });

            it('should return 409 for conflict', async () => {
                const payload = { /* payload that causes conflict */ };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(409);

                expect(response.body).toHaveProperty('code', 'EXPECTED_ERROR_CODE');
            });

            it('should return 401 for unauthorized', async () => {
                const payload = { /* payload that causes unauthorized */ };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(401);

                expect(response.body).toHaveProperty('code', 'EXPECTED_ERROR_CODE');
            });

            it('should return 429 when rate limit exceeded', async () => {
                const payload = { /* valid payload */ };

                // Send multiple requests to exceed limit
                for (let i = 0; i < 4; i++) {
                    const response = await request(app.getHttpServer())
                        .post(endpoint)
                        .send(payload);

                    if (i < 3) {
                        expect(response.status).toBe(200);
                    } else {
                        expect(response.status).toBe(429);
                        expect(response.body).toHaveProperty('code', 'RATE_LIMIT_ERROR');
                    }
                }
            });
        });

        describe('Edge cases and boundary conditions', () => {
            it('should handle malformed JSON', async () => {
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .set('Content-Type', 'application/json')
                    .send('invalid json')
                    .expect(400);
            });

            it('should handle empty payload', async () => {
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send({})
                    .expect(422); // Usually 422 for validation errors

                expect(response.body).toHaveProperty('errors');
            });

            it('should handle null values', async () => {
                const payload = {
                    field1: null,
                    field2: null,
                };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(422);

                expect(response.body).toHaveProperty('errors');
            });

            it('should handle undefined values', async () => {
                const payload = {
                    field1: undefined,
                    field2: undefined,
                };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(422);

                expect(response.body).toHaveProperty('errors');
            });

            it('should handle whitespace-only values', async () => {
                const payload = {
                    field1: '   ',
                    field2: '   ',
                };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(422);

                expect(response.body).toHaveProperty('errors');
            });

            it('should handle maximum length values', async () => {
                const payload = {
                    field1: 'a'.repeat(255), // Exceeds max length
                    field2: 'a'.repeat(255),
                };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(422);

                expect(response.body).toHaveProperty('errors');
            });

            it('should handle special characters in inputs', async () => {
                const payload = {
                    field1: '<EMAIL>!@#$%^&*()',
                    field2: 'special chars !@#$%^&*()',
                };
                
                const response = await request(app.getHttpServer())
                    .post(endpoint)
                    .send(payload)
                    .expect(422); // Or 404 depending on validation

                expect(response.body).toHaveProperty('errors');
            });
        });
    });
});
```

### Service Usage Guidelines

**When to inject services:**

- **`AuthService`**: 
  - Generating access/refresh tokens for authenticated requests
  - Testing authentication logic
  - Example: `authService.generateAccessToken({ userId: user.id })`
  - **IMPORTANT**: Use real AuthService, NEVER mock it!

- **`UserService`**: 
  - Creating test users in database
  - Testing user-related business logic
  - Example: `userService.create(userData)`
  - **IMPORTANT**: Use real UserService, NEVER mock it!

- **`JwtService`**: 
  - Direct JWT token operations
  - Testing token validation
  - Example: `jwtService.sign(payload, options)`
  - **IMPORTANT**: Use real JwtService, NEVER mock it!

- **`ConfigService`**: 
  - Accessing configuration values
  - Testing environment-specific behavior
  - Example: `configService.get('FRONTEND_APP.AUTH_SUCCESS_URL')`
  - **IMPORTANT**: Use real ConfigService, NEVER mock it!

**Example of minimal service setup:**
```typescript
// For simple endpoint tests (no authentication needed)
let app: INestApplication;

// For authentication tests
let app: INestApplication;
let authService: AuthService; // REAL service, not mocked!

// For user creation tests
let app: INestApplication;
let userService: UserService; // REAL service, not mocked!

// For complex tests requiring multiple services
let app: INestApplication;
let authService: AuthService; // REAL service, not mocked!
let userService: UserService; // REAL service, not mocked!
let configService: ConfigService; // REAL service, not mocked!
```

**CRITICAL RULE**: All core business services (AuthService, UserService, JwtService, ConfigService) must be REAL implementations, NEVER mocked in e2e tests!

### Test Hooks Guidelines

**Required Hooks (Always include):**
```typescript
beforeAll(async () => {
    // Use shared test app for better performance
    app = await global.getSharedTestApp();
    // Get services as needed
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
});

beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
});
```

**Optional Hooks (Only when needed):**
```typescript
beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED
    await global.clearThrottlingData(); // OPTIONAL: Only for rate-limited endpoints
});

afterEach(async () => {
    await global.clearThrottlingData(); // OPTIONAL: Only for rate-limited endpoints
});
```

**Examples by endpoint type:**
```typescript
// Simple endpoint (no rate limiting)
beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED
});

// Rate-limited endpoint (forgot password, verify code)
beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED
    await global.clearThrottlingData(); // OPTIONAL: For rate limiting tests
});

afterEach(async () => {
    await global.clearThrottlingData(); // OPTIONAL: For rate limiting tests
});
```

## Required Checklist

### ✅ Service Analysis
- [ ] Read the service method being used
- [ ] Identify all possible `throw new Exception()` cases
- [ ] Identify guards/interceptors that might throw errors
- [ ] Identify validation errors

### ✅ API Documentation
- [ ] `@ApiOperation` with summary and description
- [ ] `@ApiBody` with type and description
- [ ] **Validation Errors**: Use correct decorator based on actual validation behavior
  - [ ] `@ApiBadRequestResponse` (400) or `@ApiUnprocessableEntityResponse` (422)
- [ ] **Business Logic Errors**: Use decorators matching actual exception types
  - [ ] `@ApiNotFoundResponse` (404) for `NotFoundException`
  - [ ] `@ApiConflictResponse` (409) for `ConflictException`
  - [ ] `@ApiForbiddenResponse` (403) for `ForbiddenException`
  - [ ] `@ApiUnauthorizedResponse` (401) for `UnauthorizedException`
- [ ] **Rate Limiting**: `@ApiTooManyRequestsResponse` (429) if endpoint uses throttling
- [ ] **System Errors**: `@ApiInternalServerErrorResponse` (500) for unexpected errors
- [ ] Complete schema description for each error response with actual error codes

### ✅ E2E Tests
- [ ] Setup test environment with required hooks
  - [ ] `beforeAll()` - Initialize shared test app with `global.getSharedTestApp()`
  - [ ] `beforeEach()` - **REQUIRED**: Always clear database with `global.clearTestDatabase()`
  - [ ] `afterEach()` - **OPTIONAL**: Only if testing rate-limited endpoints
- [ ] Import required dependencies and utilities
- [ ] **Service Setup**: Only inject services that are actually needed for test cases
  - [ ] `AuthService` - for generating tokens, authentication logic (**REAL service, NEVER mocked!**)
  - [ ] `UserService` - for user operations, database interactions (**REAL service, NEVER mocked!**)
  - [ ] `JwtService` - for JWT token operations (**REAL service, NEVER mocked!**)
  - [ ] `ConfigService` - for configuration values (**REAL service, NEVER mocked!**)
  - [ ] Other services as needed (**REAL services, NEVER mocked!**)
- [ ] **CRITICAL**: Verify that NO core business services are mocked in test setup
- [ ] Success test case
- [ ] Test case for each error code described in API docs
- [ ] **Test validation errors** with correct status codes:
  - [ ] 400 for basic validation failures
  - [ ] 422 for detailed validation errors (class-validator)
  - [ ] **REQUIRED**: Always check `expect(response.body).toHaveProperty('errors')` for 422 responses
- [ ] **Test business logic errors** with correct status codes:
  - [ ] 404 for `NotFoundException`
  - [ ] 409 for `ConflictException`
  - [ ] 403 for `ForbiddenException`
  - [ ] 401 for `UnauthorizedException`
- [ ] Test rate limiting behavior (429) if endpoint uses throttling
- [ ] Test system errors (500) - only if realistic to test
- [ ] Test edge cases and boundary conditions
- [ ] Test malformed JSON, empty payload, null/undefined values
- [ ] **Verify real business logic**: Ensure tests actually test the real implementation, not mocked behavior

### ✅ Validation Error Testing Requirements
- [ ] **ALWAYS** check `expect(response.body).toHaveProperty('errors')` for 422 responses
- [ ] Test all validation scenarios:
  - [ ] Invalid email format
  - [ ] Invalid phone format
  - [ ] Weak password (missing complexity requirements)
  - [ ] Missing required fields
  - [ ] Empty payload
  - [ ] Null values
  - [ ] Undefined values
  - [ ] Whitespace-only values
  - [ ] Maximum length violations
  - [ ] Special characters in inputs
  - [ ] Invalid date formats (if applicable)
- [ ] **NEVER** use `// Only check status code, skip response body validation` for 422 tests
- [ ] **ALWAYS** verify error response structure for validation errors

## Required Rules

1. **ALWAYS** read service before writing controller
2. **ALWAYS** write complete API docs before writing tests
3. **ALWAYS** test all error cases described in API docs
4. **ALWAYS** update both API docs and tests when changing service logic
5. **ALWAYS** use the defined template for controller and test
6. **ALWAYS** describe complete error responses in API documentation
7. **ALWAYS** test rate limiting behavior if endpoint uses throttling
8. **ALWAYS** test edge cases and boundary conditions
9. **ALWAYS** only inject services that are actually needed for test cases
10. **ALWAYS** uncomment and import services only when required by specific test scenarios
11. **ALWAYS** include `global.clearTestDatabase()` in `beforeEach()` hook
12. **ALWAYS** include `global.clearThrottlingData()` hooks when testing rate-limited endpoints
13. **ALWAYS** use `global.getSharedTestApp()` for better performance

## Validation Error Testing Rules

14. **ALWAYS** check `expect(response.body).toHaveProperty('errors')` for 422 responses
15. **NEVER** use `// Only check status code, skip response body validation` for 422 tests
16. **ALWAYS** test all validation scenarios including edge cases
17. **ALWAYS** verify error response structure for validation errors
18. **ALWAYS** test both individual field validation and combined validation errors

## E2E Testing Critical Rules

14. **NEVER** mock core business services (AuthService, UserService, JwtService, ConfigService)
15. **ALWAYS** use real business logic implementations in e2e tests
16. **ALWAYS** test with real database operations
17. **ALWAYS** use real validation pipes and decorators
18. **ONLY** mock external dependencies (HttpService, SESService, S3Service, etc.)
19. **ALWAYS** verify that tests actually test the real implementation, not mocked behavior
20. **ALWAYS** ensure test isolation and proper cleanup
21. **ALWAYS** make tests fast and reliable by mocking external services only
22. **ALWAYS** use shared test application for optimal performance

## Validation Error Testing Examples

### ✅ Correct Validation Error Test
```typescript
it('should return 422 for validation errors', async () => {
  const payload = {
    email: 'invalid-email',
    password: 'weak',
    name: '',
    phone: '123', // Invalid phone format
    birthday: 'invalid-date', // Invalid date format
  };

  const response = await request(app.getHttpServer())
    .post(endpoint)
    .send(payload)
    .expect(422);

  expect(response.body).toHaveProperty('errors');
});
```

### ❌ Incorrect Validation Error Test
```typescript
it('should return 422 for validation errors', async () => {
  const payload = {
    email: 'invalid-email',
    password: 'weak',
  };

  const response = await request(app.getHttpServer())
    .post(endpoint)
    .send(payload)
    .expect(422);

  // Only check status code, skip response body validation
});
```

### ✅ Complete Validation Test Suite
```typescript
describe('Validation errors', () => {
  it('should return 422 for invalid email format', async () => {
    const payload = { email: 'invalid-email', /* other required fields */ };
    const response = await request(app.getHttpServer())
      .post(endpoint)
      .send(payload)
      .expect(422);
    expect(response.body).toHaveProperty('errors');
  });

  it('should return 422 for missing required fields', async () => {
    const payload = { email: '<EMAIL>' }; // Missing other fields
    const response = await request(app.getHttpServer())
      .post(endpoint)
      .send(payload)
      .expect(422);
    expect(response.body).toHaveProperty('errors');
  });

  it('should return 422 for empty payload', async () => {
    const response = await request(app.getHttpServer())
      .post(endpoint)
      .send({})
      .expect(422);
    expect(response.body).toHaveProperty('errors');
  });

  it('should return 422 for null values', async () => {
    const payload = { email: null, password: null, /* other fields */ };
    const response = await request(app.getHttpServer())
      .post(endpoint)
      .send(payload)
      .expect(422);
    expect(response.body).toHaveProperty('errors');
  });

  it('should return 422 for whitespace-only values', async () => {
    const payload = { email: '   ', password: '   ', /* other fields */ };
    const response = await request(app.getHttpServer())
      .post(endpoint)
      .send(payload)
      .expect(422);
    expect(response.body).toHaveProperty('errors');
  });
});
```

## Common Error Codes

- `USER_NOT_FOUND` - User not found
- `USER_NOT_ACTIVATED` - User account not activated
- `INVALID_VERIFICATION_CODE` - Invalid verification code
- `VERIFY_CODE_RATE_LIMITED` - Rate limit exceeded for verification codes
- `EMAIL_NOT_EXISTS` - Email does not exist
- `INVALID_EMAIL` - Invalid email format
- `BAD_REQUEST` - Invalid input data

## Important Notes

- Use `AllExceptionsFilter` to handle error responses
- Error codes are defined in `libs/shared/src/types/error.type.ts`
- Rate limiting uses `VerifyCodeThrottleGuard` with configuration in decorator
- **Status Code Guidelines**:
  - `400 Bad Request`: Basic validation failures, malformed requests
  - `401 Unauthorized`: Missing or invalid authentication (Passport.js)
  - `403 Forbidden`: Valid authentication but insufficient permissions
  - `404 Not Found`: Resource not found (NotFoundException)
  - `409 Conflict`: Resource conflicts (ConflictException)
  - `422 Unprocessable Entity`: Detailed validation errors (class-validator)
  - `429 Too Many Requests`: Rate limiting exceeded

## E2E Testing Anti-Patterns to Avoid

### ❌ Don't Mock Core Services
```typescript
// ❌ WRONG: Mocking AuthService
.overrideProvider(AuthService)
.useValue({
    verifyGoogleMember: jest.fn().mockResolvedValue(mockData),
    generateAccessToken: jest.fn().mockReturnValue('mock_token'),
})
```

### ❌ Don't Mock Database Operations
```typescript
// ❌ WRONG: Mocking UserService
.overrideProvider(UserService)
.useValue({
    create: jest.fn().mockResolvedValue(mockUser),
    findByEmail: jest.fn().mockResolvedValue(mockUser),
})
```

### ❌ Don't Mock Validation
```typescript
// ❌ WRONG: Mocking validation pipes
.overrideProvider(ValidationPipe)
.useValue({
    transform: jest.fn().mockReturnValue(mockData),
})
```

### ❌ Don't Use Deprecated initTestApp
```typescript
// ❌ WRONG: Using deprecated initTestApp (slow performance)
beforeAll(async () => {
    app = await initTestApp(); // DEPRECATED: Use shared app instead
});

afterAll(async () => {
    await cleanupTestApp(); // DEPRECATED: Not needed with shared app
});
```

### ✅ Correct Approach
```typescript
// ✅ CORRECT: Only mock external services
.overrideProvider(HttpService)
.useValue({
    get: jest.fn().mockReturnValue(of({ status: 200, data: mockData })),
})

.overrideProvider(SESService)
.useValue({
    sendEmail: jest.fn().mockResolvedValue(true),
})
```

### ✅ Correct Shared App Usage
```typescript
// ✅ CORRECT: Using shared test app for optimal performance
beforeAll(async () => {
    app = await global.getSharedTestApp(); // FAST: Shared app instance
    authService = app.get<AuthService>(AuthService);
    userService = app.get<UserService>(UserService);
    testHelper = new UserTestHelper(app, authService, userService);
});

beforeEach(async () => {
    await global.clearTestDatabase(); // REQUIRED: Clear database between tests
    await global.clearThrottlingData(); // OPTIONAL: For rate-limited endpoints
});

afterEach(async () => {
    await global.clearThrottlingData(); // OPTIONAL: For rate-limited endpoints
});
```

## Example Implementation

### Service Method Analysis
```typescript
// Example: userService.forgotPassword(email)
async forgotPassword(email: string): Promise<boolean> {
  const user = await this.getVerifiedUserByEmail(email); // Can throw USER_NOT_FOUND, USER_NOT_ACTIVATED
  
  // ... processing logic
  
  return true;
}
```

### Controller with Complete Documentation
```typescript
@Post('forgot-password/send-code')
@HttpCode(HttpStatus.OK)
@ApiOperation({
  summary: 'Send forgot password email',
  description: 'Send verification code via email to reset password. Code expires in 5 minutes.',
})
@ApiBody({
  type: ForgotPasswordDto,
  description: 'Email of user who needs password reset',
})
@ApiUnprocessableEntityResponse({
  description: 'Invalid input data',
  type: ResponseUnprocessableEntityDto,
})
@ApiNotFoundResponse({
  description: 'User not found or account not activated',
  schema: {
    oneOf: [
      {
        properties: {
          code: { enum: ['USER_NOT_FOUND'] }
        }
      },
      {
        properties: {
          code: { enum: ['USER_NOT_ACTIVATED'] }
        }
      }
    ]
  }
})
@ApiTooManyRequestsResponse({
  description: 'Rate limit exceeded. Try again after 10 minutes.',
  schema: {
    properties: {
      code: { enum: ['VERIFY_CODE_RATE_LIMITED'] },
      data: {
        type: 'object',
        properties: {
          remainingTime: { type: 'number', example: 600 },
          maxAttempts: { type: 'number', example: 3 }
        }
      }
    }
  }
})
async sendForgotPasswordCode(@Body() payload: ForgotPasswordDto): Promise<boolean> {
  const { email } = payload;
  await this.userService.forgotPassword(email);
  return true;
}
```

### Complete E2E Test
```typescript
describe('POST /auth/forgot-password/send-code', () => {
  const endpoint = '/auth/forgot-password/send-code';

  describe('Success cases', () => {
    it('should send forgot password code successfully', async () => {
      const payload = { email: '<EMAIL>' };
      
      const response = await request(app.getHttpServer())
        .post(endpoint)
        .send(payload)
        .expect(200);

      expect(response.body).toBe(true);
    });
  });

  describe('Error cases', () => {
    it('should return 422 for invalid email format', async () => {
      const payload = { email: 'invalid-email' };
      
      const response = await request(app.getHttpServer())
        .post(endpoint)
        .send(payload)
        .expect(422);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 404 for non-existent user', async () => {
      const payload = { email: '<EMAIL>' };
      
      const response = await request(app.getHttpServer())
        .post(endpoint)
        .send(payload)
        .expect(404);

      expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
    });

    it('should return 404 for unactivated user', async () => {
      const payload = { email: '<EMAIL>' };
      
      const response = await request(app.getHttpServer())
        .post(endpoint)
        .send(payload)
        .expect(404);

      expect(response.body).toHaveProperty('code', 'USER_NOT_ACTIVATED');
    });

    it('should return 429 when rate limit exceeded', async () => {
      const payload = { email: '<EMAIL>' };

      // Send 4 times consecutively to exceed limit (3 times)
      for (let i = 0; i < 4; i++) {
        const response = await request(app.getHttpServer())
          .post(endpoint)
          .send(payload);

        if (i < 3) {
          expect(response.status).toBe(200);
        } else {
          expect(response.status).toBe(429);
          expect(response.body).toHaveProperty('code', 'VERIFY_CODE_RATE_LIMITED');
          expect(response.body).toHaveProperty('data');
          expect(response.body.data).toHaveProperty('remainingTime');
          expect(response.body.data).toHaveProperty('maxAttempts', 3);
        }
      }
    });
  });
});
```

**IMPORTANT**: This test uses REAL business logic (AuthService, UserService) and REAL database operations. Only external services (email sending) are mocked!