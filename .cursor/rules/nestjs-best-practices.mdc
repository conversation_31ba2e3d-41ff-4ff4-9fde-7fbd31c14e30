---
description:
globs:
alwaysApply: true
---

# NestJS Best Practices

## General Guidelines

### Code Organization

- Use feature-based folder structure
- Keep controllers thin, move business logic to services
- Use DTOs for request/response validation
- Implement proper error handling with custom exceptions
- Use dependency injection for all services

### Naming Conventions

- Use PascalCase for classes, interfaces, and types
- Use camelCase for variables, functions, and methods
- Use kebab-case for file names
- Use UPPER_SNAKE_CASE for constants

### Error Handling

- Always use custom exceptions with proper error codes
- Implement global exception filters
- Use validation pipes for input validation
- Return consistent error response format

#### REQUIRED: Attach ErrorCode in HttpExceptions

- Always include a `code` field using `ErrorCode` when throwing `HttpException` (BadRequestException, NotFoundException, ForbiddenException, UnauthorizedException, ConflictException).

Example:

```typescript
// Bad
throw new NotFoundException('Order not found');

// Good
throw new NotFoundException({
  code: 'NOT_FOUND',
  errors: { orderId },
});

// Bad
throw new BadRequestException('Invalid quantity');

// Good
throw new BadRequestException({
  code: 'BAD_REQUEST',
});

// Forbidden
throw new ForbiddenException({
  code: 'FORBIDDEN',
});
```

### Database

- Use TypeORM repositories for data access
- Implement proper database migrations
- Use transactions for complex operations
- Implement soft deletes where appropriate

### Security

- Always validate and sanitize input data
- Use proper authentication and authorization
- Implement rate limiting for sensitive endpoints
- Use environment variables for sensitive configuration

### Testing

- Write unit tests for services
- Write integration tests for controllers
- Use test databases for E2E tests
- Mock external dependencies

## File Structure Example

```
src/
├── controllers/
│   └── user.controller.ts
├── services/
│   └── user.service.ts
├── dto/
│   ├── create-user.dto.ts
│   └── update-user.dto.ts
├── entities/
│   └── user.entity.ts
├── repositories/
│   └── user.repository.ts
├── guards/
│   └── auth.guard.ts
├── interceptors/
│   └── logging.interceptor.ts
├── filters/
│   └── http-exception.filter.ts
└── pipes/
    └── validation.pipe.ts
```

## Code Examples

### Controller Example

```typescript
@Controller('users')
@ApiTags('Users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Create user' })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({ status: 201, type: UserDto })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserDto> {
    return this.userService.create(createUserDto);
  }
}
```

### Service Example

```typescript
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }
}
```

### DTO Example

```typescript
export class CreateUserDto {
  @IsEmail()
  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @IsString()
  @MinLength(6)
  @ApiProperty({ example: 'password123' })
  password: string;

  @IsString()
  @ApiProperty({ example: 'John Doe' })
  name: string;
}
```

### Entity Example

```typescript
@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column()
  name: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
```

## Best Practices Checklist

### ✅ Code Quality

- [ ] Use TypeScript strict mode
- [ ] Implement proper error handling
- [ ] Use dependency injection
- [ ] Follow SOLID principles
- [ ] Write clean, readable code

### ✅ Security

- [ ] Validate all inputs
- [ ] Implement authentication
- [ ] Use proper authorization
- [ ] Sanitize data
- [ ] Use HTTPS in production

### ✅ Performance

- [ ] Use proper database indexing
- [ ] Implement caching where appropriate
- [ ] Optimize database queries
- [ ] Use pagination for large datasets
- [ ] Implement proper logging

### ✅ Testing

- [ ] Write unit tests
- [ ] Write integration tests
- [ ] Write E2E tests
- [ ] Use test coverage
- [ ] Mock external dependencies

### ✅ Documentation

- [ ] Use Swagger/OpenAPI
- [ ] Document API endpoints
- [ ] Write README files
- [ ] Document environment variables
- [ ] Keep documentation updated
