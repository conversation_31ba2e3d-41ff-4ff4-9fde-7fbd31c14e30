{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api/src", "compilerOptions": {"assets": [{"include": "../../../libs/user/src/templates/**/*.hbs", "outDir": "dist"}, {"include": "../../../libs/mail/src/templates/layouts/**/*.hbs", "outDir": "dist"}], "plugins": ["@nestjs/swagger"], "deleteOutDir": true, "webpack": false, "tsConfigPath": "apps/api/tsconfig.app.json"}, "monorepo": true, "root": "apps/api", "projects": {"notification": {"type": "library", "root": "libs/notification", "entryFile": "index", "sourceRoot": "libs/notification/src", "compilerOptions": {"tsConfigPath": "libs/notification/tsconfig.lib.json"}}, "api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "auth": {"type": "library", "root": "libs/auth", "entryFile": "index", "sourceRoot": "libs/auth/src", "compilerOptions": {"tsConfigPath": "libs/auth/tsconfig.lib.json"}}, "aws": {"type": "library", "root": "libs/aws", "entryFile": "index", "sourceRoot": "libs/aws/src", "compilerOptions": {"tsConfigPath": "libs/aws/tsconfig.lib.json"}}, "cache": {"type": "library", "root": "libs/cache", "entryFile": "index", "sourceRoot": "libs/cache/src", "compilerOptions": {"tsConfigPath": "libs/cache/tsconfig.lib.json"}}, "mail": {"type": "library", "root": "libs/mail", "entryFile": "index", "sourceRoot": "libs/mail/src", "compilerOptions": {"tsConfigPath": "libs/mail/tsconfig.lib.json"}}, "order": {"type": "library", "root": "libs/order", "entryFile": "index", "sourceRoot": "libs/order/src", "compilerOptions": {"tsConfigPath": "libs/order/tsconfig.lib.json"}}, "payment": {"type": "library", "root": "libs/payment", "entryFile": "index", "sourceRoot": "libs/payment/src", "compilerOptions": {"tsConfigPath": "libs/payment/tsconfig.lib.json"}}, "schedule": {"type": "application", "root": "apps/schedule", "entryFile": "main", "sourceRoot": "apps/schedule/src", "compilerOptions": {"tsConfigPath": "apps/schedule/tsconfig.app.json"}}, "shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}, "store": {"type": "library", "root": "libs/store", "entryFile": "index", "sourceRoot": "libs/store/src", "compilerOptions": {"tsConfigPath": "libs/store/tsconfig.lib.json"}}, "user": {"type": "library", "root": "libs/user", "entryFile": "index", "sourceRoot": "libs/user/src", "compilerOptions": {"tsConfigPath": "libs/user/tsconfig.lib.json"}}}}