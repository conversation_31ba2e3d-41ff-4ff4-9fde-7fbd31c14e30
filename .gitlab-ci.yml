stages:
  - lint
  - build
  # - test
  - deploy

# Ensure only one pipeline runs at a time per branch
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == "dev"
    - if: $CI_COMMIT_BRANCH == "stg"
    - if: $CI_COMMIT_BRANCH == "prod"
    - if: $CI_COMMIT_BRANCH =~ /^release\/.*$/
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*$/
    - if: $CI_COMMIT_BRANCH =~ /^hotfix\/.*$/

# Lint stage
lint:
  stage: lint
  image: node:22.16-alpine
  resource_group: lint
  cache:
    key: '$CI_COMMIT_REF_SLUG'
    paths:
      - node_modules/
      - .yarn-cache/
    policy: pull-push
  before_script:
    - apk add --no-cache git
    - yarn config set cache-folder .yarn-cache
    - yarn install --frozen-lockfile
  script:
    - yarn lint
    - yarn check-types
  only:
    - master
    - dev
    - stg
    - prod
    - release/*
    - merge_requests

# Build stage - Push to GitLab Container Registry
build:
  stage: build
  image: docker:latest
  resource_group: build
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_CERT_PATH: ''
    REGISTRY: $CI_REGISTRY
    IMAGE_TAG: $CI_COMMIT_SHA
  before_script:
    - echo "🔍 Debug Docker environment..."
    - echo "DOCKER_HOST $DOCKER_HOST"
    - echo "DOCKER_TLS_CERTDIR $DOCKER_TLS_CERTDIR"
    - echo "DOCKER_TLS_VERIFY $DOCKER_TLS_VERIFY"
    - echo "DOCKER_CERT_PATH $DOCKER_CERT_PATH"
    - echo "🔍 Waiting for Docker daemon..."
    - until docker info; do sleep 2; done
    - echo "✅ Docker is ready!"
    - echo "🔐 Logging into GitLab Container Registry with CI_JOB_TOKEN..."
    - echo $CI_JOB_TOKEN | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY
  script:
    - echo "🔨 Building and pushing API image..."
    - docker build -t $REGISTRY/$CI_PROJECT_PATH/backend_laundry-api:$IMAGE_TAG -f docker/app/Dockerfile --build-arg APP_SERVICE=api .
    - docker push $REGISTRY/$CI_PROJECT_PATH/backend_laundry-api:$IMAGE_TAG
    - echo "🔨 Building and pushing Schedule image..."
    - docker build -t $REGISTRY/$CI_PROJECT_PATH/backend_laundry-schedule:$IMAGE_TAG -f docker/app/Dockerfile --build-arg APP_SERVICE=schedule .
    - docker push $REGISTRY/$CI_PROJECT_PATH/backend_laundry-schedule:$IMAGE_TAG
    - echo "📋 Creating image metadata..."
    - mkdir -p images
    - echo "API_IMAGE=$REGISTRY/$CI_PROJECT_PATH/backend_laundry-api:$IMAGE_TAG" > images/metadata.txt
    - echo "SCHEDULE_IMAGE=$REGISTRY/$CI_PROJECT_PATH/backend_laundry-schedule:$IMAGE_TAG" >> images/metadata.txt
    - echo "BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> images/metadata.txt
    - echo "COMMIT_SHA=$CI_COMMIT_SHA" >> images/metadata.txt
    - cat images/metadata.txt
  artifacts:
    paths:
      - images/metadata.txt
    expire_in: 1 day
    reports:
      dotenv: images/metadata.txt
  only:
    - master
    - dev
    - stg
    - prod
    - release/*

# E2E Test stage
# test:
#   stage: test
#   image: docker:latest
#   resource_group: test
#   services:
#     - docker:dind
#   variables:
#     DOCKER_HOST: tcp://docker:2375
#     DOCKER_TLS_CERTDIR: ''
#     DOCKER_CERT_PATH: ''
#   before_script:
#     - echo "🔍 Debug Docker environment..."
#     - echo "DOCKER_HOST $DOCKER_HOST"
#     - echo "DOCKER_TLS_CERTDIR $DOCKER_TLS_CERTDIR"
#     - echo "DOCKER_TLS_VERIFY $DOCKER_TLS_VERIFY"
#     - echo "DOCKER_CERT_PATH $DOCKER_CERT_PATH"
#     - echo "🔍 Waiting for Docker daemon..."
#     - until docker info; do sleep 2; done
#     - echo "✅ Docker is ready!"

#   script:
#     - echo "🧪 Running E2E tests using built API image..."
#     - echo "🔍 Checking if API image exists locally..."
#     - docker images | grep backend_laundry-api || echo "Image not found locally, will use from registry"
#     - echo "🚀 Starting test environment with built API image..."
#     - cp env.test .env
#     - cat .env
#     - echo "🔧 Starting database services first..."
#     - docker-compose -f docker-compose.yml up -d postgres redis
#     - echo "⏳ Waiting for database services to be ready..."
#     - sh -c "until docker exec backend_laundry_postgres pg_isready -U upostgres; do sleep 2; done"
#     - sh -c "until docker exec backend_laundry_redis redis-cli ping; do sleep 2; done"
#     - echo "✅ Database services ready! Starting API container..."
#     - docker-compose -f docker-compose.yml up -d api
#     - echo "🔍 Checking container status..."
#     - docker ps -a
#     - echo "🔍 Checking if API container is running..."
#     - docker ps | grep backend_laundry_api || (echo "❌ API container is not running! Showing logs:" && docker logs backend_laundry_api && exit 1)
#     - echo "⏳ Waiting for API to be healthy..."
#     - sh -c "until docker exec backend_laundry_api curl -f http://localhost:9000/health; do echo 'API not ready, checking container status...'; docker ps | grep backend_laundry_api || echo 'Container not running!'; docker logs --tail=10 backend_laundry_api || echo 'No logs available'; sleep 5; done"
#     - echo "✅ API is healthy! Container status:"
#     - docker ps | grep backend_laundry_api
#     - echo "📋 Last 100 lines of API container logs:"
#     - docker logs --tail=200 backend_laundry_api
#     - echo "🧪 Running E2E tests inside built API container..."
#     - docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e

#   after_script:
#     - echo "🧹 Cleaning up test environment..."
#     - docker-compose -f docker-compose.yml down -v
#     - docker system prune -f

#   coverage: '/All files[^|]*\|[^|]*\|[^|]*\|[^|]*\s+(\d+)/'
#   artifacts:
#     paths:
#       - coverage/
#     expire_in: 1 day
#   only:
#     - master
#     - dev
#     - stg
#     - prod
#     - release/*

# Deploy to production - Copy images and deploy
deploy-production:
  stage: deploy
  image: alpine:latest
  resource_group: deploy-production
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  before_script:
    - apk add --no-cache openssh-client rsync
    - echo "🔍 Debug Checking environment variables..."
    - echo "PROD_USER $PROD_USER"
    - echo "PROD_HOST $PROD_HOST"
    - echo "PROD_PATH $PROD_PATH"
    - echo "CI_JOB_TOKEN $CI_JOB_TOKEN"
    - echo "CI_REGISTRY $CI_REGISTRY"
    - echo "API_IMAGE $API_IMAGE"
    - echo "SCHEDULE_IMAGE $SCHEDULE_IMAGE"
    - echo "🔍 Debug Checking if PROD_HOST is empty..."
    - if [ -z "$PROD_HOST" ]; then echo "❌ PROD_HOST is empty!"; exit 1; fi
    - if [ -z "$PROD_USER" ]; then echo "❌ PROD_USER is empty!"; exit 1; fi
    - if [ -z "$PROD_PATH" ]; then echo "❌ PROD_PATH is empty!"; exit 1; fi
    - eval $(ssh-agent -s)
    - echo "$PROD_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - echo "🔍 Debug SSH agent status..."
    - ssh-add -l
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "🔍 Debug Running ssh-keyscan for $PROD_HOST..."
    - ssh-keyscan -H $PROD_HOST >> ~/.ssh/known_hosts || echo "⚠️ ssh-keyscan failed, but continuing..."
    - chmod 644 ~/.ssh/known_hosts
    - echo "🔍 Debug Testing SSH connection..."
    - ssh -o ConnectTimeout=10 -o BatchMode=yes $PROD_USER@$PROD_HOST "echo 'SSH connection successful'" || echo "❌ SSH connection failed"
  script:
    - echo "📦 Deploying to production server..."
    - echo "🔐 Logging into GitLab Container Registry on server with CI_JOB_TOKEN..."
    - ssh $PROD_USER@$PROD_HOST "echo $CI_JOB_TOKEN | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY"
    - echo "📥 Pulling images from registry on server..."
    - ssh $PROD_USER@$PROD_HOST "docker pull $API_IMAGE && docker tag $API_IMAGE backend_laundry-api:latest"
    - ssh $PROD_USER@$PROD_HOST "docker pull $SCHEDULE_IMAGE && docker tag $SCHEDULE_IMAGE backend_laundry-schedule:latest"
    - echo "✅ Images pulled and tagged successfully!"
    - echo "🚀 Deploying to production server..."
    - ssh $PROD_USER@$PROD_HOST "cd $PROD_PATH && chmod +x deploy-server.sh && ./deploy-server.sh"
    - echo "✅ Production deployment completed successfully!"
  dependencies:
    - build
  only:
    - master
    - prod
  when: manual

# Deploy to staging
deploy-stg:
  stage: deploy
  image: alpine:latest
  resource_group: deploy-staging
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  before_script:
    - apk add --no-cache openssh-client rsync
    - echo "🔍 Debug Checking environment variables..."
    - echo "STG_USER $STG_USER"
    - echo "STG_HOST $STG_HOST"
    - echo "STG_PATH $STG_PATH"
    - echo "CI_JOB_TOKEN $CI_JOB_TOKEN"
    - echo "CI_REGISTRY $CI_REGISTRY"
    - echo "API_IMAGE $API_IMAGE"
    - echo "SCHEDULE_IMAGE $SCHEDULE_IMAGE"
    - echo "🔍 Debug Checking if STG_HOST is empty..."
    - if [ -z "$STG_HOST" ]; then echo "❌ STG_HOST is empty!"; exit 1; fi
    - if [ -z "$STG_USER" ]; then echo "❌ STG_USER is empty!"; exit 1; fi
    - if [ -z "$STG_PATH" ]; then echo "❌ STG_PATH is empty!"; exit 1; fi
    - eval $(ssh-agent -s)
    - echo "$STG_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - echo "🔍 Debug SSH agent status..."
    - ssh-add -l
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "🔍 Debug Running ssh-keyscan for $STG_HOST..."
    - ssh-keyscan -H $STG_HOST >> ~/.ssh/known_hosts || echo "⚠️ ssh-keyscan failed, but continuing..."
    - chmod 644 ~/.ssh/known_hosts
    - echo "🔍 Debug Testing SSH connection..."
    - ssh -o ConnectTimeout=10 -o BatchMode=yes $STG_USER@$STG_HOST "echo 'SSH connection successful'" || echo "❌ SSH connection failed"
  script:
    - echo "📦 Deploying to staging server..."
    - echo "🔐 Logging into GitLab Container Registry on server with CI_JOB_TOKEN..."
    - ssh $STG_USER@$STG_HOST "echo $CI_JOB_TOKEN | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY"
    - echo "📥 Pulling images from registry on server..."
    - ssh $STG_USER@$STG_HOST "docker pull $API_IMAGE && docker tag $API_IMAGE backend_laundry-api:latest"
    - ssh $STG_USER@$STG_HOST "docker pull $SCHEDULE_IMAGE && docker tag $SCHEDULE_IMAGE backend_laundry-schedule:latest"
    - echo "✅ Images pulled and tagged successfully!"
    - echo "🚀 Deploying to staging server..."
    - ssh $STG_USER@$STG_HOST "cd $STG_PATH && chmod +x deploy-server.sh && ./deploy-server.sh"
    - echo "✅ Staging deployment completed successfully!"
  dependencies:
    - build
  only:
    - stg
  when: manual

# Deploy to dev
deploy-dev:
  stage: deploy
  image: alpine:latest
  resource_group: deploy-dev
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  before_script:
    - apk add --no-cache openssh-client rsync
    - echo "🔍 Debug Checking environment variables..."
    - echo "DEV_USER $DEV_USER"
    - echo "DEV_HOST $DEV_HOST"
    - echo "DEV_PATH $DEV_PATH"
    - echo "CI_JOB_TOKEN $CI_JOB_TOKEN"
    - echo "CI_REGISTRY $CI_REGISTRY"
    - echo "API_IMAGE $API_IMAGE"
    - echo "SCHEDULE_IMAGE $SCHEDULE_IMAGE"
    - echo "🔍 Debug Checking if DEV_HOST is empty..."
    - if [ -z "$DEV_HOST" ]; then echo "❌ DEV_HOST is empty!"; exit 1; fi
    - if [ -z "$DEV_USER" ]; then echo "❌ DEV_USER is empty!"; exit 1; fi
    - if [ -z "$DEV_PATH" ]; then echo "❌ DEV_PATH is empty!"; exit 1; fi
    - eval $(ssh-agent -s)
    - echo "$DEV_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - echo "🔍 Debug SSH agent status..."
    - ssh-add -l
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "🔍 Debug Running ssh-keyscan for $DEV_HOST..."
    - ssh-keyscan -H $DEV_HOST >> ~/.ssh/known_hosts || echo "⚠️ ssh-keyscan failed, but continuing..."
    - chmod 644 ~/.ssh/known_hosts
    - echo "🔍 Debug Testing SSH connection..."
    - ssh -o ConnectTimeout=10 -o BatchMode=yes $DEV_USER@$DEV_HOST "echo 'SSH connection successful'" || echo "❌ SSH connection failed"
  script:
    - echo "📦 Deploying to dev server..."
    - echo "🔐 Logging into GitLab Container Registry on server with CI_JOB_TOKEN..."
    - ssh $DEV_USER@$DEV_HOST "echo $CI_JOB_TOKEN | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY" || echo "⚠️ Docker login failed, retrying..."
    - echo "📥 Pulling images from registry on server..."
    - ssh $DEV_USER@$DEV_HOST "docker pull $API_IMAGE && docker tag $API_IMAGE backend_laundry-api:latest" || echo "⚠️ API image pull failed"
    - ssh $DEV_USER@$DEV_HOST "docker pull $SCHEDULE_IMAGE && docker tag $SCHEDULE_IMAGE backend_laundry-schedule:latest" || echo "⚠️ Schedule image pull failed"
    - echo "✅ Images pulled and tagged successfully!"
    - echo "🚀 Deploying to dev server..."
    - ssh $DEV_USER@$DEV_HOST "cd $DEV_PATH && chmod +x deploy-server.sh && ./deploy-server.sh"
    - echo "✅ Dev deployment completed successfully!"
  dependencies:
    - build
  only:
    - dev
    - release/*
