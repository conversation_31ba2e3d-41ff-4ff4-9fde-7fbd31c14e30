volumes:
  local_redis: {}
  local_postgres: {}

services:
  # The API
  api:
    container_name: backend_laundry_api
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      args:
        APP_PORT: 9000
        APP_SERVICE: api
    working_dir: /app
    volumes:
      - ./:/app
    ports:
      - '9000:9000'
    entrypoint: /bin/sh ./scripts/start-service.sh api dev
    env_file:
      - ${ENV_FILE:-.env}
    environment:
      APP_URL: 'http://localhost:9000'
      APP_PORT: 9000
      APP_NAME: api
    tty: true
    networks:
      - backend_laundry_network
    depends_on:
      - postgres
      - redis

  # The Schedule
  schedule:
    container_name: backend_laundry_schedule
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      args:
        APP_SERVICE: schedule
    working_dir: /app
    volumes:
      - ./:/app
    entrypoint: /bin/sh ./scripts/start-service.sh schedule dev
    env_file:
      - ${ENV_FILE:-.env}
    environment:
      APP_NAME: schedule
    tty: true
    networks:
      - backend_laundry_network
    depends_on:
      - postgres
      - redis

  # The Database
  postgres:
    container_name: backend_laundry_postgres
    build:
      context: ./
      dockerfile: ./docker/postgres/Dockerfile
    volumes:
      - local_postgres:/var/lib/postgresql/data:Z
      - ./docker/postgres/initdb:/docker-entrypoint-initdb.d/
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    env_file:
      - ${ENV_FILE:-.env}
    ports:
      - '5432:5432'
    tty: true
    networks:
      - backend_laundry_network

  redis:
    container_name: backend_laundry_redis
    build:
      context: ./
      dockerfile: ./docker/redis/Dockerfile
    ports:
      - '${REDIS_PORT}:6379'
    volumes:
      - local_redis:/data
    tty: true
    env_file:
      - ${ENV_FILE:-.env}
    networks:
      - backend_laundry_network

networks:
  backend_laundry_network:
    name: backend_laundry_network
    driver: bridge
