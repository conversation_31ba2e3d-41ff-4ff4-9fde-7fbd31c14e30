# Testing Guide

## Overview

Hướng dẫn chạy test cho backend API trong môi trường Docker.

## Prerequisites

- Docker và Docker Compose đã được cài đặt
- Các service cần thiết: PostgreSQL, Redis, API

## Environment Setup

### 1. Test Environment Variables

File `env.test` chứa các biến môi trường cho testing:

```bash
# Database
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=laundry_test

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# JWT
JWT_ACCESS_SECRET=test-access-secret
JWT_REFRESH_SECRET=test-refresh-secret

# Other configurations...
```

## Running Tests

### 1. Chạy Tất Cả E2E Tests

```bash
# Sử dụng script có sẵn
./scripts/test-e2e.sh

# Hoặc chạy thủ công
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e
```

### 2. Chạy Test Cụ Thể

```bash
# Chạy test cho một file cụ thể
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e -- --testPathPattern=user-delete.e2e-spec.ts

# Chạy test cho một thư mục
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e -- --testPathPattern=user/

# Chạy test với pattern
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e -- --testNamePattern="should delete user successfully"
```

### 3. Chạy Unit Tests

```bash
docker exec -e NODE_ENV=test backend_laundry_api yarn test
```

### 4. Chạy Test Coverage

```bash
docker exec -e NODE_ENV=test backend_laundry_api yarn test:cov
```

## Test Structure

### E2E Tests

```
test/
├── e2e/
│   ├── auth/                    # Authentication tests
│   │   ├── auth-login.e2e-spec.ts
│   │   ├── auth-register.e2e-spec.ts
│   │   └── ...
│   ├── user/                    # User management tests
│   │   ├── user-delete.e2e-spec.ts
│   │   ├── user-update.e2e-spec.ts
│   │   └── ...
│   ├── helpers/                 # Test helpers
│   │   └── user-test.helper.ts
│   └── setup/                   # Test setup
│       ├── global-setup.ts
│       └── jest-e2e.setup.ts
```

### Test Helpers

- `UserTestHelper`: Helper class để tạo test users và authentication
- `global.getSharedTestApp()`: Shared test application để tối ưu performance
- `global.clearTestDatabase()`: Clear database trước mỗi test

## Test Best Practices

### 1. E2E Testing Core Principles

- **NEVER mock core business services** (AuthService, UserService, etc.)
- **ALWAYS use real database operations**
- **ALWAYS use real validation pipes and decorators**
- **ONLY mock external dependencies** (HttpService, SESService, S3Service, etc.)

### 2. Test Setup Template

```typescript
describe('ControllerName (e2e) - Endpoint Description', () => {
    let app: INestApplication;
    let authService: AuthService;
    let userService: UserService;
    let testHelper: UserTestHelper;

    beforeAll(async () => {
        // Use shared test app for better performance
        app = await global.getSharedTestApp();
        authService = app.get<AuthService>(AuthService);
        userService = app.get<UserService>(UserService);
        testHelper = new UserTestHelper(app, authService, userService);
    });

    beforeEach(async () => {
        await global.clearTestDatabase(); // REQUIRED: Always clear database before each test
        await global.clearThrottlingData(); // OPTIONAL: Only if testing rate-limited endpoints
    });

    afterEach(async () => {
        await global.clearThrottlingData(); // OPTIONAL: Only if testing rate-limited endpoints
    });
});
```

### 3. Test Cases Structure

```typescript
describe('POST /endpoint', () => {
    describe('Success cases', () => {
        it('should perform action successfully', async () => {
            // Test implementation
        });
    });

    describe('Error cases', () => {
        it('should return 422 for validation errors', async () => {
            // Test implementation
        });

        it('should return 404 for not found', async () => {
            // Test implementation
        });

        it('should return 409 for conflict', async () => {
            // Test implementation
        });

        it('should return 401 for unauthorized', async () => {
            // Test implementation
        });

        it('should return 429 when rate limit exceeded', async () => {
            // Test implementation
        });
    });

    describe('Edge cases and boundary conditions', () => {
        it('should handle malformed JSON', async () => {
            // Test implementation
        });

        it('should handle empty payload', async () => {
            // Test implementation
        });

        it('should handle null values', async () => {
            // Test implementation
        });

        it('should handle undefined values', async () => {
            // Test implementation
        });

        it('should handle whitespace-only values', async () => {
            // Test implementation
        });

        it('should handle maximum length values', async () => {
            // Test implementation
        });

        it('should handle special characters in inputs', async () => {
            // Test implementation
        });
    });
});
```

## Common Test Commands

### Development

```bash
# Chạy test trong watch mode
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e --watch

# Chạy test với verbose output
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e --verbose

# Chạy test với coverage
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e --coverage
```

### Debugging

```bash
# Chạy test với debug logs
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e --detectOpenHandles

# Chạy test với timeout tăng
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e --testTimeout=30000
```