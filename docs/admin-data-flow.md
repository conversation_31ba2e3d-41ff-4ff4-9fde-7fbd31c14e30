# 🔧 FLOW ADMIN QUẢN LÝ DỮ LIỆU HỆ THỐNG

## 1. QUẢN LÝ CỬA HÀNG (STORES)

### Tạo cửa hàng mới
```sql
-- Tạo store mới
INSERT INTO STORES (
  id: 'store-uuid',
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Hà Nội Central',
  code: 'HN001',
  address: '123 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Hà Nội',
  phone: '024-3936-1234',
  manager_name: '<PERSON><PERSON><PERSON><PERSON>',
  operating_hours: {
    "monday": "06:00-22:00",
    "tuesday": "06:00-22:00",
    "wednesday": "06:00-22:00", 
    "thursday": "06:00-22:00",
    "friday": "06:00-22:00",
    "saturday": "07:00-23:00",
    "sunday": "08:00-20:00"
  },
  facilities: ["free_wifi", "parking", "waiting_area", "vending_machine"],
  status: 'active',
  latitude: 21.0285,
  longitude: 105.8542,
  created_at: NOW()
);
```

### Cập nhật thông tin cửa hàng
```sql
-- Update store information
UPDATE STORES 
SET 
  manager_name = '<PERSON><PERSON><PERSON><PERSON> B',
  phone = '024-3936-5678',
  operating_hours = operating_hours || '{"saturday": "06:00-23:00"}',
  updated_at = NOW()
WHERE id = 'store-uuid';
```

### Deactivate cửa hàng
```sql
-- Deactivate store (soft delete)
UPDATE STORES 
SET status = 'inactive', updated_at = NOW()
WHERE id = 'store-uuid';

-- Deactivate tất cả máy giặt của store
UPDATE WASHING_MACHINES 
SET status = 'maintenance', updated_at = NOW()
WHERE store_id = 'store-uuid';
```

---

## 2. QUẢN LÝ MÁY GIẶT (WASHING_MACHINES)

### Thêm máy giặt mới
```sql
-- Thêm máy giặt vào store
INSERT INTO WASHING_MACHINES (
  id: 'machine-uuid',
  store_id: 'store-uuid',
  code: 'HN001-A01',
  model: 'Samsung WF45R6300AV',
  type: 'large_capacity',
  status: 'available',
  capacity_kg: 20,
  features: {
    "steam_cleaning": true,
    "auto_detergent_dispenser": true,
    "smart_control": true,
    "inverter_motor": true,
    "wifi_connectivity": true
  },
  options: {
    "temperature_range": {"min": 20, "max": 90, "available": [20, 30, 40, 60, 90]},
    "spin_speeds": [400, 800, 1000, 1200, 1400],
    "water_levels": ["low", "medium", "high", "auto"],
    "wash_intensities": ["gentle", "normal", "heavy"]
  },
  created_at: NOW()
);
```

### Cập nhật trạng thái máy
```sql
-- Đưa máy vào bảo trì
UPDATE WASHING_MACHINES 
SET 
  status = 'maintenance',
  last_maintenance_at = NOW()
WHERE id = 'machine-uuid';

-- Máy hoàn thành bảo trì
UPDATE WASHING_MACHINES 
SET status = 'available'
WHERE id = 'machine-uuid';

-- Máy bị hỏng
UPDATE WASHING_MACHINES 
SET status = 'broken'
WHERE id = 'machine-uuid';
```

---

## 3. QUẢN LÝ CHƯƠNG TRÌNH MÁY GIẶT (MACHINE_PROGRAMS)

### Bước 1: Tạo Service Products trước
```sql
-- Tạo Normal Wash service product
INSERT INTO PRODUCTS (
  id: 'normal-wash-service',
  name: 'Normal Wash',
  slug: 'normal-wash',
  type: 'service',
  category: 'laundry_service',
  price_points: 50,
  stock_quantity: NULL,  -- Services không có stock
  status: 'active',
  image_url: 'https://example.com/normal-wash.jpg',
  description: 'Standard washing service for everyday clothes',
  created_at: NOW()
);

-- Tạo Delicate Wash service product
INSERT INTO PRODUCTS (
  id: 'delicate-wash-service',
  name: 'Delicate Wash',
  slug: 'delicate-wash',
  type: 'service',
  category: 'laundry_service',
  price_points: 65,
  stock_quantity: NULL,
  status: 'active',
  image_url: 'https://example.com/delicate-wash.jpg',
  description: 'Gentle washing service for delicate fabrics',
  created_at: NOW()
);

-- Tạo Quick Wash service product
INSERT INTO PRODUCTS (
  id: 'quick-wash-service',
  name: 'Quick Wash',
  slug: 'quick-wash',
  type: 'service',
  category: 'laundry_service',
  price_points: 40,
  stock_quantity: NULL,
  status: 'active',
  image_url: 'https://example.com/quick-wash.jpg',
  description: 'Fast 30-minute washing service',
  created_at: NOW()
);

-- Tạo Heavy Duty service product
INSERT INTO PRODUCTS (
  id: 'heavy-duty-service',
  name: 'Heavy Duty Wash',
  slug: 'heavy-duty-wash',
  type: 'service',
  category: 'laundry_service',
  price_points: 70,
  stock_quantity: NULL,
  status: 'active',
  image_url: 'https://example.com/heavy-duty.jpg',
  description: 'Intensive washing for heavily soiled items',
  created_at: NOW()
);
```

### Bước 2: Tạo Machine Programs
```sql
-- Tạo Normal Wash program cho máy A01
INSERT INTO MACHINE_PROGRAMS (
  id: 'program-normal-uuid',
  machine_id: 'machine-uuid',
  product_id: 'normal-wash-service',  -- Link đến product đã tạo ở bước 1
  name: 'Normal Wash',
  code: 'NORMAL',
  duration_minutes: 45,
  settings: {
    "temperature": 40,
    "spin_speed": 1000,
    "water_level": "medium",
    "wash_intensity": "normal",
    "pre_wash": false,
    "extra_rinse": 1
  },
  features: {
    "steam_refresh": false,
    "stain_removal": false,
    "fabric_softener": true,
    "anti_wrinkle": false,
    "eco_mode": false
  },
  is_active: true,
  display_order: 1,
  created_at: NOW()
);

-- Tạo Delicate Wash program cho cùng máy
INSERT INTO MACHINE_PROGRAMS (
  id: 'program-delicate-uuid',
  machine_id: 'machine-uuid',
  product_id: 'delicate-wash-service',  -- Link đến product đã tạo ở bước 1
  name: 'Delicate Wash',
  code: 'DELICATE',
  duration_minutes: 60,
  settings: {
    "temperature": 30,
    "spin_speed": 400,
    "water_level": "high",
    "wash_intensity": "gentle",
    "pre_wash": true,
    "extra_rinse": 2
  },
  features: {
    "steam_refresh": true,
    "fabric_softener": true,
    "anti_wrinkle": true,
    "eco_mode": false
  },
  is_active: true,
  display_order: 2,
  created_at: NOW()
);

-- Tạo Quick Wash program
INSERT INTO MACHINE_PROGRAMS (
  id: 'program-quick-uuid',
  machine_id: 'machine-uuid',
  product_id: 'quick-wash-service',  -- Link đến product đã tạo ở bước 1
  name: 'Quick Wash',
  code: 'QUICK',
  duration_minutes: 30,
  settings: {
    "temperature": 20,
    "spin_speed": 1200,
    "water_level": "low",
    "wash_intensity": "normal",
    "pre_wash": false,
    "extra_rinse": 0
  },
  features: {
    "steam_refresh": false,
    "fabric_softener": false,
    "anti_wrinkle": false,
    "eco_mode": true
  },
  is_active: true,
  display_order: 3,
  created_at: NOW()
);
```

### Bước 3: Setup Programs cho nhiều máy
```sql
-- Tạo programs cho máy Standard (chỉ basic programs)
INSERT INTO MACHINE_PROGRAMS (
  machine_id, product_id, name, code, duration_minutes, settings, features, is_active, display_order, created_at
) VALUES 
-- Normal Wash cho máy standard
('standard-machine-uuid', 'normal-wash-service', 'Normal Wash', 'NORMAL', 45, 
 '{"temperature": 40, "spin_speed": 800, "water_level": "medium"}',
 '{"fabric_softener": true}', true, 1, NOW()),
-- Quick Wash cho máy standard  
('standard-machine-uuid', 'quick-wash-service', 'Quick Wash', 'QUICK', 35,
 '{"temperature": 30, "spin_speed": 1000, "water_level": "low"}', 
 '{"eco_mode": true}', true, 2, NOW());

-- Tạo programs cho máy Premium (tất cả programs + advanced features)
INSERT INTO MACHINE_PROGRAMS (
  machine_id, product_id, name, code, duration_minutes, settings, features, is_active, display_order, created_at
) VALUES 
-- Normal Wash cho máy premium
('premium-machine-uuid', 'normal-wash-service', 'Normal Wash', 'NORMAL', 45,
 '{"temperature": 40, "spin_speed": 1200, "water_level": "auto", "wash_intensity": "normal"}',
 '{"steam_refresh": true, "fabric_softener": true, "anti_wrinkle": true}', true, 1, NOW()),
-- Delicate Wash cho máy premium
('premium-machine-uuid', 'delicate-wash-service', 'Delicate Wash', 'DELICATE', 60,
 '{"temperature": 20, "spin_speed": 400, "water_level": "high", "wash_intensity": "gentle"}',
 '{"steam_refresh": true, "fabric_softener": true, "anti_wrinkle": true, "stain_removal": true}', true, 2, NOW()),
-- Quick Wash cho máy premium
('premium-machine-uuid', 'quick-wash-service', 'Quick Wash', 'QUICK', 25,
 '{"temperature": 30, "spin_speed": 1400, "water_level": "auto"}',
 '{"steam_refresh": true, "eco_mode": true}', true, 3, NOW()),
-- Heavy Duty cho máy premium  
('premium-machine-uuid', 'heavy-duty-service', 'Heavy Duty', 'HEAVY', 90,
 '{"temperature": 60, "spin_speed": 1000, "water_level": "high", "wash_intensity": "heavy"}',
 '{"steam_refresh": true, "stain_removal": true, "sanitize": true}', true, 4, NOW());
```

### Cập nhật program settings
```sql
-- Điều chỉnh settings cho program
UPDATE MACHINE_PROGRAMS 
SET 
  duration_minutes = 50,  -- Tăng thời gian từ 45 lên 50 phút
  settings = settings || '{"extra_rinse": 2}',  -- Thêm extra rinse
  updated_at = NOW()
WHERE id = 'program-uuid';
```

### Enable/Disable programs
```sql
-- Disable program tạm thời
UPDATE MACHINE_PROGRAMS 
SET is_active = false, updated_at = NOW()
WHERE id = 'program-uuid';

-- Enable lại program
UPDATE MACHINE_PROGRAMS 
SET is_active = true, updated_at = NOW()
WHERE id = 'program-uuid';
```

### Sao chép programs sang máy khác
```sql
-- Copy tất cả programs từ máy A sang máy B
INSERT INTO MACHINE_PROGRAMS (
  id, machine_id, product_id, name, code, duration_minutes,
  settings, features, is_active, display_order, created_at
)
SELECT 
  gen_random_uuid(),        -- New UUID
  'new-machine-uuid',       -- New machine
  product_id, name, code, duration_minutes,
  settings, features, is_active, display_order, NOW()
FROM MACHINE_PROGRAMS 
WHERE machine_id = 'source-machine-uuid'
AND is_active = true;
```

---

## 4. QUẢN LÝ SẢN PHẨM/DỊCH VỤ KHÁC (PRODUCTS)

### Tạo service product
```sql
-- Các service products đã được tạo trong phần MACHINE_PROGRAMS ở trên
-- Đây là các products khác cho e-commerce
```

### Tạo physical product
```sql
-- Tạo physical product (detergent)
INSERT INTO PRODUCTS (
  id: 'detergent-ariel-1.2l',
  name: 'Ariel Liquid Detergent 1.2L',
  slug: 'ariel-liquid-detergent-1-2l',
  type: 'physical', 
  category: 'detergent',
  price_points: 25,
  stock_quantity: 100,
  status: 'active',
  image_url: 'https://example.com/ariel-1.2l.jpg',
  description: 'Premium liquid detergent for effective stain removal',
  created_at: NOW()
);
```

### Cập nhật giá sản phẩm
```sql
-- Update product pricing
UPDATE PRODUCTS 
SET 
  price_points = 55,  -- Tăng giá từ 50 lên 55
  updated_at = NOW()
WHERE id = 'normal-wash-service';
```

### Quản lý stock
```sql
-- Update stock cho physical products
UPDATE PRODUCTS 
SET 
  stock_quantity = stock_quantity - 10,  -- Bán 10 units
  updated_at = NOW()
WHERE id = 'detergent-ariel-1.2l';

-- Nhập hàng
UPDATE PRODUCTS 
SET 
  stock_quantity = stock_quantity + 50,  -- Nhập thêm 50 units
  updated_at = NOW()
WHERE id = 'detergent-ariel-1.2l';

-- Hết hàng
UPDATE PRODUCTS 
SET status = 'out_of_stock'
WHERE stock_quantity = 0 AND type = 'physical';
```

---

## 5. QUẢN LÝ USER & PAYMENT

### Xem thông tin user và balance
```sql
-- User overview với payment info
SELECT 
  u.id,
  u.username,
  u.name,
  u.email,
  u.phone,
  u.status as user_status,
  pu.total_points,
  u.created_at as user_created,
  u.verified_at
FROM USERS u
LEFT JOIN PAYMENT_USERS pu ON u.id = pu.user_id
WHERE u.status = 'active'
ORDER BY u.created_at DESC;
```

### Điều chỉnh points cho user
```sql
-- Admin adjustment - thêm points cho user
INSERT INTO PAYMENT_TRANSACTIONS (
  id: 'trans-uuid',
  user_id: 'user-uuid',
  order_id: NULL,  -- Không liên quan đến order
  type: 'ADJUSTMENT',
  amount: 50000,   -- 50k VND equivalent
  point: 50,       -- Thêm 50 points
  exchange_rate: 1000.0,
  currency: 'VND',
  payment_method: 'adjustment',
  status: 'success',
  description: 'Admin adjustment - Customer complaint compensation',
  created_at: NOW()
);

-- Update balance record
INSERT INTO PAYMENT_TRANSACTION_BALANCES (
  id: 'balance-uuid',
  user_id: 'user-uuid',
  transaction_id: 'trans-uuid',
  balance_before: 25,   -- Balance hiện tại
  balance_after: 75,    -- 25 + 50
  amount_change: 50,
  created_at: NOW()
);

-- Update user total points
UPDATE PAYMENT_USERS 
SET total_points = 75, updated_at = NOW()
WHERE user_id = 'user-uuid';
```

### Suspend user account
```sql
-- Suspend user
UPDATE USERS 
SET status = 'suspended', updated_at = NOW()
WHERE id = 'user-uuid';

-- Reactivate user
UPDATE USERS 
SET status = 'active', updated_at = NOW()
WHERE id = 'user-uuid';
```

---

## 6. MONITORING & REPORTS

### Service sessions analytics
```sql
-- Sessions hôm nay
SELECT 
  COUNT(*) as total_sessions,
  COUNT(CASE WHEN status = 'done' THEN 1 END) as completed_sessions,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_sessions,
  AVG(EXTRACT(EPOCH FROM (completed_at - started_at))/60) as avg_duration_minutes
FROM SERVICE_SESSIONS 
WHERE DATE(created_at) = CURRENT_DATE;

-- Top machines được sử dụng nhiều nhất
SELECT 
  m.code,
  m.model,
  s.name as store_name,
  COUNT(ss.id) as usage_count,
  AVG(EXTRACT(EPOCH FROM (ss.completed_at - ss.started_at))/60) as avg_duration
FROM SERVICE_SESSIONS ss
JOIN WASHING_MACHINES m ON ss.machine_id = m.id
JOIN STORES s ON m.store_id = s.id
WHERE ss.created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY m.id, m.code, m.model, s.name
ORDER BY usage_count DESC
LIMIT 10;
```

### Revenue analytics
```sql
-- Doanh thu theo ngày
SELECT 
  DATE(pt.created_at) as date,
  SUM(pt.amount) as total_revenue_vnd,
  SUM(pt.point) as total_points_used,
  COUNT(*) as transaction_count
FROM PAYMENT_TRANSACTIONS pt
WHERE pt.status = 'success'
AND pt.type IN ('POINT_PAYMENT', 'DIRECT_PAYMENT')
AND pt.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(pt.created_at)
ORDER BY date DESC;

-- Top selling products
SELECT 
  p.name,
  p.type,
  p.category,
  COUNT(oi.id) as order_count,
  SUM(oi.total_points) as total_points_sold
FROM ORDER_ITEMS oi
JOIN PRODUCTS p ON oi.product_id = p.id
JOIN ORDERS o ON oi.order_id = o.id
WHERE o.status = 'completed'
AND o.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY p.id, p.name, p.type, p.category
ORDER BY total_points_sold DESC;
```

### Machine utilization
```sql
-- Machine downtime tracking
SELECT 
  m.code,
  m.model,
  s.name as store_name,
  m.status,
  m.last_used_at,
  EXTRACT(EPOCH FROM (NOW() - m.last_used_at))/3600 as hours_since_last_use,
  m.last_maintenance_at
FROM WASHING_MACHINES m
JOIN STORES s ON m.store_id = s.id
WHERE m.status IN ('maintenance', 'broken')
OR m.last_used_at < NOW() - INTERVAL '24 hours'
ORDER BY hours_since_last_use DESC;
```

---

## 💡 LƯU Ý QUAN TRỌNG

### **1. Data Consistency**
- Luôn sử dụng database transactions cho operations quan trọng
- Validate data trước khi insert/update
- Check foreign key constraints

### **2. Security**
- Admin actions cần authentication và authorization
- Log tất cả admin activities
- Sensitive operations cần approval workflow

### **3. Backup & Recovery**
- Backup data trước khi bulk operations
- Test restore procedures định kỳ
- Monitor system performance

### **4. Business Rules**
- Không delete hard data, chỉ soft delete (status inactive)
- Price changes không affect orders đã tạo
- Machine maintenance cần notify users đang có sessions

Admin flow này đảm bảo quản lý toàn diện và an toàn cho hệ thống! 🚀