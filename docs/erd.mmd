erDiagram
    USER_NOTIFICATIONS {
        UUID id PK "Unique notification identifier"
        UUID user_id FK "Reference to user"
        STRING title "Notification title"
        STRING content "Notification content"
        STRING type "Notification type: email, sms, notification"
        STRING device_token "Device token for push notification"
        BOOLEAN is_read "Read status"
        JSONB data "Additional notification data"
        TIMESTAMP sent_at "Notification sent time"
        TIMES<PERSON>MP read_at "Notification read time"
        TIMES<PERSON><PERSON> created_at "Notification creation time"
    }

    USER_DEVICES {
        UUID id PK "Unique device identifier"
        UUID user_id FK "Reference to user"
        STRING device_token "Push notification token"
        STRING device_type "Device type: ios, android, web"
        STRING device_name "Device name/model"
        STRING app_version "Application version"
        STRING os_version "Operating system version"
        BOOLEAN is_active "Device active status"
        TIMESTAMP last_active_at "Last device activity time"
        TIMESTAMP created_at "Device registration time"
        TIM<PERSON><PERSON><PERSON> updated_at "Device info update time"
    }

    USER_SOCIALS {
        UUID id PK "Unique social connection identifier"
        UUID user_id FK "Reference to user"
        STRING provider "Social provider: google, facebook, apple, zalo"
        STRING provider_id "Provider user ID"
        STRING provider_email "Email from social provider"
        STRING provider_name "Name from social provider"
        STRING avatar_url "Profile picture URL"
        JSONB provider_data "Additional provider data"
        BOOLEAN is_primary "Primary social login method"
        TIMESTAMP connected_at "Social account connection time"
        TIMESTAMP updated_at "Social data update time"
    }

    USERS {
        UUID id PK "Unique user identifier"
        STRING username "Unique username for login"
        STRING name "Full name of user"
        STRING email "User email address"
        STRING password "User password (hashed)"
        STRING phone "User phone number"
        STRING address "User address"
        STRING avatar "User avatar/profile picture URL"
        STRING gender "User gender: male, female, other"
        STRING status "User status: active, inactive, suspended"
        TIMESTAMP created_at "Account creation time"
        TIMESTAMP updated_at "Last account update time"
        TIMESTAMP verified_at "Account verification time"
        TIMESTAMP deleted_at "Account deletion time (soft delete)"
    }

    PAYMENT_USERS {
        UUID user_id PK "Primary key - Reference to user"
        INTEGER total_points "Current point balance"
        TIMESTAMP created_at "Payment account creation time"
        TIMESTAMP updated_at "Last balance update time"
    }

    PAYMENT_TRANSACTION_BALANCES {
        UUID id PK "Unique balance record identifier"
        UUID user_id FK "Reference to user"
        UUID transaction_id FK "Reference to payment transaction"
        INTEGER balance_before "Point balance before transaction"
        INTEGER balance_after "Point balance after transaction"
        INTEGER amount_change "Points changed (+/-)"
        TIMESTAMP created_at "Balance record creation time"
    }

    PAYMENT_TRANSACTIONS {
        UUID id PK "Unique identifier"
        UUID user_id FK "Reference to user"
        UUID order_id FK "Reference to order"
        STRING type "Transaction type: DIRECT_PAYMENT, POINT_PAYMENT, REFUND, ADJUSTMENT"
        INTEGER amount "Amount in original currency (VND)"
        INTEGER point "Points equivalent of amount"
        FLOAT exchange_rate "VND/point exchange rate at transaction time"
        STRING currency "Currency unit (VND, USD, etc)"
        STRING payment_method "Payment method: points, momo, vnpay, cash"
        STRING gateway_transaction_id "Gateway transaction reference"
        STRING status "Transaction status: pending, success, failed"
        STRING description "Transaction description"
        JSONB gateway_response "Gateway response data"
        TIMESTAMP created_at "Transaction timestamp"
    }

    SYSTEM_CONFIGURATIONS {
        UUID id PK "Unique configuration identifier"
        STRING key "Configuration key (unique)"
        STRING value "Configuration value"
        STRING type "Value type: string, number, boolean, json"
        STRING category "Configuration category: notification, session, system"
        STRING description "Configuration description"
        BOOLEAN is_active "Configuration active status"
        TIMESTAMP created_at "Configuration creation time"
        TIMESTAMP updated_at "Configuration update time"
    }

    FAQ_QUESTIONS {
        UUID id PK "Unique FAQ question identifier"
        UUID parent_id FK "Reference to parent question (NULL for master record, points to master for language records)"
        STRING type "Question type: faq, help, tutorial"
        STRING slug "URL-friendly identifier (unique per master record)"
        STRING language "Language code (NULL for master record, en/vi/ja for language records)"
        STRING title "Question title (NULL for master record, filled for language records)"
        TEXT content "Question content/answer (NULL for master record, filled for language records)"
        INTEGER display_order "Display order for sorting"
        BOOLEAN is_active "Question active status"
        BOOLEAN is_expanded "Default expanded state"
        JSONB metadata "Additional question metadata"
        TIMESTAMP created_at "Question creation time"
        TIMESTAMP updated_at "Question update time"
    }

    ORDERS {
        UUID id PK "Unique order identifier"
        UUID user_id FK "Reference to user"
        INTEGER total_points "Total points required"
        INTEGER used_points "Points actually used for payment"
        INTEGER bonus_points "Bonus points earned from this order"
        INTEGER total_amount "Total amount in VND"
        STRING currency "Currency unit (VND, USD)"
        FLOAT exchange_rate "VND/point exchange rate at order time (e.g. 1000)"
        STRING payment_method "Payment method: points, momo, vnpay, cash"
        STRING status "Order status: pending, paid, completed, cancelled"
        TIMESTAMP created_at "Order creation time"
    }

    STORES {
        UUID id PK "Unique store identifier"
        STRING name "Store name"
        STRING code "Store code (e.g. HN001, HCM002)"
        STRING address "Store address"
        STRING phone "Store contact phone"
        STRING manager_name "Store manager name"
        JSONB operating_hours "Store operating hours"
        JSONB facilities "Store facilities and amenities"
        STRING status "Store status: active, inactive, maintenance"
        FLOAT latitude "Store latitude for mapping"
        FLOAT longitude "Store longitude for mapping"
        TIMESTAMP created_at "Store creation time"
        TIMESTAMP updated_at "Store update time"
    }

    MACHINE_PROGRAMS {
        UUID id PK "Unique program identifier"
        UUID machine_id FK "Reference to washing machine"
        UUID product_id FK "Reference to service product"
        STRING name "Program name"
        STRING code "Program code (e.g. NORMAL, DELICATE, QUICK)"
        INTEGER duration_minutes "Program duration in minutes"
        JSONB settings "Program settings (temperature, spin_speed, etc)"
        JSONB features "Program features and extras"
        BOOLEAN is_active "Program active status"
        INTEGER display_order "Display order in UI"
        TIMESTAMP created_at "Program creation time"
        TIMESTAMP updated_at "Program update time"
    }

    PRODUCTS {
        UUID id PK "Unique product identifier"
        STRING name "Product/Service name"
        STRING slug "URL-friendly identifier"
        STRING type "Product type: service, physical"
        STRING category "Product category: laundry_service, detergent, fabric_softener, etc"
        INTEGER price_points "Price in points (base price for services)"
        INTEGER stock_quantity "Available stock (null for services)"
        STRING status "Product status: active, inactive, out_of_stock"
        STRING image_url "Product image URL"
        TEXT description "Product description"
        UUID store_id FK "Reference to store"
        TIMESTAMP created_at "Product creation time"
        TIMESTAMP updated_at "Product update time"
    }

    ORDER_ITEMS {
        UUID id PK "Unique order item identifier"
        UUID order_id FK "Reference to order"
        UUID product_id FK "Reference to product"
        INTEGER quantity "Item quantity or service cycles"
        INTEGER total_points "Total points for this item"
        JSONB metadata "Additional item metadata"
    }

    WASHING_MACHINES {
        UUID id PK "Unique machine identifier"
        UUID store_id FK "Reference to store"
        STRING code "Machine code/number"
        STRING model "Machine model/brand"
        STRING type "Machine type: standard, large_capacity, premium"
        STRING status "Machine status: available, in_use, maintenance, broken"
        INTEGER capacity_kg "Machine capacity in kg"
        JSONB features "Machine hardware features and capabilities"
        JSONB options "Available washing options for this machine"
        TIMESTAMP last_used_at "Last usage timestamp"
        TIMESTAMP last_maintenance_at "Last maintenance timestamp"
        TIMESTAMP created_at "Machine installation time"
    }

    SERVICE_SESSIONS {
        UUID id PK "Unique service session identifier"
        UUID user_id FK "Reference to user"
        UUID order_id FK "Reference to order"
        UUID order_item_id FK "Reference to order item"
        UUID machine_id FK "Reference to washing machine (for service items only)"
        UUID machine_program_id FK "Reference to machine program"
        STRING status "Session status: waiting, in_use, done, cancelled"
        TIMESTAMP started_at "Service start time"
        TIMESTAMP estimated_end_time "Estimated service end time"
        TIMESTAMP completed_at "Service completion time"
        TIMESTAMP created_at "Session creation time"
    }

    %% System Configuration & FAQ Management
    FAQ_QUESTIONS ||--o{ FAQ_QUESTIONS : "master-language relationship"

    %% Store & Machine Management
    STORES ||--o{ WASHING_MACHINES : "has machines"
    STORES ||--o{ PRODUCTS : "has products"
    WASHING_MACHINES ||--o{ MACHINE_PROGRAMS : "has programs"
    MACHINE_PROGRAMS }o--|| PRODUCTS : "references service product"

    %% User Management System (Top Level)
    USER_NOTIFICATIONS ||--|| USERS : "belongs to user"
    USER_DEVICES ||--|| USERS : "belongs to user" 
    USER_SOCIALS ||--|| USERS : "belongs to user"

    %% User & Payment System
    USERS ||--|| PAYMENT_USERS : "has payment account"
    USERS ||--o{ PAYMENT_TRANSACTIONS : "makes transactions"
    PAYMENT_TRANSACTIONS ||--|| PAYMENT_TRANSACTION_BALANCES : "has balance record"

    %% Order & Payment System
    USERS ||--o{ ORDERS : "places orders"
    ORDERS ||--o{ ORDER_ITEMS : "contains items"
    ORDER_ITEMS }o--|| PRODUCTS : "of product/service"
    ORDERS ||--|| PAYMENT_TRANSACTIONS : "paid by transaction"

    %% Service System (only for service products)
    USERS ||--o{ SERVICE_SESSIONS : "uses services"
    ORDERS ||--o{ SERVICE_SESSIONS : "creates sessions"
    ORDER_ITEMS ||--o{ SERVICE_SESSIONS : "creates session"
    SERVICE_SESSIONS }o--o| WASHING_MACHINES : "uses machine (optional)"
    SERVICE_SESSIONS }o--|| MACHINE_PROGRAMS : "follows program"