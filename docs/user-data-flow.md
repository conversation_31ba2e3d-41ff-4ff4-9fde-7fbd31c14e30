# 🔄 FLOW DỮ LIỆU - DỊCH VỤ MÁY GIẶT (USER JOURNEY)

## 1. USER CHỌN STORE & MACHINE

### L<PERSON>a chọn cửa hàng

```sql
-- L<PERSON>a chọn cửa hàng
SELECT * FROM STORES
WHERE status = 'active'
ORDER BY name;
```

### Xem máy available tại store

```sql
-- Xem máy available tại store
SELECT
  m.*,
  m.features as machine_features,
  m.options as machine_options
FROM WASHING_MACHINES m
WHERE m.store_id = 'selected_store_id'
AND m.status = 'available';
```

## 2. USER CHỌN CHƯƠNG TRÌNH GIẶT & TẠO ORDER

### Xem các chương trình có sẵn cho máy

```sql
-- User xem các chương trình giặt có sẵn cho máy này
SELECT
  mp.id as machine_program_id,
  mp.name as program_name,
  mp.code as program_code,
  mp.duration_minutes,
  mp.settings as program_settings,
  mp.features as program_features,
  mp.display_order,
  p.id as product_id,
  p.name as product_name,
  p.price_points,
  p.description
FROM MACHINE_PROGRAMS mp
JOIN PRODUCTS p ON mp.product_id = p.id
WHERE mp.machine_id = 'selected_machine_id'
AND mp.is_active = true
AND p.status = 'active'
ORDER BY mp.display_order;
```

### Tạo order

```sql
-- User chọn chương trình "Delicate Wash" và tạo order
INSERT INTO ORDERS (
  id: 'order-uuid',
  user_id: 'user-uuid',
  total_points: 65,        -- Giá từ PRODUCTS.price_points
  total_amount: 65000,     -- 65 * 1000 exchange_rate
  currency: 'VND',
  exchange_rate: 1000.0,
  payment_method: 'points',
  status: 'pending'
);
```

### Tạo order item

```sql
-- Tạo order item cho chương trình đã chọn
INSERT INTO ORDER_ITEMS (
  id: 'order-item-uuid',
  order_id: 'order-uuid',
  product_id: 'delicate-wash-service', -- Service product user đã chọn
  quantity: 1,
  total_points: 65,                    -- Từ PRODUCTS.price_points
  metadata: {}                         -- Không cần lưu machine_program_id nữa
);
```

## 3. THANH TOÁN

**Quy trình thanh toán (Thanh toán trực tiếp):**

1. **Bước 1**: Tạo payment transaction ghi nhận giao dịch với payment gateway
2. **Bước 2**: Khi giao dịch thành công → Update trạng thái payment transaction
3. **Bước 3**: Tạo payment transaction balance để cộng points (từ payment gateway) và cập nhật payment_users
4. **Bước 4**: Tạo payment transaction balance để trừ points (thanh toán cho order) và cập nhật payment_users
5. **Bước 5**: Emit 'payment.balance.success' event → Update order status = 'paid'

**Quy trình cashback (khi đơn hàng được thanh toán):**

1. **Bước 1**: Khi order status = 'paid'
2. **Bước 2**: Tạo payment transaction với type = 'CASHBACK'
3. **Bước 3**: Tạo payment transaction balance để cộng points từ cashback và cập nhật payment_users

### Tạo payment transaction ghi nhận giao dịch với payment gateway

```sql
-- Tạo payment transaction ghi nhận giao dịch với payment gateway
INSERT INTO PAYMENT_TRANSACTIONS (
  id: 'trans-gateway-uuid',
  user_id: 'user-uuid',
  order_id: 'order-uuid',
  type: 'DIRECT_PAYMENT',
  amount: 65000,           -- VND amount từ order
  point: 0,                -- Chưa có points vì chưa thanh toán
  exchange_rate: 1000.0,
  currency: 'VND',
  payment_method: 'momo',  -- hoặc 'vnpay', 'cash', 'card'
  status: 'pending',
  gateway_transaction_id: 'gateway-ref-123',
  description: 'Payment for delicate wash service via payment gateway'
);
```

### Tạo payment transaction ghi nhận giao dịch với payment gateway

```sql
-- Tạo payment transaction ghi nhận giao dịch với payment gateway
INSERT INTO PAYMENT_TRANSACTIONS (
  id: 'trans-gateway-uuid',
  user_id: 'user-uuid',
  order_id: 'order-uuid',
  type: 'DIRECT_PAYMENT',
  amount: 65000,           -- VND amount từ order
  point: 0,                -- Chưa có points vì chưa thanh toán
  exchange_rate: 1000.0,
  currency: 'VND',
  payment_method: 'momo',  -- hoặc 'vnpay', 'cash', 'card'
  status: 'pending',
  gateway_transaction_id: 'gateway-ref-123',
  description: 'Payment for delicate wash service via payment gateway'
);
```

### Khi giao dịch thành công → Update trạng thái payment transaction và order

```sql
-- Update payment transaction status
UPDATE PAYMENT_TRANSACTIONS
SET
  status = 'success',
  point = 65,              -- 65,000 VND / 1000 = 65 points
  updated_at = NOW()
WHERE id = 'trans-gateway-uuid';

-- Update order status
UPDATE ORDERS
SET status = 'paid'
WHERE id = 'order-uuid';

-- Tạo payment transaction cho cashback (sẽ được xử lý bởi OrderListener trong lib order)
-- INSERT INTO PAYMENT_TRANSACTIONS (type: 'CASHBACK', ...)
-- INSERT INTO PAYMENT_TRANSACTION_BALANCES (amount_change: +1, ...)
-- UPDATE PAYMENT_USERS (total_points = 1, ...)
```

### Tạo payment transaction balance để cộng points (từ payment gateway) và cập nhật payment_users

```sql
-- Tạo balance record cho việc cộng points từ payment gateway
INSERT INTO PAYMENT_TRANSACTION_BALANCES (
  id: 'balance-add-uuid',
  user_id: 'user-uuid',
  transaction_id: 'trans-gateway-uuid',
  balance_before: 0,       -- Ban đầu không có points
  balance_after: 65,       -- 0 + 65
  amount_change: 65,
  description: 'Points added from payment gateway transaction'
);

-- Cập nhật points trong payment_users
UPDATE PAYMENT_USERS
SET total_points = 65, updated_at = NOW()  -- 0 + 65 = 65
WHERE user_id = 'user-uuid';
```

### Tạo payment transaction balance để trừ points (thanh toán cho order) và cập nhật payment_users

```sql
-- Tạo balance record cho việc trừ points thanh toán order
INSERT INTO PAYMENT_TRANSACTION_BALANCES (
  id: 'balance-deduct-uuid',
  user_id: 'user-uuid',
  transaction_id: 'trans-gateway-uuid',
  balance_before: 65,      -- Sau khi cộng points
  balance_after: 0,        -- 65 - 65 = 0 (đã thanh toán hết)
  amount_change: -65,
  description: 'Points deducted for order payment'
);

-- Cập nhật points trong payment_users
UPDATE PAYMENT_USERS
SET total_points = 0, updated_at = NOW()  -- 65 - 65 = 0
WHERE user_id = 'user-uuid';
```

## 4. TẠO SERVICE SESSION

```sql
-- Lấy machine_program từ ORDER_ITEMS và MACHINE_PROGRAMS
SELECT
  oi.product_id,
  mp.id as machine_program_id,
  mp.machine_id,
  mp.settings,
  mp.features,
  mp.duration_minutes
FROM ORDER_ITEMS oi
JOIN MACHINE_PROGRAMS mp ON oi.product_id = mp.product_id
WHERE oi.id = 'order-item-uuid'
AND mp.machine_id = 'selected_machine_id'  -- Machine user đã chọn
AND mp.is_active = true;

-- Tạo service session sau khi thanh toán thành công
INSERT INTO SERVICE_SESSIONS (
  id: 'session-uuid',
  user_id: 'user-uuid',
  order_id: 'order-uuid',
  order_item_id: 'order-item-uuid',
  machine_id: 'selected_machine_id',
  machine_program_id: 'program-uuid',  -- Từ query trên
  status: 'waiting',
  created_at: NOW()
);

-- Reserve machine
UPDATE WASHING_MACHINES
SET status = 'in_use'
WHERE id = 'selected_machine_id';
```

## 5. USER SỬ DỤNG DỊCH VỤ

```sql
-- User đến và bắt đầu sử dụng
UPDATE SERVICE_SESSIONS
SET
  status = 'in_use',
  started_at = NOW(),
  estimated_end_time = NOW() + INTERVAL '60 minutes'  -- Dựa trên duration_minutes từ program
WHERE id = 'session-uuid';

-- Update machine last used
UPDATE WASHING_MACHINES
SET last_used_at = NOW()
WHERE id = 'selected_machine_id';

-- Push notification khi user bắt đầu sử dụng
INSERT INTO USER_NOTIFICATIONS (
  user_id: 'user-uuid',
  title: 'Service Started',
  content: 'Your delicate wash service has started. Estimated completion: 60 minutes.',
  type: 'notification',
  device_token: 'user_device_token_here',
  sent_at: NOW(),
  created_at: NOW()
);
```

### Background Job - Notification 5 phút trước khi kết thúc

```sql
-- Scheduled job chạy mỗi phút để check sessions sắp kết thúc
SELECT
  ss.id,
  ss.user_id,
  ss.estimated_end_time,
  ud.device_token
FROM SERVICE_SESSIONS ss
JOIN USERS u ON ss.user_id = u.id
JOIN USER_DEVICES ud ON u.id = ud.user_id
WHERE ss.status = 'in_use'
AND ss.estimated_end_time IS NOT NULL
AND ss.estimated_end_time BETWEEN NOW() + INTERVAL '4 minutes' AND NOW() + INTERVAL '6 minutes'  -- 5 phút ±1 phút buffer
AND ud.is_active = true
AND NOT EXISTS (
  -- Tránh gửi duplicate notification
  SELECT 1 FROM USER_NOTIFICATIONS un
  WHERE un.user_id = ss.user_id
  AND un.type = 'notification'
  AND un.content LIKE '%5 minutes remaining%'
  AND un.created_at > ss.started_at
);

-- Push notification 5 phút trước khi kết thúc
INSERT INTO USER_NOTIFICATIONS (
  user_id: 'user-uuid',
  title: 'Service Almost Complete',
  content: 'Your delicate wash service will be complete in 5 minutes. Please prepare to collect your laundry.',
  type: 'notification',
  device_token: 'user_device_token_here',
  sent_at: NOW(),
  created_at: NOW()
);
```

## 6. HOÀN THÀNH DỊCH VỤ

```sql
-- Service hoàn thành
UPDATE SERVICE_SESSIONS
SET
  status = 'done',
  completed_at = NOW()
WHERE id = 'session-uuid';

-- Free up machine
UPDATE WASHING_MACHINES
SET status = 'available'
WHERE id = 'selected_machine_id';

-- Complete order
UPDATE ORDERS
SET status = 'completed'
WHERE id = 'order-uuid';

-- Send notification
INSERT INTO USER_NOTIFICATIONS (
  user_id: 'user-uuid',
  title: 'Service Completed',
  message: 'Your delicate wash service is complete!',
  type: 'success',
  channel: 'push',
  created_at: NOW()
);
```

---

## 📊 DATA FLOW DIAGRAM

```
USER
 ↓ (chọn store/machine)
STORES ← WASHING_MACHINES (với features & options)
 ↓ (chọn chương trình)
MACHINE_PROGRAMS → PRODUCTS (program settings & product pricing)
 ↓ (đặt hàng)
ORDERS → ORDER_ITEMS (chỉ cần product_id)
 ↓ (thanh toán)
PAYMENT_TRANSACTIONS → PAYMENT_TRANSACTION_BALANCES
 ↓ (cập nhật balance)
PAYMENT_USERS (balance updated)
 ↓ (tạo session với program FK)
SERVICE_SESSIONS (machine_program_id FK)
 ↓ (sử dụng)
WASHING_MACHINES (status tracking)
 ↓ (hoàn thành)
USER_NOTIFICATIONS (thông báo)
```

---
