# Environment variables
.env

# Logs
logs
*.log
npm-debug.log*

# Documentation
docs
*.md

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Dependency directories
node_modules
jspm_packages

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Dist folder (code after build)
dist

# Project file
*.sublime-project
*.sublime-workspace

# Git file
.git

# Docker file
*Dockerfile*
*docker-compose*

# Deploy folder and file
deploy
.gitlab-ci.yml
README.md