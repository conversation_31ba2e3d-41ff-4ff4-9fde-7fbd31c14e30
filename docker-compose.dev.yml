volumes:
  local_redis: {}
  local_postgres: {}

services:
  # The API
  api:
    image: backend_laundry-api:latest
    container_name: backend_laundry_api
    ports:
      - '${APP_PORT}:3000'
    entrypoint: /bin/sh ./scripts/start-service.sh api
    env_file:
      - .env
    environment:
      APP_NAME: api
      APP_SERVICE: api
      # APP_LOG_LEVEL: debug  # Uncomment để set log level: error, warn, log, debug, verbose, none
    tty: true
    networks:
      - backend_laundry_network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis

  # The Schedule
  schedule:
    image: backend_laundry-schedule:latest
    container_name: backend_laundry_schedule
    entrypoint: /bin/sh ./scripts/start-service.sh schedule
    env_file:
      - .env
    environment:
      APP_NAME: schedule
      APP_SERVICE: schedule
      # APP_LOG_LEVEL: debug  # Uncomment để set log level: error, warn, log, debug, verbose, none
    tty: true
    networks:
      - backend_laundry_network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis

  # The Database
  postgres:
    container_name: backend_laundry_postgres
    build:
      context: ./
      dockerfile: ./docker/postgres/Dockerfile
    volumes:
      - local_postgres:/var/lib/postgresql/data:Z
      - ./docker/postgres/initdb:/docker-entrypoint-initdb.d/
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    env_file:
      - .env
    ports:
      - '${DB_PORT}:5432'
    tty: true
    networks:
      - backend_laundry_network
    restart: unless-stopped

  redis:
    container_name: backend_laundry_redis
    build:
      context: ./
      dockerfile: ./docker/redis/Dockerfile
    ports:
      - '${REDIS_PORT}:6379'
    volumes:
      - local_redis:/data
    tty: true
    env_file:
      - .env
    networks:
      - backend_laundry_network
    restart: unless-stopped

networks:
  backend_laundry_network:
    name: backend_laundry_network
    driver: bridge
