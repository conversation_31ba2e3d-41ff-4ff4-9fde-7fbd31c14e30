## How to run project

It's easy, just follow these steps:

**Make environment file**

Create a new `.env` file

```sh
cp env.example .env
```

Open this file using `vim` or your text editor of choice:

```sh
vim .env
```

The current `.env` file. We need update `DB_NAME` variable, `DB_USER` variable, `DB_PASSWORD` variable

Save the file when you’re done editing. If you used `vim`, you can do that by pressing `ESC`, then `:wq` and `Enter` to confirm.

**Start developing with**

Step 1: Build docker composer

```sh
./scripts/build.sh
```

Step 2: Install dependencies

Ubuntu + MacOS

```sh
docker run --rm -v $(pwd):/app -w /app --entrypoint="" backend_laundry-api yarn install --ignore-engines
```

Windows

```sh
docker run --rm -v //project_path:/app -w //project_path/app --entrypoint="" backend_laundry_api yarn install --ignore-engines
```

Step 3: Docker composer up

```sh
./scripts/start.sh
```

Step 4: Run migration

```sh
./scripts/run.sh api yarn typeorm migration:run -d dist/apps/api/apps/api/src/datasource.js
```

Go to result API endpoint <http://0.0.0.0:9000/api/v1/docs>

## Useful commands

After changing `Dockerfile`, please run this command:

```sh
docker stop $(docker ps -f "name=backend_laundry" -a -q)
docker rm $(docker ps -f "name=backend_laundry" -a -q)
docker rmi -f $(docker images "backend_laundry*" -a -q)
./scripts/start.sh
```

Generates and/or modifies files based on a schematic <https://docs.nestjs.com/cli/usages#nest-generate>

Create new app:

```sh
./scripts/run.sh api yarn nest:generate app my-app
```

Create new a library:

```sh
./scripts/run.sh api yarn nest:generate library my-library
```

Create new migration:

```sh
./scripts/run.sh api yarn typeorm migration:create apps/api/src/migrations/create-demo-table
```

Run migration:

```sh
./scripts/run.sh api yarn typeorm migration:run -d dist/apps/api/apps/api/src/datasource.js
```

Revert migration:

```sh
./scripts/run.sh api yarn typeorm migration:revert -d dist/apps/api/apps/api/src/datasource.js
```

## Coding convention

Use the <https://docs.nestjs.com/first-steps#linting-and-formatting>

You can now run command line to check coding convention:

```sh
./scripts/run.sh api yarn run lint
```

## Testing

Xem [Testing Guide](./docs/testing-guide.md) để biết chi tiết về cách chạy test trong Docker.

### Quick Test Commands

```bash
# Chạy tất cả E2E tests
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e

# Chạy test cụ thể
docker exec -e NODE_ENV=test backend_laundry_api yarn test:e2e -- --testPathPattern=user-delete.e2e-spec.ts

# Chạy unit tests
docker exec -e NODE_ENV=test backend_laundry_api yarn test

# Chạy test với coverage
docker exec -e NODE_ENV=test backend_laundry_api yarn test:cov
```

## Commitlint

Use the <https://www.conventionalcommits.org/en/v1.0.0/>

## Documents

[Entity-Relationship Diagram](./docs/erd.mmd).

[User Data Flow](./docs/user-data-flow.mmd).

[Admin Data Flow](./docs/admin-data-flow.mmd).
